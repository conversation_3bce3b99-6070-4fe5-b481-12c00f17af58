<?php

namespace Tests\Unit\Application\Orders\Handlers;

use Tests\TestCase;
use App\Application\Orders\Commands\ProcessRefundCommand;
use App\Application\Orders\Handlers\ProcessRefundHandler;
use App\Application\Orders\DTOs\OrderDTO;
use App\Domain\Orders\Entities\Order;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\Exceptions\OrderNotFoundException;
use App\Domain\Orders\Exceptions\InvalidRefundException;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Domain\Shared\Events\DomainEventDispatcher;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use Mockery;

class ProcessRefundHandlerTest extends TestCase
{
    private OrderRepositoryInterface $orderRepository;
    private DomainEventDispatcher $eventDispatcher;
    private ProcessRefundHandler $handler;

    protected function setUp(): void
    {
        parent::setUp();

        $this->orderRepository = Mockery::mock(OrderRepositoryInterface::class);
        $this->eventDispatcher = Mockery::mock(DomainEventDispatcher::class);
        $this->handler = new ProcessRefundHandler(
            $this->orderRepository,
            $this->eventDispatcher
        );
    }

    public function test_it_can_process_full_refund(): void
    {
        // Arrange
        $orderId = 1;
        $refundAmount = 100.0;
        $reason = "customer_request";

        $command = new ProcessRefundCommand(
            orderId: $orderId,
            refundAmount: $refundAmount,
            reason: $reason,
            isPartialRefund: false
        );

        // Gerçek Order entity oluştur
        $order = Order::create(
            userId: 123,
            orderNumber: new OrderNumber('ORD-123'),
            totalAmount: new Money($refundAmount),
            paymentMethod: 'credit_card',
            shippingMethod: 'standard'
        );
        $order->setId($orderId);
        $order->setStatus(OrderStatus::DELIVERED);

        $this->orderRepository
            ->shouldReceive('findById')
            ->with($orderId)
            ->once()
            ->andReturn($order);

        $this->orderRepository
            ->shouldReceive('save')
            ->with($order)
            ->once()
            ->andReturn($order);

        $this->eventDispatcher
            ->shouldReceive('dispatchEventsFor')
            ->with($order)
            ->once();

        // Act
        $result = $this->handler->handle($command);

        // Assert
        $this->assertInstanceOf(OrderDTO::class, $result);
    }

    public function test_it_can_process_partial_refund(): void
    {
        // Arrange
        $orderId = 1;
        $totalAmount = 200.0;
        $refundAmount = 50.0;
        $reason = "product_defect";

        $command = new ProcessRefundCommand(
            orderId: $orderId,
            refundAmount: $refundAmount,
            reason: $reason,
            isPartialRefund: true
        );

        // Gerçek Order entity oluştur
        $order = Order::create(
            userId: 123,
            orderNumber: new OrderNumber('ORD-456'),
            totalAmount: new Money($totalAmount),
            paymentMethod: 'credit_card',
            shippingMethod: 'standard'
        );
        $order->setId($orderId);
        $order->setStatus(OrderStatus::DELIVERED);

        $this->orderRepository
            ->shouldReceive('findById')
            ->with($orderId)
            ->once()
            ->andReturn($order);

        $this->orderRepository
            ->shouldReceive('save')
            ->with($order)
            ->once()
            ->andReturn($order);

        $this->eventDispatcher
            ->shouldReceive('dispatchEventsFor')
            ->with($order)
            ->once();

        // Act
        $result = $this->handler->handle($command);

        // Assert
        $this->assertInstanceOf(OrderDTO::class, $result);
    }

    public function test_it_throws_exception_when_order_not_found(): void
    {
        // Arrange
        $orderId = 999;
        $command = new ProcessRefundCommand(
            orderId: $orderId,
            refundAmount: 100.0,
            reason: "test"
        );

        $this->orderRepository
            ->shouldReceive('findById')
            ->with($orderId)
            ->once()
            ->andReturn(null);

        // Assert
        $this->expectException(OrderNotFoundException::class);

        // Act
        $this->handler->handle($command);
    }

    public function test_it_throws_exception_for_invalid_refund_amount(): void
    {
        // Arrange
        $orderId = 1;
        $totalAmount = 100.0;
        $refundAmount = 150.0; // More than total

        $command = new ProcessRefundCommand(
            orderId: $orderId,
            refundAmount: $refundAmount,
            reason: "test"
        );

        // Gerçek Order entity oluştur
        $order = Order::create(
            userId: 123,
            orderNumber: new OrderNumber('ORD-789'),
            totalAmount: new Money($totalAmount),
            paymentMethod: 'credit_card',
            shippingMethod: 'standard'
        );
        $order->setId($orderId);
        $order->setStatus(OrderStatus::DELIVERED);

        $this->orderRepository
            ->shouldReceive('findById')
            ->with($orderId)
            ->once()
            ->andReturn($order);

        // Assert
        $this->expectException(InvalidRefundException::class);
        $this->expectExceptionMessage("Refund amount cannot exceed order total");

        // Act
        $this->handler->handle($command);
    }

    public function test_it_throws_exception_for_invalid_order_status(): void
    {
        // Arrange
        $orderId = 1;
        $refundAmount = 100.0;

        $command = new ProcessRefundCommand(
            orderId: $orderId,
            refundAmount: $refundAmount,
            reason: "test"
        );

        // Gerçek Order entity oluştur (PENDING status - invalid for refund)
        $order = Order::create(
            userId: 123,
            orderNumber: new OrderNumber('ORD-999'),
            totalAmount: new Money($refundAmount),
            paymentMethod: 'credit_card',
            shippingMethod: 'standard'
        );
        $order->setId($orderId);
        // PENDING status zaten default, değiştirmiyoruz

        $this->orderRepository
            ->shouldReceive('findById')
            ->with($orderId)
            ->once()
            ->andReturn($order);

        // Assert
        $this->expectException(InvalidRefundException::class);
        $this->expectExceptionMessage("Order status does not allow refunds");

        // Act
        $this->handler->handle($command);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
