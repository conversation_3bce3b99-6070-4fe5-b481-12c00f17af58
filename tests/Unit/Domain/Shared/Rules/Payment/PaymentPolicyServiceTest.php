<?php

namespace Tests\Unit\Domain\Shared\Rules\Payment;

use Tests\TestCase;
use App\Domain\Shared\Rules\Payment\PaymentPolicyService;
use App\Domain\Shared\Rules\Payment\PaymentValidationRule;
use App\Domain\Shared\Rules\Payment\RefundRule;
use App\Domain\Shared\Rules\Payment\PaymentMethodRule;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Customers\Entities\Customer;

class PaymentPolicyServiceTest extends TestCase
{
    private PaymentPolicyService $service;
    private Order $testOrder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new PaymentPolicyService();
        $this->testOrder = $this->createTestOrder();
    }

    public function test_can_validate_payment()
    {
        $amount = Money::fromAmount(100, 'TRY');
        $method = 'credit_card';
        
        $result = $this->service->validatePayment($this->testOrder, $amount, $method);
        
        $this->assertNotNull($result);
        $this->assertTrue($result->isValid());
        $this->assertEquals($this->testOrder, $result->getOrder());
        $this->assertEquals($amount, $result->getAmount());
        $this->assertEquals($method, $result->getMethod());
    }

    public function test_can_validate_refund()
    {
        $amount = Money::fromAmount(50, 'TRY');
        $reason = 'Customer request';
        
        $result = $this->service->validateRefund($this->testOrder, $amount, $reason);
        
        $this->assertNotNull($result);
        $this->assertEquals($this->testOrder, $result->getOrder());
        $this->assertEquals($amount, $result->getAmount());
        $this->assertEquals($reason, $result->getReason());
    }

    public function test_can_register_and_unregister_rules()
    {
        $customRule = new PaymentValidationRule(
            Money::fromAmount(50, 'TRY'),
            ['credit_card', 'bank_transfer']
        );
        
        $this->service->registerRule($customRule);
        $rules = $this->service->getRules();
        $this->assertCount(4, $rules); // 3 standart + 1 custom
        
        $this->service->unregisterRule('payment_validation');
        $rules = $this->service->getRules();
        $this->assertCount(3, $rules);
    }

    public function test_can_get_rules_by_priority()
    {
        $rules = $this->service->getRulesByPriority();
        
        $this->assertIsArray($rules);
        $this->assertCount(3, $rules);
        
        // Öncelik sırasını kontrol et
        $priorities = array_map(function ($rule) {
            return $rule->getPriority();
        }, $rules);
        
        $sortedPriorities = $priorities;
        rsort($sortedPriorities);
        
        $this->assertEquals($sortedPriorities, $priorities);
    }

    public function test_can_get_specific_rule()
    {
        $rule = $this->service->getRule('payment_validation');
        
        $this->assertNotNull($rule);
        $this->assertInstanceOf(PaymentValidationRule::class, $rule);
        $this->assertEquals('payment_validation', $rule->getName());
    }

    public function test_can_check_rule_existence()
    {
        $this->assertTrue($this->service->hasRule('payment_validation'));
        $this->assertTrue($this->service->hasRule('refund'));
        $this->assertTrue($this->service->hasRule('payment_method'));
        $this->assertFalse($this->service->hasRule('non_existent_rule'));
    }

    public function test_can_clear_and_reload_rules()
    {
        $this->assertCount(3, $this->service->getRules());
        
        $this->service->clearRules();
        $this->assertCount(0, $this->service->getRules());
        
        $this->service->reloadStandardRules();
        $this->assertCount(3, $this->service->getRules());
    }

    public function test_validates_payment_with_minimum_amount()
    {
        $smallAmount = Money::fromAmount(0.5, 'TRY'); // Minimum altında
        $method = 'credit_card';
        
        $result = $this->service->validatePayment($this->testOrder, $smallAmount, $method);
        
        $this->assertFalse($result->isValid());
        $this->assertNotEmpty($result->getErrors());
    }

    public function test_validates_payment_with_invalid_method()
    {
        $amount = Money::fromAmount(100, 'TRY');
        $invalidMethod = 'invalid_method';
        
        $result = $this->service->validatePayment($this->testOrder, $amount, $invalidMethod);
        
        $this->assertFalse($result->isValid());
        $this->assertNotEmpty($result->getErrors());
    }

    public function test_validates_refund_with_conditions()
    {
        // Sipariş teslim edilmemiş - iade edilemez
        $this->testOrder->setStatus('pending');
        
        $amount = Money::fromAmount(50, 'TRY');
        $reason = 'Customer request';
        
        $result = $this->service->validateRefund($this->testOrder, $amount, $reason);
        
        $this->assertFalse($result->isValid());
        $this->assertNotEmpty($result->getErrors());
    }

    public function test_security_check_with_high_amount()
    {
        $highAmount = Money::fromAmount(15000, 'TRY');
        
        $securityCheck = $this->service->performSecurityCheck($this->testOrder, [
            'amount' => $highAmount
        ]);
        
        $this->assertIsArray($securityCheck);
        $this->assertArrayHasKey('risk_level', $securityCheck);
        $this->assertEquals('high', $securityCheck['risk_level']);
        $this->assertContains('High amount transaction', $securityCheck['issues']);
    }

    public function test_security_check_with_international_payment()
    {
        $amount = Money::fromAmount(500, 'TRY');
        
        $securityCheck = $this->service->performSecurityCheck($this->testOrder, [
            'amount' => $amount,
            'international' => true
        ]);
        
        $this->assertIsArray($securityCheck);
        $this->assertEquals('medium', $securityCheck['risk_level']);
        $this->assertContains('International payment', $securityCheck['issues']);
    }

    public function test_security_check_with_rapid_transactions()
    {
        $amount = Money::fromAmount(500, 'TRY');
        
        $securityCheck = $this->service->performSecurityCheck($this->testOrder, [
            'amount' => $amount,
            'rapid_transactions' => true
        ]);
        
        $this->assertIsArray($securityCheck);
        $this->assertEquals('high', $securityCheck['risk_level']);
        $this->assertContains('Rapid successive transactions detected', $securityCheck['issues']);
    }

    private function createTestOrder(): Order
    {
        $customer = new Customer();
        $customer->setId(1);
        $customer->setName('Test Customer');
        $customer->setEmail('<EMAIL>');
        $customer->setEmailVerified(true);
        $customer->setPhoneVerified(true);

        $order = new Order();
        $order->setId(1);
        $order->setCustomer($customer);
        $order->setStatus('confirmed');
        $order->setTotalAmount(Money::fromAmount(200, 'TRY'));
        $order->setPaymentMethod('credit_card');
        $order->setPaid(true);
        $order->setCreatedAt(now()->subHours(2));
        $order->setDeliveredAt(now()->subDays(1));

        return $order;
    }
}
