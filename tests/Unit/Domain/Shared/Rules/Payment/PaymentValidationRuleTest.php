<?php

namespace Tests\Unit\Domain\Shared\Rules\Payment;

use Tests\TestCase;
use App\Domain\Shared\Rules\Payment\PaymentValidationRule;
use App\Domain\Shared\Rules\Payment\PaymentRuleResult;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Customers\Entities\Customer;

class PaymentValidationRuleTest extends TestCase
{
    private PaymentValidationRule $rule;
    private Order $testOrder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rule = new PaymentValidationRule(
            Money::fromAmount(10, 'TRY'),
            ['credit_card', 'bank_transfer', 'cash']
        );
        $this->testOrder = $this->createTestOrder();
    }

    public function test_allows_valid_payment()
    {
        $result = $this->rule->applyRule($this->testOrder, [
            'amount' => Money::fromAmount(100, 'TRY'),
            'method' => 'credit_card'
        ]);

        $this->assertInstanceOf(PaymentRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isPaymentAllowed());
        $this->assertEquals('credit_card', $result->getPaymentMethod());
    }

    public function test_denies_payment_below_minimum()
    {
        $result = $this->rule->applyRule($this->testOrder, [
            'amount' => Money::fromAmount(5, 'TRY'),
            'method' => 'credit_card'
        ]);

        $this->assertInstanceOf(PaymentRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isPaymentAllowed());
        $this->assertNotEmpty($result->getErrors());
        $this->assertStringContainsString('below minimum', $result->getErrors()[0]);
    }

    public function test_denies_invalid_payment_method()
    {
        $result = $this->rule->applyRule($this->testOrder, [
            'amount' => Money::fromAmount(100, 'TRY'),
            'method' => 'invalid_method'
        ]);

        $this->assertInstanceOf(PaymentRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isPaymentAllowed());
        $this->assertNotEmpty($result->getErrors());
        $this->assertStringContainsString('not supported', $result->getErrors()[0]);
    }

    public function test_denies_payment_for_invalid_order_status()
    {
        $this->testOrder->setStatus('cancelled');

        $result = $this->rule->applyRule($this->testOrder, [
            'amount' => Money::fromAmount(100, 'TRY'),
            'method' => 'credit_card'
        ]);

        $this->assertInstanceOf(PaymentRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isPaymentAllowed());
        $this->assertNotEmpty($result->getErrors());
        $this->assertStringContainsString('status', $result->getErrors()[0]);
    }

    public function test_requires_verification_for_high_amount()
    {
        $result = $this->rule->applyRule($this->testOrder, [
            'amount' => Money::fromAmount(5000, 'TRY'),
            'method' => 'credit_card'
        ]);

        $this->assertInstanceOf(PaymentRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isPaymentAllowed());
        $this->assertNotEmpty($result->getWarnings());
        
        $metadata = $result->getMetadata();
        $this->assertTrue($metadata['verification_required']);
        $this->assertContains('identity_verification', $metadata['required_verifications']);
    }

    public function test_requires_additional_verification_for_very_high_amount()
    {
        $result = $this->rule->applyRule($this->testOrder, [
            'amount' => Money::fromAmount(15000, 'TRY'),
            'method' => 'credit_card'
        ]);

        $this->assertInstanceOf(PaymentRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isPaymentAllowed());
        $this->assertNotEmpty($result->getWarnings());
        
        $metadata = $result->getMetadata();
        $this->assertTrue($metadata['verification_required']);
        $this->assertContains('identity_verification', $metadata['required_verifications']);
        $this->assertContains('phone_verification', $metadata['required_verifications']);
    }

    public function test_denies_non_order_entity()
    {
        $customer = new Customer();
        
        $result = $this->rule->applyRule($customer, [
            'amount' => Money::fromAmount(100, 'TRY'),
            'method' => 'credit_card'
        ]);

        $this->assertInstanceOf(PaymentRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isPaymentAllowed());
        $this->assertStringContainsString('not an order', $result->getErrors()[0]);
    }

    public function test_is_applicable_to_orders_only()
    {
        $this->assertTrue($this->rule->isApplicable($this->testOrder));
        $this->assertFalse($this->rule->isApplicable(new Customer()));
    }

    public function test_rule_properties()
    {
        $this->assertEquals('payment_validation', $this->rule->getName());
        $this->assertEquals(100, $this->rule->getPriority());
        $this->assertStringContainsString('payment amount', $this->rule->getDescription());
    }

    public function test_can_set_minimum_amount()
    {
        $newMinimum = Money::fromAmount(50, 'TRY');
        $this->rule->setMinimumPaymentAmount($newMinimum);

        $result = $this->rule->applyRule($this->testOrder, [
            'amount' => Money::fromAmount(25, 'TRY'),
            'method' => 'credit_card'
        ]);

        $this->assertFalse($result->isValid());
        $this->assertStringContainsString('50', $result->getErrors()[0]);
    }

    public function test_can_set_allowed_methods()
    {
        $this->rule->setAllowedMethods(['bank_transfer']);

        $result = $this->rule->applyRule($this->testOrder, [
            'amount' => Money::fromAmount(100, 'TRY'),
            'method' => 'credit_card'
        ]);

        $this->assertFalse($result->isValid());
        $this->assertStringContainsString('not supported', $result->getErrors()[0]);
    }

    public function test_standard_rule_creation()
    {
        $standardRule = PaymentValidationRule::standard();
        
        $this->assertInstanceOf(PaymentValidationRule::class, $standardRule);
        $this->assertEquals('payment_validation', $standardRule->getName());
        $this->assertEquals(100, $standardRule->getPriority());
    }

    public function test_strict_rule_creation()
    {
        $strictRule = PaymentValidationRule::strict();
        
        $this->assertInstanceOf(PaymentValidationRule::class, $strictRule);
        $this->assertEquals('payment_validation', $strictRule->getName());
        $this->assertEquals(150, $strictRule->getPriority());
    }

    private function createTestOrder(): Order
    {
        $customer = new Customer();
        $customer->setId(1);
        $customer->setName('Test Customer');
        $customer->setEmail('<EMAIL>');

        $order = new Order();
        $order->setId(1);
        $order->setCustomer($customer);
        $order->setStatus('confirmed');
        $order->setTotalAmount(Money::fromAmount(200, 'TRY'));
        $order->setCreatedAt(now());

        return $order;
    }
}
