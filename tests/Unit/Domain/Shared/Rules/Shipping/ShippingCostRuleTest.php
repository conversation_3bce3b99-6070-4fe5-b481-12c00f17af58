<?php

namespace Tests\Unit\Domain\Shared\Rules\Shipping;

use Tests\TestCase;
use App\Domain\Shared\Rules\Shipping\ShippingCostRule;
use App\Domain\Shared\Rules\Shipping\ShippingRuleResult;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Domain\Orders\ValueObjects\Address;
use App\Domain\Orders\Entities\OrderItem;
use App\Domain\Customers\Entities\Customer;
use App\Domain\Products\Entities\Product;

class ShippingCostRuleTest extends TestCase
{
    private Order $testOrder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->testOrder = $this->createTestOrder();
    }

    public function test_free_shipping_rule_without_minimum()
    {
        $rule = ShippingCostRule::free();

        $result = $rule->applyRule($this->testOrder, [
            'order_amount' => Money::fromAmount(100, 'TRY')
        ]);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isShippingAllowed());
        $this->assertTrue($result->isFreeShipping());
        $this->assertEquals(0, $result->getShippingCost()->getAmountInMajorUnit());
        $this->assertEquals(3, $result->getEstimatedDays());
        $this->assertContains('standard', $result->getAvailableMethods());
        $this->assertContains('express', $result->getAvailableMethods());
    }

    public function test_free_shipping_rule_with_minimum_amount_met()
    {
        $minimumAmount = Money::fromAmount(200, 'TRY');
        $rule = ShippingCostRule::free($minimumAmount);

        $result = $rule->applyRule($this->testOrder, [
            'order_amount' => Money::fromAmount(300, 'TRY')
        ]);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isShippingAllowed());
        $this->assertTrue($result->isFreeShipping());
        $this->assertEquals(0, $result->getShippingCost()->getAmountInMajorUnit());
    }

    public function test_free_shipping_rule_with_minimum_amount_not_met()
    {
        $minimumAmount = Money::fromAmount(200, 'TRY');
        $rule = ShippingCostRule::free($minimumAmount);

        $result = $rule->applyRule($this->testOrder, [
            'order_amount' => Money::fromAmount(100, 'TRY')
        ]);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isShippingAllowed());
        $this->assertTrue($result->hasRestrictions());
        $this->assertNotEmpty($result->getRestrictions());
        $this->assertStringContainsString('Minimum order amount required', $result->getRestrictions()[0]);
    }

    public function test_fixed_shipping_cost_rule()
    {
        $fixedCost = Money::fromAmount(25, 'TRY');
        $rule = ShippingCostRule::fixed($fixedCost);

        $result = $rule->applyRule($this->testOrder, []);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isShippingAllowed());
        $this->assertFalse($result->isFreeShipping());
        $this->assertEquals(25, $result->getShippingCost()->getAmountInMajorUnit());
        $this->assertEquals(3, $result->getEstimatedDays());
        $this->assertContains('standard', $result->getAvailableMethods());
        $this->assertStringContainsString('Fixed shipping cost', $result->getReason());
    }

    public function test_weight_based_shipping_rule()
    {
        $weightTiers = [
            [
                'min_weight' => 0,
                'max_weight' => 1,
                'cost' => Money::fromAmount(10, 'TRY'),
                'estimated_days' => 2
            ],
            [
                'min_weight' => 1,
                'max_weight' => 5,
                'cost' => Money::fromAmount(20, 'TRY'),
                'estimated_days' => 3
            ],
            [
                'min_weight' => 5,
                'max_weight' => null, // Sınırsız
                'cost' => Money::fromAmount(35, 'TRY'),
                'estimated_days' => 4
            ]
        ];

        $rule = ShippingCostRule::weightBased($weightTiers);

        // 0.5kg için test
        $result = $rule->applyRule($this->testOrder, [
            'weight' => 0.5
        ]);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isShippingAllowed());
        $this->assertEquals(10, $result->getShippingCost()->getAmountInMajorUnit());
        $this->assertEquals(2, $result->getEstimatedDays());
        $this->assertStringContainsString('Weight-based shipping: 0.5kg', $result->getReason());
    }

    public function test_weight_based_shipping_rule_second_tier()
    {
        $weightTiers = [
            [
                'min_weight' => 0,
                'max_weight' => 1,
                'cost' => Money::fromAmount(10, 'TRY'),
                'estimated_days' => 2
            ],
            [
                'min_weight' => 1,
                'max_weight' => 5,
                'cost' => Money::fromAmount(20, 'TRY'),
                'estimated_days' => 3
            ]
        ];

        $rule = ShippingCostRule::weightBased($weightTiers);

        // 3kg için test (ikinci tier)
        $result = $rule->applyRule($this->testOrder, [
            'weight' => 3
        ]);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isShippingAllowed());
        $this->assertEquals(20, $result->getShippingCost()->getAmountInMajorUnit());
        $this->assertEquals(3, $result->getEstimatedDays());
    }

    public function test_weight_based_shipping_rule_without_weight()
    {
        $weightTiers = [
            [
                'min_weight' => 0,
                'max_weight' => 1,
                'cost' => Money::fromAmount(10, 'TRY'),
                'estimated_days' => 2
            ]
        ];

        $rule = ShippingCostRule::weightBased($weightTiers);

        $result = $rule->applyRule($this->testOrder, []);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isShippingAllowed());
        $this->assertTrue($result->hasRestrictions());
        $this->assertStringContainsString('Weight information required', $result->getRestrictions()[0]);
    }

    public function test_weight_based_shipping_rule_no_applicable_tier()
    {
        $weightTiers = [
            [
                'min_weight' => 0,
                'max_weight' => 1,
                'cost' => Money::fromAmount(10, 'TRY'),
                'estimated_days' => 2
            ]
        ];

        $rule = ShippingCostRule::weightBased($weightTiers);

        // 10kg - tier'ların dışında
        $result = $rule->applyRule($this->testOrder, [
            'weight' => 10
        ]);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isShippingAllowed());
        $this->assertTrue($result->hasRestrictions());
        $this->assertStringContainsString('No shipping available for this weight', $result->getRestrictions()[0]);
    }

    public function test_amount_based_shipping_rule()
    {
        $amountTiers = [
            [
                'min_amount' => Money::fromAmount(0, 'TRY'),
                'max_amount' => Money::fromAmount(100, 'TRY'),
                'cost' => Money::fromAmount(15, 'TRY'),
                'estimated_days' => 3
            ],
            [
                'min_amount' => Money::fromAmount(100, 'TRY'),
                'max_amount' => Money::fromAmount(500, 'TRY'),
                'cost' => Money::fromAmount(10, 'TRY'),
                'estimated_days' => 2
            ],
            [
                'min_amount' => Money::fromAmount(500, 'TRY'),
                'max_amount' => null, // Sınırsız
                'cost' => Money::fromAmount(0, 'TRY'), // Ücretsiz
                'estimated_days' => 1
            ]
        ];

        $rule = ShippingCostRule::amountBased($amountTiers);

        // 50 TRY için test (ilk tier)
        $result = $rule->applyRule($this->testOrder, [
            'order_amount' => Money::fromAmount(50, 'TRY')
        ]);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isShippingAllowed());
        $this->assertEquals(15, $result->getShippingCost()->getAmountInMajorUnit());
        $this->assertEquals(3, $result->getEstimatedDays());
        $this->assertStringContainsString('Amount-based shipping: 50', $result->getReason());
    }

    public function test_amount_based_shipping_rule_second_tier()
    {
        $amountTiers = [
            [
                'min_amount' => Money::fromAmount(0, 'TRY'),
                'max_amount' => Money::fromAmount(100, 'TRY'),
                'cost' => Money::fromAmount(15, 'TRY'),
                'estimated_days' => 3
            ],
            [
                'min_amount' => Money::fromAmount(100, 'TRY'),
                'max_amount' => Money::fromAmount(500, 'TRY'),
                'cost' => Money::fromAmount(10, 'TRY'),
                'estimated_days' => 2
            ]
        ];

        $rule = ShippingCostRule::amountBased($amountTiers);

        // 300 TRY için test (ikinci tier)
        $result = $rule->applyRule($this->testOrder, [
            'order_amount' => Money::fromAmount(300, 'TRY')
        ]);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isShippingAllowed());
        $this->assertEquals(10, $result->getShippingCost()->getAmountInMajorUnit());
        $this->assertEquals(2, $result->getEstimatedDays());
    }

    public function test_amount_based_shipping_rule_without_order_amount()
    {
        $amountTiers = [
            [
                'min_amount' => Money::fromAmount(0, 'TRY'),
                'max_amount' => Money::fromAmount(100, 'TRY'),
                'cost' => Money::fromAmount(15, 'TRY'),
                'estimated_days' => 3
            ]
        ];

        $rule = ShippingCostRule::amountBased($amountTiers);

        $result = $rule->applyRule($this->testOrder, []);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isShippingAllowed());
        $this->assertTrue($result->hasRestrictions());
        $this->assertStringContainsString('Order amount required', $result->getRestrictions()[0]);
    }

    public function test_amount_based_shipping_rule_no_applicable_tier_uses_base_cost()
    {
        $amountTiers = [
            [
                'min_amount' => Money::fromAmount(0, 'TRY'),
                'max_amount' => Money::fromAmount(100, 'TRY'),
                'cost' => Money::fromAmount(15, 'TRY'),
                'estimated_days' => 3
            ]
        ];

        $rule = ShippingCostRule::amountBased($amountTiers);

        // 1000 TRY - tier'ların dışında, base cost kullanılmalı
        $result = $rule->applyRule($this->testOrder, [
            'order_amount' => Money::fromAmount(1000, 'TRY')
        ]);

        $this->assertInstanceOf(ShippingRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isShippingAllowed());
        $this->assertEquals(15, $result->getShippingCost()->getAmountInMajorUnit()); // Base cost
        $this->assertEquals(3, $result->getEstimatedDays()); // Default estimated days
    }

    public function test_unknown_rule_type_returns_error()
    {
        // Invalid rule type ile rule oluşturmaya çalış
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid shipping cost rule type');

        new ShippingCostRule([
            'type' => 'invalid_type'
        ]);
    }

    public function test_rule_properties()
    {
        $rule = ShippingCostRule::fixed(Money::fromAmount(25, 'TRY'));

        $this->assertEquals('fixed_shipping', $rule->getName());
        $this->assertEquals('Fixed shipping cost rule', $rule->getDescription());
        $this->assertEquals(100, $rule->getPriority());
        $this->assertEquals('fixed', $rule->getRuleType());
        $this->assertEquals('fixed', $rule->getCostType());
    }

    public function test_weight_based_rule_properties()
    {
        $rule = ShippingCostRule::weightBased([]);

        $this->assertEquals('weight_based_shipping', $rule->getName());
        $this->assertEquals('Weight-based shipping cost calculation', $rule->getDescription());
        $this->assertEquals('weight', $rule->getRuleType());
    }

    public function test_amount_based_rule_properties()
    {
        $rule = ShippingCostRule::amountBased([]);

        $this->assertEquals('amount_based_shipping', $rule->getName());
        $this->assertEquals('Amount-based shipping cost calculation', $rule->getDescription());
        $this->assertEquals('amount', $rule->getRuleType());
    }

    public function test_free_shipping_rule_properties()
    {
        $rule = ShippingCostRule::free();

        $this->assertEquals('free_shipping', $rule->getName());
        $this->assertEquals('Free shipping rule', $rule->getDescription());
        $this->assertEquals(50, $rule->getPriority()); // Free shipping has lower priority
        $this->assertEquals('free', $rule->getRuleType());
    }

    public function test_get_cost_rules()
    {
        $costRules = [
            'type' => 'fixed',
            'cost' => Money::fromAmount(25, 'TRY')
        ];

        $rule = ShippingCostRule::fixed(Money::fromAmount(25, 'TRY'));
        $retrievedRules = $rule->getCostRules();

        $this->assertEquals('fixed', $retrievedRules['type']);
        $this->assertInstanceOf(Money::class, $retrievedRules['cost']);
        $this->assertEquals(25, $retrievedRules['cost']->getAmountInMajorUnit());
    }

    public function test_custom_priority()
    {
        $rule = ShippingCostRule::fixed(Money::fromAmount(25, 'TRY'), 200);

        $this->assertEquals(200, $rule->getPriority());
    }

    public function test_check_applicability()
    {
        $rule = ShippingCostRule::fixed(Money::fromAmount(25, 'TRY'));

        $this->assertTrue($rule->isApplicable($this->testOrder, []));
    }

    private function createTestOrder(): Order
    {
        $customer = new Customer();
        $customer->setId(1);
        $customer->setName('Test Customer');
        $customer->setEmail('<EMAIL>');

        $product = new Product();
        $product->setId(1);
        $product->setName('Test Product');
        $product->setWeight(1.5); // 1.5kg

        $orderItem = new OrderItem();
        $orderItem->setProduct($product);
        $orderItem->setQuantity(2);
        $orderItem->setUnitPrice(Money::fromAmount(50, 'TRY'));

        $order = new Order();
        $order->setId(1);
        $order->setOrderNumber(new OrderNumber('ORD-2024-001'));
        $order->setCustomer($customer);
        $order->setStatus('pending');
        $order->setTotalAmount(Money::fromAmount(100, 'TRY'));
        $order->addItem($orderItem);

        $shippingAddress = new Address(
            'Test Street 123',
            'Test City',
            'Test State',
            '12345',
            'Turkey'
        );
        $order->setShippingAddress($shippingAddress);

        return $order;
    }
}
