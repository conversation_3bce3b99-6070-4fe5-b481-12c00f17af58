<?php

namespace Tests\Unit\Domain\Shared\Rules\Order;

use Tests\TestCase;
use App\Domain\Shared\Rules\Order\OrderStatusTransitionRule;
use App\Domain\Shared\Rules\Order\OrderRuleResult;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Domain\Orders\ValueObjects\Address;
use App\Domain\Orders\Entities\OrderItem;
use App\Enums\OrderStatus;
use App\Domain\Customers\Entities\Customer;
use App\Domain\Products\Entities\Product;
use Carbon\Carbon;

class OrderStatusTransitionRuleTest extends TestCase
{
    private OrderStatusTransitionRule $rule;
    private Order $testOrder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rule = OrderStatusTransitionRule::standard();
        $this->testOrder = $this->createTestOrder();
    }

    public function test_allows_valid_transition_pending_to_confirmed()
    {
        $this->testOrder->setStatus('pending');
        $this->testOrder->markAsPaid();

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'confirmed'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('confirmed', $result->getNewStatus());
        $this->assertEmpty($result->getErrors());
    }

    public function test_allows_valid_transition_confirmed_to_processing()
    {
        $this->testOrder->setStatus('confirmed');
        $this->testOrder->markAsPaid();

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'processing'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('processing', $result->getNewStatus());
    }

    public function test_allows_valid_transition_processing_to_shipped()
    {
        $this->testOrder->setStatus('processing');
        $this->testOrder->markAsPaid();

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'shipped'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('shipped', $result->getNewStatus());
    }

    public function test_allows_valid_transition_shipped_to_delivered()
    {
        $this->testOrder->setStatus('shipped');

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'delivered'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('delivered', $result->getNewStatus());
    }

    public function test_allows_valid_transition_delivered_to_completed()
    {
        $this->testOrder->setStatus('delivered');

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'completed'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('completed', $result->getNewStatus());
    }

    public function test_allows_cancellation_from_pending()
    {
        $this->testOrder->setStatus('pending');

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'cancelled'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('cancelled', $result->getNewStatus());
    }

    public function test_allows_cancellation_from_confirmed()
    {
        $this->testOrder->setStatus('confirmed');

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'cancelled'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('cancelled', $result->getNewStatus());
    }

    public function test_denies_cancellation_from_shipped()
    {
        $this->testOrder->setStatus('shipped');

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'cancelled'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertNotEmpty($result->getErrors());
        $this->assertStringContainsString('Cannot cancel order that has been shipped', $result->getErrors()[0]);
    }

    public function test_denies_cancellation_from_delivered()
    {
        $this->testOrder->setStatus('delivered');

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'cancelled'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('Cannot cancel order that has been shipped or delivered', $result->getErrors()[0]);
    }

    public function test_denies_confirmation_without_payment()
    {
        $this->testOrder->setStatus('pending');
        // Ödeme yapılmamış

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'confirmed'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('Order must be paid before confirmation', $result->getErrors()[0]);
    }

    public function test_denies_processing_without_stock()
    {
        $this->testOrder->setStatus('confirmed');
        $this->testOrder->markAsPaid();
        
        // Stok yetersiz yap
        foreach ($this->testOrder->getItems() as $item) {
            $product = $item->getProduct();
            $product->setStockQuantity(0); // Stok sıfır
        }

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'processing'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('Insufficient stock to process order', $result->getErrors()[0]);
    }

    public function test_denies_shipping_without_address()
    {
        $this->testOrder->setStatus('processing');
        $this->testOrder->setShippingAddress(null); // Adres yok

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'shipped'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('Shipping address required for shipment', $result->getErrors()[0]);
    }

    public function test_allows_refund_from_delivered()
    {
        $this->testOrder->setStatus('delivered');
        $this->testOrder->setDeliveredAt(Carbon::now()->subDays(10)); // 10 gün önce teslim edilmiş

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'refunded'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('refunded', $result->getNewStatus());
    }

    public function test_denies_refund_from_non_delivered()
    {
        $this->testOrder->setStatus('shipped');

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'refunded'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('Order must be delivered before refund', $result->getErrors()[0]);
    }

    public function test_denies_refund_after_period_expired()
    {
        $this->testOrder->setStatus('delivered');
        $this->testOrder->setDeliveredAt(Carbon::now()->subDays(35)); // 35 gün önce (30 gün sınırı aşıldı)

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'refunded'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('Refund period has expired', $result->getErrors()[0]);
    }

    public function test_allows_same_status_transition()
    {
        $this->testOrder->setStatus('pending');

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'pending'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('pending', $result->getNewStatus());
        $this->assertStringContainsString('No status change required', $result->getReason());
    }

    public function test_denies_transition_without_target_status()
    {
        $this->testOrder->setStatus('pending');

        $result = $this->rule->applyRule($this->testOrder, []);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('Target status is required', $result->getErrors()[0]);
    }

    public function test_denies_transition_for_non_order_entity()
    {
        $nonOrder = new \stdClass();

        $result = $this->rule->applyRule($nonOrder, [
            'target_status' => 'confirmed'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('Entity is not an order', $result->getErrors()[0]);
    }

    public function test_requires_approval_for_specific_transitions()
    {
        $strictRule = OrderStatusTransitionRule::strict();
        $this->testOrder->setStatus('processing');

        $result = $strictRule->applyRule($this->testOrder, [
            'target_status' => 'cancelled'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->isOrderAllowed()); // Requires action
        $this->assertContains('manager_approval', $result->getRequiredActions());
        $this->assertNotEmpty($result->getWarnings());
    }

    public function test_allows_transition_with_approval()
    {
        $strictRule = OrderStatusTransitionRule::strict();
        $this->testOrder->setStatus('processing');

        $result = $strictRule->applyRule($this->testOrder, [
            'target_status' => 'cancelled',
            'approved' => true
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('cancelled', $result->getNewStatus());
    }

    public function test_rule_properties()
    {
        $this->assertEquals('order_status_transition', $this->rule->getName());
        $this->assertEquals(200, $this->rule->getPriority());
        $this->assertStringContainsString('status transitions', $this->rule->getDescription());
    }

    public function test_is_applicable_with_target_status()
    {
        $this->assertTrue($this->rule->isApplicable($this->testOrder, ['target_status' => 'confirmed']));
        $this->assertFalse($this->rule->isApplicable($this->testOrder, []));
        $this->assertFalse($this->rule->isApplicable($this->testOrder, ['action' => 'cancel']));
    }

    public function test_can_set_allowed_transitions()
    {
        $this->rule->setAllowedTransitions(['pending->confirmed']);
        
        $this->testOrder->setStatus('pending');
        $this->testOrder->markAsPaid();

        // İzin verilen geçiş
        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'confirmed'
        ]);
        $this->assertTrue($result->isValid());

        // İzin verilmeyen geçiş
        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'cancelled'
        ]);
        $this->assertFalse($result->isValid());
    }

    public function test_can_set_restricted_transitions()
    {
        $this->rule->setRestrictedTransitions(['pending->cancelled']);
        
        $this->testOrder->setStatus('pending');

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'cancelled'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('is restricted', $result->getErrors()[0]);
    }

    public function test_can_set_approval_required_transitions()
    {
        $this->rule->setApprovalRequiredTransitions(['pending->cancelled']);
        
        $this->testOrder->setStatus('pending');

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'cancelled'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->isOrderAllowed()); // Requires approval
        $this->assertContains('manager_approval', $result->getRequiredActions());
    }

    public function test_standard_rule_creation()
    {
        $standardRule = OrderStatusTransitionRule::standard();
        
        $this->assertInstanceOf(OrderStatusTransitionRule::class, $standardRule);
        $this->assertEquals('order_status_transition', $standardRule->getName());
        $this->assertEquals(200, $standardRule->getPriority());
    }

    public function test_strict_rule_creation()
    {
        $strictRule = OrderStatusTransitionRule::strict();
        
        $this->assertInstanceOf(OrderStatusTransitionRule::class, $strictRule);
        $this->assertEquals('order_status_transition', $strictRule->getName());
        $this->assertEquals(200, $strictRule->getPriority());
    }

    public function test_returns_next_allowed_transitions()
    {
        $this->testOrder->setStatus('pending');
        $this->testOrder->markAsPaid();

        $result = $this->rule->applyRule($this->testOrder, [
            'target_status' => 'confirmed'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        
        $metadata = $result->getMetadata();
        $this->assertArrayHasKey('next_allowed_transitions', $metadata);
        $this->assertIsArray($metadata['next_allowed_transitions']);
    }

    private function createTestOrder(): Order
    {
        $customer = new Customer();
        $customer->setId(1);
        $customer->setName('Test Customer');
        $customer->setEmail('<EMAIL>');

        $product = new Product();
        $product->setId(1);
        $product->setName('Test Product');
        $product->setStockQuantity(100);

        $orderItem = new OrderItem();
        $orderItem->setProduct($product);
        $orderItem->setQuantity(2);
        $orderItem->setUnitPrice(Money::fromAmount(50, 'TRY'));

        $order = new Order();
        $order->setId(1);
        $order->setOrderNumber(new OrderNumber('ORD-2024-001'));
        $order->setCustomer($customer);
        $order->setStatus('pending');
        $order->setTotalAmount(Money::fromAmount(100, 'TRY'));
        $order->setCreatedAt(Carbon::now());
        $order->addItem($orderItem);

        $shippingAddress = new Address(
            'Test Street 123',
            'Test City',
            'Test State',
            '12345',
            'Turkey'
        );
        $order->setShippingAddress($shippingAddress);

        return $order;
    }
}
