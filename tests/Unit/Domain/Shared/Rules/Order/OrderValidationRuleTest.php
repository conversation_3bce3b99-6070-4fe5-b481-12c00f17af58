<?php

namespace Tests\Unit\Domain\Shared\Rules\Order;

use Tests\TestCase;
use App\Domain\Shared\Rules\Order\OrderValidationRule;
use App\Domain\Shared\Rules\Order\OrderRuleResult;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Domain\Orders\ValueObjects\Address;
use App\Domain\Orders\Entities\OrderItem;
use App\Enums\OrderStatus;
use App\Domain\Customers\Entities\Customer;

class OrderValidationRuleTest extends TestCase
{
    private OrderValidationRule $rule;
    private Order $testOrder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rule = OrderValidationRule::standard();
        $this->testOrder = $this->createTestOrder();
    }

    public function test_allows_valid_order()
    {
        $result = $this->rule->applyRule($this->testOrder);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEmpty($result->getErrors());
    }

    public function test_denies_order_below_minimum_amount()
    {
        $this->testOrder->setTotalAmount(Money::fromAmount(5, 'TRY'));

        $result = $this->rule->applyRule($this->testOrder);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertNotEmpty($result->getErrors());
        $this->assertStringContainsString('below minimum', $result->getErrors()[0]);
    }

    public function test_denies_order_with_invalid_status()
    {
        $this->testOrder->setStatus('cancelled');

        $result = $this->rule->applyRule($this->testOrder);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertNotEmpty($result->getErrors());
        $this->assertStringContainsString('not allowed', $result->getErrors()[0]);
    }

    public function test_denies_order_without_items()
    {
        $this->testOrder->getItems()->clear();

        $result = $this->rule->applyRule($this->testOrder);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertContains('must contain at least one item', $result->getErrors());
    }

    public function test_denies_order_without_shipping_address()
    {
        $this->testOrder->setShippingAddress(null);

        $result = $this->rule->applyRule($this->testOrder);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertContains('Shipping address is required', $result->getErrors());
    }

    public function test_requires_customer_verification()
    {
        $customer = $this->testOrder->getCustomer();
        $customer->setEmailVerified(false);

        $result = $this->rule->applyRule($this->testOrder);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertTrue($result->hasRequiredActions());
        $this->assertContains('customer_verification', $result->getRequiredActions());
    }

    public function test_requires_payment_method_selection()
    {
        $this->testOrder->setPaymentMethod(null);

        $result = $this->rule->applyRule($this->testOrder);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertTrue($result->hasRequiredActions());
        $this->assertContains('payment_method_selection', $result->getRequiredActions());
    }

    public function test_denies_insufficient_inventory()
    {
        // Stok yetersiz context'i ile test
        $result = $this->rule->applyRule($this->testOrder, [
            'inventory_checked' => false
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertContains('Insufficient inventory', $result->getErrors());
    }

    public function test_allows_with_verified_customer()
    {
        $result = $this->rule->applyRule($this->testOrder, [
            'customer_verified' => true,
            'inventory_checked' => true
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEmpty($result->getRequiredActions());
    }

    public function test_requires_phone_verification_for_high_amount()
    {
        $this->testOrder->setTotalAmount(Money::fromAmount(1500, 'TRY'));
        $customer = $this->testOrder->getCustomer();
        $customer->setPhoneVerified(false);

        $result = $this->rule->applyRule($this->testOrder);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertTrue($result->hasRequiredActions());
        $this->assertContains('customer_verification', $result->getRequiredActions());
    }

    public function test_denies_non_order_entity()
    {
        $customer = new Customer();
        
        $result = $this->rule->applyRule($customer);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('not supported', $result->getErrors()[0]);
    }

    public function test_is_applicable_to_orders_only()
    {
        $this->assertTrue($this->rule->isApplicable($this->testOrder));
        $this->assertFalse($this->rule->isApplicable(new Customer()));
    }

    public function test_rule_properties()
    {
        $this->assertEquals('order_validation', $this->rule->getName());
        $this->assertEquals(100, $this->rule->getPriority());
        $this->assertStringContainsString('order requirements', $this->rule->getDescription());
    }

    public function test_can_set_minimum_order_amount()
    {
        $newMinimum = Money::fromAmount(50, 'TRY');
        $this->rule->setMinimumOrderAmount($newMinimum);

        $this->testOrder->setTotalAmount(Money::fromAmount(25, 'TRY'));
        $result = $this->rule->applyRule($this->testOrder);

        $this->assertFalse($result->isValid());
        $this->assertStringContainsString('50', $result->getErrors()[0]);
    }

    public function test_can_set_allowed_statuses()
    {
        $this->rule->setAllowedStatuses(['pending']);

        $this->testOrder->setStatus('processing');
        $result = $this->rule->applyRule($this->testOrder);

        $this->assertFalse($result->isValid());
        $this->assertStringContainsString('not allowed', $result->getErrors()[0]);
    }

    public function test_can_disable_customer_verification()
    {
        $this->rule->setRequireCustomerVerification(false);
        
        $customer = $this->testOrder->getCustomer();
        $customer->setEmailVerified(false);

        $result = $this->rule->applyRule($this->testOrder);

        $this->assertTrue($result->isValid());
        $this->assertEmpty($result->getRequiredActions());
    }

    public function test_can_disable_inventory_check()
    {
        $this->rule->setRequireInventoryCheck(false);

        $result = $this->rule->applyRule($this->testOrder, [
            'inventory_checked' => false
        ]);

        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
    }

    public function test_express_rule_creation()
    {
        $expressRule = OrderValidationRule::express();
        
        $this->assertInstanceOf(OrderValidationRule::class, $expressRule);
        $this->assertEquals('order_validation', $expressRule->getName());
        $this->assertEquals(50, $expressRule->getPriority());
    }

    private function createTestOrder(): Order
    {
        $customer = new Customer();
        $customer->setId(1);
        $customer->setName('Test Customer');
        $customer->setEmail('<EMAIL>');
        $customer->setEmailVerified(true);
        $customer->setPhoneVerified(true);

        $order = new Order(
            1, // userId
            new OrderNumber('ORD-2024-001'),
            new Money(100.00, 'TRY'),
            'credit_card',
            'standard_shipping'
        );
        $order->setId(1);
        $order->setCustomer($customer);
        $order->setStatus('processing');
        $order->setTotalAmount(Money::fromAmount(200, 'TRY'));
        $order->setPaymentMethod('credit_card');
        $order->setCreatedAt(now());
        
        // Shipping address ekle
        $address = new Address(
            'John Doe',
            '+905551234567',
            'Test Street 123',
            'Istanbul',
            'Istanbul',
            'Turkey',
            'shipping'
        );
        $order->setShippingAddress($address);
        
        // Items ekle
        $order->addItem($this->createTestOrderItem());

        return $order;
    }

    private function createTestOrderItem()
    {
        return new OrderItem(
            1, // productId
            'Test Product',
            Money::fromAmount(50, 'TRY'),
            2 // quantity
        );
    }

    private function createTestProduct()
    {
        $product = new \stdClass();
        $product->id = 1;
        $product->name = 'Test Product';
        $product->stock_quantity = 10;
        $product->is_active = true;
        
        return $product;
    }
}
