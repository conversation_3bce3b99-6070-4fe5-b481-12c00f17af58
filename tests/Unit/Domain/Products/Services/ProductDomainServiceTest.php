<?php

namespace Tests\Unit\Domain\Products\Services;

use Tests\TestCase;
use App\Domain\Products\Services\ProductDomainService;
use App\Domain\Products\Entities\Product;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Domain\Products\Repositories\ProductRepositoryInterface;
use Mockery;

class ProductDomainServiceTest extends TestCase
{
    private ProductDomainService $service;
    private ProductRepositoryInterface $mockRepository;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockRepository = Mockery::mock(ProductRepositoryInterface::class);
        $this->service = new ProductDomainService($this->mockRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_create_a_product()
    {
        // Arrange
        $name = 'Test Ürün';
        $slug = 'test-urun';
        $sku = SKU::fromString('TEST-001');
        $price = Price::fromAmount(100.0, 'TRY');
        $stock = Stock::fromQuantity(10);
        $categoryId = 1;

        $this->mockRepository
            ->shouldReceive('findBySku')
            ->with($sku)
            ->andReturn(null);

        $this->mockRepository
            ->shouldReceive('findBySlug')
            ->with($slug)
            ->andReturn(null);

        // Act
        $product = $this->service->createProduct(
            $name,
            $slug,
            $sku,
            $price,
            $stock,
            $categoryId
        );

        // Assert
        $this->assertInstanceOf(Product::class, $product);
        $this->assertEquals($name, $product->getName());
        $this->assertEquals($slug, $product->getSlug());
        $this->assertTrue($sku->equals($product->getSku()));
        $this->assertTrue($price->equals($product->getPrice()));
        $this->assertEquals($categoryId, $product->getCategoryId());
    }

    /** @test */
    public function it_throws_exception_when_sku_already_exists()
    {
        // Arrange
        $sku = SKU::fromString('TEST-001');
        $existingProduct = Mockery::mock(Product::class);

        $this->mockRepository
            ->shouldReceive('findBySku')
            ->with($sku)
            ->andReturn($existingProduct);

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage("SKU 'TEST-001' zaten kullanımda.");

        // Act
        $this->service->createProduct(
            'Test Ürün',
            'test-urun',
            $sku,
            Price::fromAmount(100.0),
            Stock::fromQuantity(10),
            1
        );
    }

    /** @test */
    public function it_throws_exception_when_slug_already_exists()
    {
        // Arrange
        $slug = 'test-urun';
        $sku = SKU::fromString('TEST-001');
        $existingProduct = Mockery::mock(Product::class);

        $this->mockRepository
            ->shouldReceive('findBySku')
            ->with($sku)
            ->andReturn(null);

        $this->mockRepository
            ->shouldReceive('findBySlug')
            ->with($slug)
            ->andReturn($existingProduct);

        // Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage("Slug 'test-urun' zaten kullanımda.");

        // Act
        $this->service->createProduct(
            'Test Ürün',
            $slug,
            $sku,
            Price::fromAmount(100.0),
            Stock::fromQuantity(10),
            1
        );
    }

    /** @test */
    public function it_can_update_product_basic_info()
    {
        // Arrange
        $product = $this->createTestProduct();
        $updateData = [
            'name' => 'Güncellenmiş Ürün',
            'slug' => 'guncellenmis-urun',
            'description' => 'Yeni açıklama'
        ];

        $this->mockRepository
            ->shouldReceive('findBySlug')
            ->with('guncellenmis-urun')
            ->andReturn(null);

        // Act
        $updatedProduct = $this->service->updateProduct($product, $updateData);

        // Assert
        $this->assertEquals('Güncellenmiş Ürün', $updatedProduct->getName());
        $this->assertEquals('guncellenmis-urun', $updatedProduct->getSlug());
        $this->assertEquals('Yeni açıklama', $updatedProduct->getDescription());
    }

    /** @test */
    public function it_can_update_product_price()
    {
        // Arrange
        $product = $this->createTestProduct();
        $newPrice = ['amount' => 150.0, 'currency' => 'TRY'];
        $updateData = ['price' => $newPrice];

        // Act
        $updatedProduct = $this->service->updateProduct($product, $updateData);

        // Assert
        $this->assertEquals(150.0, $updatedProduct->getPrice()->getAmount());
    }

    /** @test */
    public function it_can_increment_view_count()
    {
        // Arrange
        $product = $this->createTestProduct();
        $initialViewCount = $product->getViewCount();

        // Act
        $this->service->incrementViewCount($product);

        // Assert
        $this->assertEquals($initialViewCount + 1, $product->getViewCount());
    }

    private function createTestProduct(): Product
    {
        return Product::create(
            'Test Ürün',
            'test-urun',
            SKU::fromString('TEST-001'),
            Price::fromAmount(100.0, 'TRY'),
            Stock::fromQuantity(10),
            1,
            'Test açıklama'
        );
    }
}
