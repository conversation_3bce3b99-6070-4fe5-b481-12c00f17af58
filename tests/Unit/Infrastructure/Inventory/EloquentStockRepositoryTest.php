<?php

namespace Tests\Unit\Infrastructure\Inventory;

use Tests\TestCase;
use App\Infrastructure\Inventory\Repositories\EloquentStockRepository;
use App\Infrastructure\Inventory\Mappers\StockMapper;
use App\Infrastructure\Inventory\Models\EloquentStock;
use App\Infrastructure\Inventory\Models\EloquentStockLocation;
use App\Domain\Inventory\Entities\Stock;
use App\Domain\Inventory\ValueObjects\StockLevel;
use App\Domain\Inventory\ValueObjects\StockLocation;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

/**
 * EloquentStockRepositoryTest
 * EloquentStockRepository unit test'leri
 */
class EloquentStockRepositoryTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private EloquentStockRepository $repository;
    private StockMapper $mapper;
    private EloquentStockLocation $location;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mapper = new StockMapper();
        $this->repository = new EloquentStockRepository($this->mapper);

        // Test data oluştur
        $this->createInventoryTestData();
    }

    /** @test */
    public function it_can_save_and_retrieve_stock()
    {
        // Arrange
        $stock = $this->createTestStock();

        // Act
        $this->repository->save($stock);
        $retrievedStock = $this->repository->findById($stock->getId());

        // Assert
        $this->assertNotNull($retrievedStock);
        $this->assertEquals($stock->getProductId(), $retrievedStock->getProductId());
        $this->assertEquals($stock->getAvailableQuantity()->getValue(), $retrievedStock->getAvailableQuantity()->getValue());
        $this->assertEquals($stock->getLowStockThreshold()->getValue(), $retrievedStock->getLowStockThreshold()->getValue());
    }

    /** @test */
    public function it_can_find_stock_by_product()
    {
        // Arrange
        $stock = $this->createTestStock();
        $this->repository->save($stock);

        // Act
        $foundStock = $this->repository->findByProduct($this->product->id);

        // Assert
        $this->assertNotNull($foundStock);
        $this->assertEquals($stock->getProductId(), $foundStock->getProductId());
    }

    /** @test */
    public function it_can_find_all_stocks_by_product()
    {
        // Arrange
        $stock1 = $this->createTestStock();
        $stock2 = $this->createTestStock(['available_quantity' => 20]);
        
        $this->repository->save($stock1);
        $this->repository->save($stock2);

        // Act
        $stocks = $this->repository->findAllByProduct($this->product->id);

        // Assert
        $this->assertCount(2, $stocks);
        $this->assertContainsOnlyInstancesOf(Stock::class, $stocks);
    }

    /** @test */
    public function it_can_find_available_stocks_by_product()
    {
        // Arrange
        $availableStock = $this->createTestStock(['available_quantity' => 10]);
        $outOfStock = $this->createTestStock(['available_quantity' => 0]);
        
        $this->repository->save($availableStock);
        $this->repository->save($outOfStock);

        // Act
        $availableStocks = $this->repository->findAvailableByProduct($this->product->id);

        // Assert
        $this->assertCount(1, $availableStocks);
        $this->assertEquals(10, $availableStocks[0]->getAvailableQuantity()->getValue());
    }

    /** @test */
    public function it_can_find_low_stock_items()
    {
        // Arrange
        $lowStock = $this->createTestStock([
            'available_quantity' => 5,
            'low_stock_threshold' => 10
        ]);
        $normalStock = $this->createTestStock([
            'available_quantity' => 20,
            'low_stock_threshold' => 10
        ]);
        
        $this->repository->save($lowStock);
        $this->repository->save($normalStock);

        // Act
        $lowStockItems = $this->repository->findLowStock();

        // Assert
        $this->assertCount(1, $lowStockItems);
        $this->assertEquals(5, $lowStockItems[0]->getAvailableQuantity()->getValue());
    }

    /** @test */
    public function it_can_find_out_of_stock_items()
    {
        // Arrange
        $outOfStock = $this->createTestStock(['available_quantity' => 0]);
        $inStock = $this->createTestStock(['available_quantity' => 10]);
        
        $this->repository->save($outOfStock);
        $this->repository->save($inStock);

        // Act
        $outOfStockItems = $this->repository->findOutOfStock();

        // Assert
        $this->assertCount(1, $outOfStockItems);
        $this->assertEquals(0, $outOfStockItems[0]->getAvailableQuantity()->getValue());
    }

    /** @test */
    public function it_can_find_stocks_needing_reorder()
    {
        // Arrange
        $needsReorder = $this->createTestStock([
            'available_quantity' => 5,
            'reorder_level' => 10
        ]);
        $noReorderNeeded = $this->createTestStock([
            'available_quantity' => 20,
            'reorder_level' => 10
        ]);
        
        $this->repository->save($needsReorder);
        $this->repository->save($noReorderNeeded);

        // Act
        $reorderItems = $this->repository->findNeedingReorder();

        // Assert
        $this->assertCount(1, $reorderItems);
        $this->assertEquals(5, $reorderItems[0]->getAvailableQuantity()->getValue());
    }

    /** @test */
    public function it_can_get_stock_statistics()
    {
        // Arrange
        $stock1 = $this->createTestStock([
            'available_quantity' => 10,
            'reserved_quantity' => 5,
            'low_stock_threshold' => 15
        ]);
        $stock2 = $this->createTestStock([
            'available_quantity' => 0,
            'reserved_quantity' => 0,
            'low_stock_threshold' => 5
        ]);
        
        $this->repository->save($stock1);
        $this->repository->save($stock2);

        // Act
        $statistics = $this->repository->getStatistics();

        // Assert
        $this->assertIsArray($statistics);
        $this->assertArrayHasKey('total_stocks', $statistics);
        $this->assertArrayHasKey('total_available', $statistics);
        $this->assertArrayHasKey('total_reserved', $statistics);
        $this->assertArrayHasKey('low_stock_count', $statistics);
        $this->assertArrayHasKey('out_of_stock_count', $statistics);
        
        $this->assertEquals(2, $statistics['total_stocks']);
        $this->assertEquals(10, $statistics['total_available']);
        $this->assertEquals(5, $statistics['total_reserved']);
        $this->assertEquals(1, $statistics['low_stock_count']);
        $this->assertEquals(1, $statistics['out_of_stock_count']);
    }

    /** @test */
    public function it_can_paginate_stocks()
    {
        // Arrange
        for ($i = 0; $i < 25; $i++) {
            $stock = $this->createTestStock(['available_quantity' => $i]);
            $this->repository->save($stock);
        }

        // Act
        $page1 = $this->repository->paginate(1, 10);
        $page2 = $this->repository->paginate(2, 10);

        // Assert
        $this->assertIsArray($page1);
        $this->assertArrayHasKey('data', $page1);
        $this->assertArrayHasKey('total', $page1);
        $this->assertArrayHasKey('per_page', $page1);
        $this->assertArrayHasKey('current_page', $page1);
        
        $this->assertCount(10, $page1['data']);
        $this->assertEquals(25, $page1['total']);
        $this->assertEquals(1, $page1['current_page']);
        
        $this->assertCount(10, $page2['data']);
        $this->assertEquals(2, $page2['current_page']);
    }

    /** @test */
    public function it_can_search_stocks()
    {
        // Arrange
        $this->product->update(['name' => 'Test Product', 'sku' => 'TEST-001']);
        $stock = $this->createTestStock();
        $this->repository->save($stock);

        // Act
        $results = $this->repository->search('Test Product');

        // Assert
        $this->assertIsArray($results);
        $this->assertCount(1, $results);
        $this->assertInstanceOf(Stock::class, $results[0]);
    }

    /** @test */
    public function it_can_delete_stock()
    {
        // Arrange
        $stock = $this->createTestStock();
        $this->repository->save($stock);
        $stockId = $stock->getId();

        // Act
        $this->repository->delete($stock);
        $deletedStock = $this->repository->findById($stockId);

        // Assert
        $this->assertNull($deletedStock);
    }

    /** @test */
    public function it_can_save_batch_stocks()
    {
        // Arrange
        $stocks = [
            $this->createTestStock(['available_quantity' => 10]),
            $this->createTestStock(['available_quantity' => 20]),
            $this->createTestStock(['available_quantity' => 30])
        ];

        // Act
        $this->repository->saveBatch($stocks);

        // Assert
        foreach ($stocks as $stock) {
            $savedStock = $this->repository->findById($stock->getId());
            $this->assertNotNull($savedStock);
        }
    }

    /**
     * Test data oluştur
     */
    protected function createInventoryTestData(): void
    {
        // Category oluştur
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'status' => true
        ]);

        // Product oluştur
        $this->product = Product::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'description' => 'Test description',
            'price' => 100.00,
            'stock' => 50,
            'category_id' => $category->id,
            'status' => true,
            'sku' => 'TEST-PRODUCT-001'
        ]);

        // Stock location oluştur
        $this->location = EloquentStockLocation::create([
            'name' => 'Test Warehouse',
            'code' => 'TEST-WH',
            'type' => 'warehouse',
            'is_active' => true,
            'is_default' => true,
            'priority' => 1
        ]);
    }

    /**
     * Test stock entity oluştur
     */
    private function createTestStock(array $overrides = []): Stock
    {
        $defaults = [
            'available_quantity' => 50,
            'low_stock_threshold' => 10,
            'reorder_level' => 20,
            'reserved_quantity' => 0
        ];

        $data = array_merge($defaults, $overrides);

        $location = new StockLocation(
            $this->location->code,
            $this->location->name,
            $this->location->type
        );

        return Stock::create(
            $this->product->id,
            null,
            new StockLevel($data['available_quantity']),
            new StockLevel($data['low_stock_threshold']),
            new StockLevel($data['reorder_level']),
            $location,
            true,
            false
        );
    }
}
