<?php

namespace Tests\Unit\Core\Infrastructure\Api\Versioning;

use App\Core\Infrastructure\Api\Versioning\Middleware\ApiVersionMiddleware;
use App\Core\Infrastructure\Api\Versioning\Contracts\ApiVersionManagerInterface;
use App\Core\Infrastructure\Api\Contracts\ApiResponseInterface;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Tests\TestCase;
use Mockery;

/**
 * ApiVersionMiddlewareTest
 * API version middleware için unit test'ler
 */
class ApiVersionMiddlewareTest extends TestCase
{
    private $versionManager;
    private $apiResponse;
    private ApiVersionMiddleware $middleware;

    protected function setUp(): void
    {
        parent::setUp();

        $this->versionManager = Mockery::mock(ApiVersionManagerInterface::class);
        $this->apiResponse = Mockery::mock(ApiResponseInterface::class);

        $this->middleware = new ApiVersionMiddleware(
            $this->versionManager,
            $this->apiResponse
        );
    }

    /** @test */
    public function it_detects_and_sets_version_on_request()
    {
        $request = Request::create('/api/test', 'GET');

        $this->versionManager
            ->shouldReceive('detectVersion')
            ->with($request)
            ->once()
            ->andReturn('1.0');

        $this->versionManager
            ->shouldReceive('isVersionSupported')
            ->with('1.0')
            ->once()
            ->andReturn(true);

        $this->versionManager
            ->shouldReceive('isVersionSunset')
            ->with('1.0')
            ->once()
            ->andReturn(false);

        $this->versionManager
            ->shouldReceive('trackVersionUsage')
            ->with('1.0', $request)
            ->once();

        $this->versionManager
            ->shouldReceive('isVersionDeprecated')
            ->with('1.0')
            ->once()
            ->andReturn(false);

        $this->versionManager
            ->shouldReceive('getSupportedVersions')
            ->once()
            ->andReturn(['1.0', '1.1']);

        $response = $this->middleware->handle($request, function ($req) {
            $this->assertEquals('1.0', $req->attributes->get('api_version'));
            return new Response('test');
        });

        $this->assertInstanceOf(Response::class, $response);
    }

    /** @test */
    public function it_returns_error_for_unsupported_version()
    {
        $request = Request::create('/api/test', 'GET');

        $this->versionManager
            ->shouldReceive('detectVersion')
            ->with($request)
            ->once()
            ->andReturn('2.0');

        $this->versionManager
            ->shouldReceive('isVersionSupported')
            ->with('2.0')
            ->once()
            ->andReturn(false);

        $this->versionManager
            ->shouldReceive('getSupportedVersions')
            ->once()
            ->andReturn(['1.0', '1.1']);

        $this->versionManager
            ->shouldReceive('getLatestVersion')
            ->once()
            ->andReturn('1.1');

        $this->versionManager
            ->shouldReceive('getDefaultVersion')
            ->once()
            ->andReturn('1.0');

        $response = $this->middleware->handle($request, function ($req) {
            $this->fail('Next middleware should not be called for unsupported version');
        });

        $this->assertEquals(400, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('unsupported_version', $content['error']);
        $this->assertEquals('2.0', $content['data']['requested_version']);
    }

    /** @test */
    public function it_returns_error_for_sunset_version()
    {
        $request = Request::create('/api/test', 'GET');

        $this->versionManager
            ->shouldReceive('detectVersion')
            ->with($request)
            ->once()
            ->andReturn('0.9');

        $this->versionManager
            ->shouldReceive('isVersionSupported')
            ->with('0.9')
            ->once()
            ->andReturn(true);

        $this->versionManager
            ->shouldReceive('isVersionSunset')
            ->with('0.9')
            ->once()
            ->andReturn(true);

        $this->versionManager
            ->shouldReceive('getSunsetWarning')
            ->with('0.9')
            ->once()
            ->andReturn('Version 0.9 has been sunset');

        $this->versionManager
            ->shouldReceive('getLatestVersion')
            ->once()
            ->andReturn('1.1');

        $response = $this->middleware->handle($request, function ($req) {
            $this->fail('Next middleware should not be called for sunset version');
        });

        $this->assertEquals(410, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('version_sunset', $content['error']);
        $this->assertEquals('0.9', $content['data']['sunset_version']);
    }

    /** @test */
    public function it_adds_version_headers_to_response()
    {
        $request = Request::create('/api/test', 'GET');

        $this->versionManager
            ->shouldReceive('detectVersion')
            ->with($request)
            ->once()
            ->andReturn('1.0');

        $this->versionManager
            ->shouldReceive('isVersionSupported')
            ->with('1.0')
            ->once()
            ->andReturn(true);

        $this->versionManager
            ->shouldReceive('isVersionSunset')
            ->with('1.0')
            ->once()
            ->andReturn(false);

        $this->versionManager
            ->shouldReceive('trackVersionUsage')
            ->with('1.0', $request)
            ->once();

        $this->versionManager
            ->shouldReceive('isVersionDeprecated')
            ->with('1.0')
            ->once()
            ->andReturn(false);

        $this->versionManager
            ->shouldReceive('getSupportedVersions')
            ->once()
            ->andReturn(['1.0', '1.1']);

        // Mock config
        config(['api_versioning.headers' => [
            'version' => 'X-API-Version',
            'supported_versions' => 'X-Supported-Versions',
        ]]);

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('test');
        });

        $this->assertTrue($response->headers->has('X-API-Version'));
        $this->assertEquals('1.0', $response->headers->get('X-API-Version'));
        $this->assertTrue($response->headers->has('X-Supported-Versions'));
    }

    /** @test */
    public function it_adds_deprecation_warning_header()
    {
        $request = Request::create('/api/test', 'GET');

        $this->versionManager
            ->shouldReceive('detectVersion')
            ->with($request)
            ->once()
            ->andReturn('0.9');

        $this->versionManager
            ->shouldReceive('isVersionSupported')
            ->with('0.9')
            ->once()
            ->andReturn(true);

        $this->versionManager
            ->shouldReceive('isVersionSunset')
            ->with('0.9')
            ->once()
            ->andReturn(false);

        $this->versionManager
            ->shouldReceive('trackVersionUsage')
            ->with('0.9', $request)
            ->once();

        $this->versionManager
            ->shouldReceive('isVersionDeprecated')
            ->with('0.9')
            ->once()
            ->andReturn(true);

        $this->versionManager
            ->shouldReceive('getDeprecationWarning')
            ->with('0.9')
            ->once()
            ->andReturn('Version 0.9 is deprecated');

        $this->versionManager
            ->shouldReceive('getSupportedVersions')
            ->once()
            ->andReturn(['0.9', '1.0', '1.1']);

        // Mock config
        config(['api_versioning.headers' => [
            'version' => 'X-API-Version',
            'supported_versions' => 'X-Supported-Versions',
            'deprecation_warning' => 'X-API-Deprecation-Warning',
        ]]);

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('test');
        });

        $this->assertTrue($response->headers->has('X-API-Deprecation-Warning'));
        $this->assertEquals('Version 0.9 is deprecated', $response->headers->get('X-API-Deprecation-Warning'));
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
