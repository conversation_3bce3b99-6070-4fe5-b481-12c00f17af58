<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="CLI Arguments" tests="189" assertions="188" errors="134" failures="7" skipped="0" time="2.795463">
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" tests="23" assertions="0" errors="23" failures="0" skipped="0" time="0.000000">
      <testcase name="test_strict_rule_creation" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="353" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_strict_rule_creation&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_time_limit_exceeded_completely_denied" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="246" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_time_limit_exceeded_completely_denied&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_allows_cancellation_for_pending_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="29" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_allows_cancellation_for_pending_order&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_denies_cancellation_for_shipped_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="60" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_denies_cancellation_for_shipped_order&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_flexible_rule_creation" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="371" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_flexible_rule_creation&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_denies_cancellation_for_completed_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="91" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_denies_cancellation_for_completed_order&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_calculates_cancellation_fee_for_high_amount_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="122" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_calculates_cancellation_fee_for_high_amount_order&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_standard_rule_creation" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="344" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_standard_rule_creation&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_can_set_cancellation_fee_threshold" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="308" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_can_set_cancellation_fee_threshold&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_is_applicable_for_cancel_action" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="271" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_is_applicable_for_cancel_action&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="264" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_rule_properties&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_allows_cancellation_for_confirmed_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="45" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_allows_cancellation_for_confirmed_order&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_can_set_cancellation_time_limit" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="292" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_can_set_cancellation_time_limit&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_requires_refund_for_paid_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="160" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_requires_refund_for_paid_order&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_can_set_cancellable_statuses" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="278" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_can_set_cancellable_statuses&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_time_limit_exceeded_requires_approval" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="228" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_time_limit_exceeded_requires_approval&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_requires_cancellation_reason" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="106" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_requires_cancellation_reason&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_no_cancellation_fee_for_low_amount_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="142" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_no_cancellation_fee_for_low_amount_order&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_can_set_cancellation_fee_percentage" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="324" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_can_set_cancellation_fee_percentage&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_denies_cancellation_for_non_order_entity" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="197" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_denies_cancellation_for_non_order_entity&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_requires_inventory_restoration_for_processing_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="181" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_requires_inventory_restoration_for_processing_order&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_denies_cancellation_for_delivered_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="76" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_denies_cancellation_for_delivered_order&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
      <testcase name="test_time_limit_check_within_limit" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php" line="212" class="Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderCancellationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest::test_time_limit_check_within_limit&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26</error>
      </testcase>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" tests="14" assertions="0" errors="14" failures="0" skipped="0" time="0.000000">
      <testcase name="test_gets_specific_rule" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="186" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_gets_specific_rule&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_checks_multiple_products" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="138" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_checks_multiple_products&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_inventory_policy_result_methods" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="238" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_inventory_policy_result_methods&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_can_register_and_unregister_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="173" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_can_register_and_unregister_rules&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_checks_stock_successfully" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="63" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_checks_stock_successfully&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_health_check_passes_with_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="209" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_health_check_passes_with_rules&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_checks_stock_with_insufficient_quantity" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="74" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_checks_stock_with_insufficient_quantity&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_gets_backorder_info" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="116" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_gets_backorder_info&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_provides_service_statistics" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="195" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_provides_service_statistics&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_detects_reorder_requirement" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="90" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_detects_reorder_requirement&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_can_reserve_stock" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="84" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_can_reserve_stock&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_gets_stock_warnings" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="129" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_gets_stock_warnings&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_service_initializes_correctly" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="52" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_service_initializes_correctly&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
      <testcase name="test_health_check_fails_without_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php" line="222" class="Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.InventoryPolicyServiceTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest::test_health_check_fails_without_rules&#13;
TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39</error>
      </testcase>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" tests="13" assertions="0" errors="13" failures="0" skipped="0" time="0.000000">
      <testcase name="test_can_register_and_unregister_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="53" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_can_register_and_unregister_rules&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_validates_payment_with_minimum_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="115" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_validates_payment_with_minimum_amount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_can_check_rule_existence" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="96" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_can_check_rule_existence&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_security_check_with_international_payment" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="165" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_security_check_with_international_payment&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_can_clear_and_reload_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="104" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_can_clear_and_reload_rules&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_security_check_with_rapid_transactions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="179" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_security_check_with_rapid_transactions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_validates_payment_with_invalid_method" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="126" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_validates_payment_with_invalid_method&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_can_validate_refund" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="40" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_can_validate_refund&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_can_get_rules_by_priority" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="69" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_can_get_rules_by_priority&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_security_check_with_high_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="151" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_security_check_with_high_amount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_can_get_specific_rule" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="87" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_can_get_specific_rule&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_validates_refund_with_conditions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="137" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_validates_refund_with_conditions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
      <testcase name="test_can_validate_payment" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php" line="26" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentPolicyServiceTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest::test_can_validate_payment&#13;
ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23</error>
      </testcase>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" tests="11" assertions="33" errors="0" failures="0" skipped="0" time="0.570105">
      <testcase name="test_loads_standard_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="164" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="2" time="0.065420"/>
      <testcase name="test_health_check_passes_with_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="202" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="1" time="0.049090"/>
      <testcase name="test_service_initializes_correctly" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="28" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="4" time="0.048942"/>
      <testcase name="test_calculates_price_with_tax" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="82" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="4" time="0.049876"/>
      <testcase name="test_can_unregister_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="180" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="3" time="0.047483"/>
      <testcase name="test_calculates_price_with_quantity_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="54" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="4" time="0.055896"/>
      <testcase name="test_validates_pricing" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="139" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="4" time="0.049698"/>
      <testcase name="test_health_check_fails_without_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="214" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="1" time="0.046132"/>
      <testcase name="test_provides_service_statistics" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="223" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="4" time="0.054207"/>
      <testcase name="test_calculates_price_with_multiple_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="107" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="5" time="0.052211"/>
      <testcase name="test_can_register_pricing_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php" line="39" class="Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Pricing.PricingPolicyServiceTest" assertions="1" time="0.051150"/>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" tests="11" assertions="46" errors="0" failures="0" skipped="0" time="0.605731">
      <testcase name="test_handles_non_product_entity" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="158" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="5" time="0.067737"/>
      <testcase name="test_handles_insufficient_stock_with_backorder_allowed" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="59" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="8" time="0.062713"/>
      <testcase name="test_not_applicable_for_digital_products" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="94" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="1" time="0.072191"/>
      <testcase name="test_validates_sufficient_stock" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="44" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="8" time="0.051110"/>
      <testcase name="test_default_quantity_when_not_specified" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="173" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="3" time="0.051843"/>
      <testcase name="test_handles_insufficient_stock_without_backorder" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="76" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="9" time="0.047385"/>
      <testcase name="test_stock_critical_level_detection" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="123" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="3" time="0.052606"/>
      <testcase name="test_stock_not_critical" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="135" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="2" time="0.048382"/>
      <testcase name="test_configuration_methods" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="146" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="3" time="0.050125"/>
      <testcase name="test_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="116" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="3" time="0.053201"/>
      <testcase name="test_not_applicable_when_tracking_disabled" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php" line="103" class="Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Inventory.StockValidationRuleTest" assertions="1" time="0.048439"/>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" tests="15" assertions="46" errors="0" failures="4" skipped="0" time="0.738403">
      <testcase name="test_can_check_rule_existence" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="103" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="4" time="0.054878"/>
      <testcase name="test_can_get_rule_result_by_name" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="177" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="2" time="0.049543"/>
      <testcase name="test_can_get_specific_rule" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="94" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="3" time="0.050190"/>
      <testcase name="test_can_validate_cancellation" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="52" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="3" time="0.047705"/>
      <testcase name="test_can_validate_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="29" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="5" time="0.045480"/>
      <testcase name="test_validates_cancellation_for_shipped_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="155" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="3" time="0.054622"/>
      <testcase name="test_validates_status_transition_invalid" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="144" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="3" time="0.045107"/>
      <testcase name="test_can_register_and_unregister_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="63" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="1" time="0.049815">
        <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_register_and_unregister_rules&#13;
Failed asserting that actual size 5 matches expected size 4.
&#13;
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php:69</failure>
      </testcase>
      <testcase name="test_order_requires_actions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="166" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="2" time="0.046046"/>
      <testcase name="test_validates_order_with_insufficient_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="122" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="3" time="0.052098"/>
      <testcase name="test_can_get_rules_by_priority" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="76" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="2" time="0.046262">
        <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_get_rules_by_priority&#13;
Failed asserting that actual size 5 matches expected size 3.
&#13;
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php:81</failure>
      </testcase>
      <testcase name="test_can_clear_and_reload_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="111" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="1" time="0.047001">
        <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_clear_and_reload_rules&#13;
Failed asserting that actual size 5 matches expected size 3.
&#13;
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php:113</failure>
      </testcase>
      <testcase name="test_can_validate_status_transition" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="40" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="4" time="0.050179"/>
      <testcase name="test_result_to_array" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="186" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="9" time="0.049698"/>
      <testcase name="test_validates_order_without_items" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php" line="133" class="Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderPolicyServiceTest" assertions="1" time="0.049779">
        <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_validates_order_without_items&#13;
Failed asserting that true is false.
&#13;
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php:139</failure>
      </testcase>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" tests="23" assertions="0" errors="23" failures="0" skipped="0" time="0.000000">
      <testcase name="test_usage_limit_exceeded_returns_no_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="196" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_usage_limit_exceeded_returns_no_discount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_is_applicable_with_matching_coupon_code" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="280" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_is_applicable_with_matching_coupon_code&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_get_discount_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="343" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_get_discount_amount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_not_yet_valid_coupon_returns_no_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="124" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_not_yet_valid_coupon_returns_no_discount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_percentage_coupon_applies_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="27" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_percentage_coupon_applies_discount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_fixed_amount_coupon_applies_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="52" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_fixed_amount_coupon_applies_discount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_coupon_code_mismatch_returns_no_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="78" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_coupon_code_mismatch_returns_no_discount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_minimum_amount_not_met_returns_no_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="146" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_minimum_amount_not_met_returns_no_discount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_custom_priority" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="442" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_custom_priority&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_minimum_amount_met_applies_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="171" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_minimum_amount_met_applies_discount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_get_used_count" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="384" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_get_used_count&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_fixed_coupon_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="267" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_fixed_coupon_rule_properties&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_is_valid_method" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="400" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_is_valid_method&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="252" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_rule_properties&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_check_applicability" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="307" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_check_applicability&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_get_coupon_code" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="319" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_get_coupon_code&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_expired_coupon_returns_no_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="102" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_expired_coupon_returns_no_discount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_percentage_validation_bounds" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="466" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_percentage_validation_bounds&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_get_usage_limit" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="371" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_get_usage_limit&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_get_discount_percentage" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="331" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_get_discount_percentage&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_get_minimum_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="356" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_get_minimum_amount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_usage_limit_not_exceeded_applies_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="226" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_usage_limit_not_exceeded_applies_discount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
      <testcase name="test_is_applicable_with_coupon_context_key" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php" line="294" class="Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Discount.CouponValidationRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest::test_is_applicable_with_coupon_context_key&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24</error>
      </testcase>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" tests="13" assertions="0" errors="13" failures="0" skipped="0" time="0.000000">
      <testcase name="test_can_set_allowed_methods" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="161" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_can_set_allowed_methods&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_can_set_minimum_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="147" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_can_set_minimum_amount&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_denies_non_order_entity" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="119" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_denies_non_order_entity&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_denies_payment_for_invalid_order_status" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="68" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_denies_payment_for_invalid_order_status&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_allows_valid_payment" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="27" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_allows_valid_payment&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_is_applicable_to_orders_only" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="134" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_is_applicable_to_orders_only&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_requires_additional_verification_for_very_high_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="101" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_requires_additional_verification_for_very_high_amount&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_strict_rule_creation" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="183" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_strict_rule_creation&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_standard_rule_creation" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="174" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_standard_rule_creation&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_denies_invalid_payment_method" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="54" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_denies_invalid_payment_method&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_denies_payment_below_minimum" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="40" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_denies_payment_below_minimum&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="140" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_rule_properties&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
      <testcase name="test_requires_verification_for_high_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php" line="84" class="Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Payment.PaymentValidationRuleTest" assertions="0" time="0.000000">
        <error type="TypeError">Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest::test_requires_verification_for_high_amount&#13;
TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20
&#13;
C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20</error>
      </testcase>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" tests="18" assertions="63" errors="0" failures="3" skipped="0" time="0.881223">
      <testcase name="test_allows_valid_order" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="28" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="4" time="0.051233"/>
      <testcase name="test_denies_insufficient_inventory" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="115" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="4" time="0.047457">
        <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_denies_insufficient_inventory&#13;
Failed asserting that an array contains 'Insufficient inventory'.
&#13;
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php:125</failure>
      </testcase>
      <testcase name="test_allows_with_verified_customer" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="128" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="4" time="0.047318"/>
      <testcase name="test_is_applicable_to_orders_only" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="168" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="2" time="0.045851"/>
      <testcase name="test_requires_phone_verification_for_high_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="141" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="5" time="0.043705"/>
      <testcase name="test_can_set_minimum_order_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="181" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="2" time="0.051516"/>
      <testcase name="test_requires_customer_verification" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="88" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="5" time="0.051518"/>
      <testcase name="test_denies_order_with_invalid_status" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="51" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="5" time="0.053623"/>
      <testcase name="test_express_rule_creation" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="229" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="3" time="0.049901"/>
      <testcase name="test_can_disable_inventory_check" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="217" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="2" time="0.045259"/>
      <testcase name="test_can_set_allowed_statuses" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="193" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="2" time="0.045033"/>
      <testcase name="test_denies_order_without_shipping_address" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="76" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="4" time="0.048181"/>
      <testcase name="test_denies_order_without_items" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="64" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="2" time="0.047331">
        <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_denies_order_without_items&#13;
Failed asserting that true is false.
&#13;
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php:71</failure>
      </testcase>
      <testcase name="test_requires_payment_method_selection" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="102" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="5" time="0.044721"/>
      <testcase name="test_denies_non_order_entity" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="156" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="4" time="0.045886">
        <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_denies_non_order_entity&#13;
Failed asserting that 'Order action denied' [ASCII](length: 19) contains "not supported" [ASCII](length: 13).
&#13;
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php:165</failure>
      </testcase>
      <testcase name="test_can_disable_customer_verification" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="204" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="2" time="0.046199"/>
      <testcase name="test_denies_order_below_minimum_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="38" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="5" time="0.050994"/>
      <testcase name="test_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php" line="174" class="Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderValidationRuleTest" assertions="3" time="0.065497"/>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" tests="28" assertions="0" errors="28" failures="0" skipped="0" time="0.000000">
      <testcase name="test_can_set_approval_required_transitions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="379" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_can_set_approval_required_transitions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_strict_rule_creation" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="404" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_strict_rule_creation&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="329" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_rule_properties&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_refund_after_period_expired" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="241" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_refund_after_period_expired&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_valid_transition_processing_to_shipped" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="61" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_processing_to_shipped&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_cancellation_from_shipped" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="132" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_cancellation_from_shipped&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_cancellation_from_confirmed" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="118" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_cancellation_from_confirmed&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_can_set_restricted_transitions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="363" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_can_set_restricted_transitions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_transition_with_approval" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="313" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_transition_with_approval&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_same_status_transition" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="256" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_same_status_transition&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_standard_rule_creation" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="395" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_standard_rule_creation&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_valid_transition_confirmed_to_processing" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="46" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_confirmed_to_processing&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_shipping_without_address" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="197" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_shipping_without_address&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_valid_transition_shipped_to_delivered" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="76" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_shipped_to_delivered&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_refund_from_delivered" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="212" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_refund_from_delivered&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_returns_next_allowed_transitions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="413" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_returns_next_allowed_transitions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_cancellation_from_pending" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="104" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_cancellation_from_pending&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_confirmation_without_payment" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="161" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_confirmation_without_payment&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_transition_for_non_order_entity" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="283" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_transition_for_non_order_entity&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_transition_without_target_status" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="271" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_transition_without_target_status&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_is_applicable_with_target_status" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="336" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_is_applicable_with_target_status&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_processing_without_stock" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="176" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_processing_without_stock&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_can_set_allowed_transitions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="343" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_can_set_allowed_transitions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_refund_from_non_delivered" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="227" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_refund_from_non_delivered&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_valid_transition_pending_to_confirmed" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="30" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_pending_to_confirmed&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_denies_cancellation_from_delivered" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="147" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_denies_cancellation_from_delivered&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_allows_valid_transition_delivered_to_completed" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="90" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_allows_valid_transition_delivered_to_completed&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
      <testcase name="test_requires_approval_for_specific_transitions" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php" line="297" class="Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Order.OrderStatusTransitionRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest::test_requires_approval_for_specific_transitions&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27</error>
      </testcase>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" tests="20" assertions="0" errors="20" failures="0" skipped="0" time="0.000000">
      <testcase name="test_custom_priority" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="391" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_custom_priority&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_weight_based_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="348" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_weight_based_rule_properties&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_weight_based_shipping_rule_second_tier" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="132" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_weight_based_shipping_rule_second_tier&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_amount_based_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="357" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_amount_based_rule_properties&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_amount_based_shipping_rule_without_order_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="279" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_amount_based_shipping_rule_without_order_amount&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_weight_based_shipping_rule_without_weight" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="163" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_weight_based_shipping_rule_without_weight&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_free_shipping_rule_without_minimum" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="26" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_free_shipping_rule_without_minimum&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_unknown_rule_type_returns_error" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="326" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_unknown_rule_type_returns_error&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_amount_based_shipping_rule_second_tier" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="248" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_amount_based_shipping_rule_second_tier&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_get_cost_rules" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="376" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_get_cost_rules&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_free_shipping_rule_with_minimum_amount_met" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="44" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_free_shipping_rule_with_minimum_amount_met&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_free_shipping_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="366" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_free_shipping_rule_properties&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_weight_based_shipping_rule" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="94" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_weight_based_shipping_rule&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_weight_based_shipping_rule_no_applicable_tier" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="185" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_weight_based_shipping_rule_no_applicable_tier&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_amount_based_shipping_rule_no_applicable_tier_uses_base_cost" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="301" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_amount_based_shipping_rule_no_applicable_tier_uses_base_cost&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_free_shipping_rule_with_minimum_amount_not_met" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="60" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_free_shipping_rule_with_minimum_amount_not_met&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_rule_properties" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="337" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_rule_properties&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_fixed_shipping_cost_rule" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="77" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_fixed_shipping_cost_rule&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_amount_based_shipping_rule" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="210" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_amount_based_shipping_rule&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
      <testcase name="test_check_applicability" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php" line="398" class="Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest" classname="Tests.Unit.Domain.Shared.Rules.Shipping.ShippingCostRuleTest" assertions="0" time="0.000000">
        <error type="ArgumentCountError">Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest::test_check_applicability&#13;
ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected
&#13;
C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412
C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23</error>
      </testcase>
    </testsuite>
  </testsuite>
</testsuites>
