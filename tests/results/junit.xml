<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="CLI Arguments" tests="19" assertions="32" errors="0" failures="0" skipped="0" time="1.887868">
    <testsuite name="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" tests="13" assertions="17" errors="0" failures="0" skipped="0" time="1.525023">
      <testcase name="it_calculates_bulk_price_with_discount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="105" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.552379"/>
      <testcase name="it_calculates_average_price" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="196" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.112332"/>
      <testcase name="it_calculates_discount_percentage" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="75" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.120280"/>
      <testcase name="it_compares_prices_correctly" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="149" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="3" time="0.126931"/>
      <testcase name="it_finds_lowest_price" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="162" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.091647"/>
      <testcase name="it_calculates_discount_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="90" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.103650"/>
      <testcase name="it_finds_highest_price" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="179" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.065284"/>
      <testcase name="it_calculates_current_price_with_active_sale" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="46" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.058259"/>
      <testcase name="it_calculates_variant_price" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="61" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.060314"/>
      <testcase name="it_calculates_price_with_tax" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="121" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.053816"/>
      <testcase name="it_checks_if_price_is_in_range" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="213" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="2" time="0.059972"/>
      <testcase name="it_calculates_tax_amount" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="135" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="1" time="0.055882"/>
      <testcase name="it_calculates_current_price_without_sale" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php" line="32" class="Tests\Unit\Domain\Products\Services\PricingDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.PricingDomainServiceTest" assertions="2" time="0.064277"/>
    </testsuite>
    <testsuite name="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" tests="6" assertions="15" errors="0" failures="0" skipped="0" time="0.362845">
      <testcase name="it_can_update_product_basic_info" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="134" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="3" time="0.064514"/>
      <testcase name="it_throws_exception_when_sku_already_exists" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="74" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="2" time="0.057240"/>
      <testcase name="it_throws_exception_when_slug_already_exists" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="101" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="2" time="0.070701"/>
      <testcase name="it_can_create_a_product" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="34" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="6" time="0.054338"/>
      <testcase name="it_can_update_product_price" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="159" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="1" time="0.050825"/>
      <testcase name="it_can_increment_view_count" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="174" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="1" time="0.065227"/>
    </testsuite>
  </testsuite>
</testsuites>
