##teamcity[testCount count='189' flowId='23684']
##teamcity[testSuiteStarted name='CLI Arguments' flowId='23684']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest' flowId='23684']
##teamcity[testFailed name='test_strict_rule_creation' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='0' flowId='23684']
##teamcity[testFailed name='test_time_limit_exceeded_completely_denied' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='57' flowId='23684']
##teamcity[testFailed name='test_allows_cancellation_for_pending_order' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='113' flowId='23684']
##teamcity[testFailed name='test_denies_cancellation_for_shipped_order' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='182' flowId='23684']
##teamcity[testFailed name='test_flexible_rule_creation' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='243' flowId='23684']
##teamcity[testFailed name='test_denies_cancellation_for_completed_order' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='300' flowId='23684']
##teamcity[testFailed name='test_calculates_cancellation_fee_for_high_amount_order' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='353' flowId='23684']
##teamcity[testFailed name='test_standard_rule_creation' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='409' flowId='23684']
##teamcity[testFailed name='test_can_set_cancellation_fee_threshold' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='458' flowId='23684']
##teamcity[testFailed name='test_is_applicable_for_cancel_action' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='514' flowId='23684']
##teamcity[testFailed name='test_rule_properties' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='566' flowId='23684']
##teamcity[testFailed name='test_allows_cancellation_for_confirmed_order' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='619' flowId='23684']
##teamcity[testFailed name='test_can_set_cancellation_time_limit' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='689' flowId='23684']
##teamcity[testFailed name='test_requires_refund_for_paid_order' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='746' flowId='23684']
##teamcity[testFailed name='test_can_set_cancellable_statuses' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='800' flowId='23684']
##teamcity[testFailed name='test_time_limit_exceeded_requires_approval' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='848' flowId='23684']
##teamcity[testFailed name='test_requires_cancellation_reason' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='895' flowId='23684']
##teamcity[testFailed name='test_no_cancellation_fee_for_low_amount_order' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='948' flowId='23684']
##teamcity[testFailed name='test_can_set_cancellation_fee_percentage' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='1001' flowId='23684']
##teamcity[testFailed name='test_denies_cancellation_for_non_order_entity' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='1054' flowId='23684']
##teamcity[testFailed name='test_requires_inventory_restoration_for_processing_order' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='1110' flowId='23684']
##teamcity[testFailed name='test_denies_cancellation_for_delivered_order' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='1171' flowId='23684']
##teamcity[testFailed name='test_time_limit_check_within_limit' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php on line 395 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:395|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest.php:26|n' duration='1223' flowId='23684']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Order\OrderCancellationRuleTest' flowId='23684']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest' flowId='23684']
##teamcity[testFailed name='test_gets_specific_rule' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1297' flowId='23684']
##teamcity[testFailed name='test_checks_multiple_products' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1363' flowId='23684']
##teamcity[testFailed name='test_inventory_policy_result_methods' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1419' flowId='23684']
##teamcity[testFailed name='test_can_register_and_unregister_rules' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1469' flowId='23684']
##teamcity[testFailed name='test_checks_stock_successfully' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1521' flowId='23684']
##teamcity[testFailed name='test_health_check_passes_with_rules' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1570' flowId='23684']
##teamcity[testFailed name='test_checks_stock_with_insufficient_quantity' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1617' flowId='23684']
##teamcity[testFailed name='test_gets_backorder_info' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1662' flowId='23684']
##teamcity[testFailed name='test_provides_service_statistics' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1711' flowId='23684']
##teamcity[testFailed name='test_detects_reorder_requirement' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1761' flowId='23684']
##teamcity[testFailed name='test_can_reserve_stock' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1816' flowId='23684']
##teamcity[testFailed name='test_gets_stock_warnings' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1889' flowId='23684']
##teamcity[testFailed name='test_service_initializes_correctly' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1945' flowId='23684']
##teamcity[testFailed name='test_health_check_fails_without_rules' message='TypeError: App\Domain\Products\ValueObjects\Price::__construct(): Argument #1 ($amount) must be of type float, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php on line 39' details='C:\laragon\www\modularecommerce\app\Domain\Products\ValueObjects\Price.php:12|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest.php:39|n' duration='1996' flowId='23684']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Inventory\InventoryPolicyServiceTest' flowId='23684']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest' flowId='23684']
##teamcity[testFailed name='test_can_register_and_unregister_rules' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2068' flowId='23684']
##teamcity[testFailed name='test_validates_payment_with_minimum_amount' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2128' flowId='23684']
##teamcity[testFailed name='test_can_check_rule_existence' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2207' flowId='23684']
##teamcity[testFailed name='test_security_check_with_international_payment' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2302' flowId='23684']
##teamcity[testFailed name='test_can_clear_and_reload_rules' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2376' flowId='23684']
##teamcity[testFailed name='test_security_check_with_rapid_transactions' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2469' flowId='23684']
##teamcity[testFailed name='test_validates_payment_with_invalid_method' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2536' flowId='23684']
##teamcity[testFailed name='test_can_validate_refund' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2600' flowId='23684']
##teamcity[testFailed name='test_can_get_rules_by_priority' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2654' flowId='23684']
##teamcity[testFailed name='test_security_check_with_high_amount' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2703' flowId='23684']
##teamcity[testFailed name='test_can_get_specific_rule' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2750' flowId='23684']
##teamcity[testFailed name='test_validates_refund_with_conditions' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2799' flowId='23684']
##teamcity[testFailed name='test_can_validate_payment' message='ArgumentCountError: Too few arguments to function App\Domain\Orders\Entities\Order::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php on line 202 and at least 5 expected' details='C:\laragon\www\modularecommerce\app\Domain\Orders\Entities\Order.php:49|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:202|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest.php:23|n' duration='2846' flowId='23684']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Payment\PaymentPolicyServiceTest' flowId='23684']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest' flowId='23684']
##teamcity[testStarted name='test_loads_standard_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_loads_standard_rules' flowId='23684']
##teamcity[testFinished name='test_loads_standard_rules' duration='14' flowId='23684']
##teamcity[testStarted name='test_health_check_passes_with_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_health_check_passes_with_rules' flowId='23684']
##teamcity[testFinished name='test_health_check_passes_with_rules' duration='2' flowId='23684']
##teamcity[testStarted name='test_service_initializes_correctly' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_service_initializes_correctly' flowId='23684']
##teamcity[testFinished name='test_service_initializes_correctly' duration='2' flowId='23684']
##teamcity[testStarted name='test_calculates_price_with_tax' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_calculates_price_with_tax' flowId='23684']
##teamcity[testFinished name='test_calculates_price_with_tax' duration='5' flowId='23684']
##teamcity[testStarted name='test_can_unregister_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_can_unregister_rules' flowId='23684']
##teamcity[testFinished name='test_can_unregister_rules' duration='2' flowId='23684']
##teamcity[testStarted name='test_calculates_price_with_quantity_discount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_calculates_price_with_quantity_discount' flowId='23684']
##teamcity[testFinished name='test_calculates_price_with_quantity_discount' duration='3' flowId='23684']
##teamcity[testStarted name='test_validates_pricing' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_validates_pricing' flowId='23684']
##teamcity[testFinished name='test_validates_pricing' duration='2' flowId='23684']
##teamcity[testStarted name='test_health_check_fails_without_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_health_check_fails_without_rules' flowId='23684']
##teamcity[testFinished name='test_health_check_fails_without_rules' duration='2' flowId='23684']
##teamcity[testStarted name='test_provides_service_statistics' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_provides_service_statistics' flowId='23684']
##teamcity[testFinished name='test_provides_service_statistics' duration='4' flowId='23684']
##teamcity[testStarted name='test_calculates_price_with_multiple_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_calculates_price_with_multiple_rules' flowId='23684']
##teamcity[testFinished name='test_calculates_price_with_multiple_rules' duration='3' flowId='23684']
##teamcity[testStarted name='test_can_register_pricing_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest::test_can_register_pricing_rules' flowId='23684']
##teamcity[testFinished name='test_can_register_pricing_rules' duration='2' flowId='23684']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Pricing\PricingPolicyServiceTest' flowId='23684']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest' flowId='23684']
##teamcity[testStarted name='test_handles_non_product_entity' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_handles_non_product_entity' flowId='23684']
##teamcity[testFinished name='test_handles_non_product_entity' duration='16' flowId='23684']
##teamcity[testStarted name='test_handles_insufficient_stock_with_backorder_allowed' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_handles_insufficient_stock_with_backorder_allowed' flowId='23684']
##teamcity[testFinished name='test_handles_insufficient_stock_with_backorder_allowed' duration='2' flowId='23684']
##teamcity[testStarted name='test_not_applicable_for_digital_products' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_not_applicable_for_digital_products' flowId='23684']
##teamcity[testFinished name='test_not_applicable_for_digital_products' duration='17' flowId='23684']
##teamcity[testStarted name='test_validates_sufficient_stock' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_validates_sufficient_stock' flowId='23684']
##teamcity[testFinished name='test_validates_sufficient_stock' duration='2' flowId='23684']
##teamcity[testStarted name='test_default_quantity_when_not_specified' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_default_quantity_when_not_specified' flowId='23684']
##teamcity[testFinished name='test_default_quantity_when_not_specified' duration='2' flowId='23684']
##teamcity[testStarted name='test_handles_insufficient_stock_without_backorder' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_handles_insufficient_stock_without_backorder' flowId='23684']
##teamcity[testFinished name='test_handles_insufficient_stock_without_backorder' duration='2' flowId='23684']
##teamcity[testStarted name='test_stock_critical_level_detection' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_stock_critical_level_detection' flowId='23684']
##teamcity[testFinished name='test_stock_critical_level_detection' duration='2' flowId='23684']
##teamcity[testStarted name='test_stock_not_critical' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_stock_not_critical' flowId='23684']
##teamcity[testFinished name='test_stock_not_critical' duration='2' flowId='23684']
##teamcity[testStarted name='test_configuration_methods' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_configuration_methods' flowId='23684']
##teamcity[testFinished name='test_configuration_methods' duration='2' flowId='23684']
##teamcity[testStarted name='test_rule_properties' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_rule_properties' flowId='23684']
##teamcity[testFinished name='test_rule_properties' duration='3' flowId='23684']
##teamcity[testStarted name='test_not_applicable_when_tracking_disabled' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest::test_not_applicable_when_tracking_disabled' flowId='23684']
##teamcity[testFinished name='test_not_applicable_when_tracking_disabled' duration='4' flowId='23684']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Inventory\StockValidationRuleTest' flowId='23684']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest' flowId='23684']
##teamcity[testStarted name='test_can_check_rule_existence' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_check_rule_existence' flowId='23684']
##teamcity[testFinished name='test_can_check_rule_existence' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_get_rule_result_by_name' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_get_rule_result_by_name' flowId='23684']
##teamcity[testFinished name='test_can_get_rule_result_by_name' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_get_specific_rule' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_get_specific_rule' flowId='23684']
##teamcity[testFinished name='test_can_get_specific_rule' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_validate_cancellation' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_validate_cancellation' flowId='23684']
##teamcity[testFinished name='test_can_validate_cancellation' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_validate_order' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_validate_order' flowId='23684']
##teamcity[testFinished name='test_can_validate_order' duration='2' flowId='23684']
##teamcity[testStarted name='test_validates_cancellation_for_shipped_order' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_validates_cancellation_for_shipped_order' flowId='23684']
##teamcity[testFinished name='test_validates_cancellation_for_shipped_order' duration='2' flowId='23684']
##teamcity[testStarted name='test_validates_status_transition_invalid' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_validates_status_transition_invalid' flowId='23684']
##teamcity[testFinished name='test_validates_status_transition_invalid' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_register_and_unregister_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_register_and_unregister_rules' flowId='23684']
##teamcity[testFailed name='test_can_register_and_unregister_rules' message='Failed asserting that actual size 5 matches expected size 4.' details='C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php:69|n' duration='1' flowId='23684']
##teamcity[testFinished name='test_can_register_and_unregister_rules' duration='5' flowId='23684']
##teamcity[testStarted name='test_order_requires_actions' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_order_requires_actions' flowId='23684']
##teamcity[testFinished name='test_order_requires_actions' duration='2' flowId='23684']
##teamcity[testStarted name='test_validates_order_with_insufficient_amount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_validates_order_with_insufficient_amount' flowId='23684']
##teamcity[testFinished name='test_validates_order_with_insufficient_amount' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_get_rules_by_priority' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_get_rules_by_priority' flowId='23684']
##teamcity[testFailed name='test_can_get_rules_by_priority' message='Failed asserting that actual size 5 matches expected size 3.' details='C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php:81|n' duration='0' flowId='23684']
##teamcity[testFinished name='test_can_get_rules_by_priority' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_clear_and_reload_rules' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_clear_and_reload_rules' flowId='23684']
##teamcity[testFailed name='test_can_clear_and_reload_rules' message='Failed asserting that actual size 5 matches expected size 3.' details='C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php:113|n' duration='0' flowId='23684']
##teamcity[testFinished name='test_can_clear_and_reload_rules' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_validate_status_transition' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_can_validate_status_transition' flowId='23684']
##teamcity[testFinished name='test_can_validate_status_transition' duration='2' flowId='23684']
##teamcity[testStarted name='test_result_to_array' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_result_to_array' flowId='23684']
##teamcity[testFinished name='test_result_to_array' duration='2' flowId='23684']
##teamcity[testStarted name='test_validates_order_without_items' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest::test_validates_order_without_items' flowId='23684']
##teamcity[testFailed name='test_validates_order_without_items' message='Failed asserting that true is false.' details='C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest.php:139|n' duration='1' flowId='23684']
##teamcity[testFinished name='test_validates_order_without_items' duration='2' flowId='23684']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Order\OrderPolicyServiceTest' flowId='23684']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest' flowId='23684']
##teamcity[testFailed name='test_usage_limit_exceeded_returns_no_discount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='0' flowId='23684']
##teamcity[testFailed name='test_is_applicable_with_matching_coupon_code' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='55' flowId='23684']
##teamcity[testFailed name='test_get_discount_amount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='109' flowId='23684']
##teamcity[testFailed name='test_not_yet_valid_coupon_returns_no_discount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='163' flowId='23684']
##teamcity[testFailed name='test_percentage_coupon_applies_discount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='211' flowId='23684']
##teamcity[testFailed name='test_fixed_amount_coupon_applies_discount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='259' flowId='23684']
##teamcity[testFailed name='test_coupon_code_mismatch_returns_no_discount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='310' flowId='23684']
##teamcity[testFailed name='test_minimum_amount_not_met_returns_no_discount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='360' flowId='23684']
##teamcity[testFailed name='test_custom_priority' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='406' flowId='23684']
##teamcity[testFailed name='test_minimum_amount_met_applies_discount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='455' flowId='23684']
##teamcity[testFailed name='test_get_used_count' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='503' flowId='23684']
##teamcity[testFailed name='test_fixed_coupon_rule_properties' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='553' flowId='23684']
##teamcity[testFailed name='test_is_valid_method' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='603' flowId='23684']
##teamcity[testFailed name='test_rule_properties' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='652' flowId='23684']
##teamcity[testFailed name='test_check_applicability' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='698' flowId='23684']
##teamcity[testFailed name='test_get_coupon_code' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='744' flowId='23684']
##teamcity[testFailed name='test_expired_coupon_returns_no_discount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='792' flowId='23684']
##teamcity[testFailed name='test_percentage_validation_bounds' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='837' flowId='23684']
##teamcity[testFailed name='test_get_usage_limit' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='882' flowId='23684']
##teamcity[testFailed name='test_get_discount_percentage' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='935' flowId='23684']
##teamcity[testFailed name='test_get_minimum_amount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='982' flowId='23684']
##teamcity[testFailed name='test_usage_limit_not_exceeded_applies_discount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='1037' flowId='23684']
##teamcity[testFailed name='test_is_applicable_with_coupon_context_key' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php on line 498 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:498|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest.php:24|n' duration='1082' flowId='23684']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Discount\CouponValidationRuleTest' flowId='23684']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest' flowId='23684']
##teamcity[testFailed name='test_can_set_allowed_methods' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1142' flowId='23684']
##teamcity[testFailed name='test_can_set_minimum_amount' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1191' flowId='23684']
##teamcity[testFailed name='test_denies_non_order_entity' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1239' flowId='23684']
##teamcity[testFailed name='test_denies_payment_for_invalid_order_status' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1285' flowId='23684']
##teamcity[testFailed name='test_allows_valid_payment' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1338' flowId='23684']
##teamcity[testFailed name='test_is_applicable_to_orders_only' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1382' flowId='23684']
##teamcity[testFailed name='test_requires_additional_verification_for_very_high_amount' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1429' flowId='23684']
##teamcity[testFailed name='test_strict_rule_creation' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1474' flowId='23684']
##teamcity[testFailed name='test_standard_rule_creation' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1519' flowId='23684']
##teamcity[testFailed name='test_denies_invalid_payment_method' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1564' flowId='23684']
##teamcity[testFailed name='test_denies_payment_below_minimum' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1610' flowId='23684']
##teamcity[testFailed name='test_rule_properties' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1657' flowId='23684']
##teamcity[testFailed name='test_requires_verification_for_high_amount' message='TypeError: App\Domain\Shared\Rules\Payment\PaymentValidationRule::__construct(): Argument #1 ($priority) must be of type int, App\Core\Domain\ValueObjects\Money given, called in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php on line 20' details='C:\laragon\www\modularecommerce\app\Domain\Shared\Rules\Payment\PaymentValidationRule.php:21|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest.php:20|n' duration='1703' flowId='23684']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Payment\PaymentValidationRuleTest' flowId='23684']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest' flowId='23684']
##teamcity[testStarted name='test_allows_valid_order' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_allows_valid_order' flowId='23684']
##teamcity[testFinished name='test_allows_valid_order' duration='2' flowId='23684']
##teamcity[testStarted name='test_denies_insufficient_inventory' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_denies_insufficient_inventory' flowId='23684']
##teamcity[testFailed name='test_denies_insufficient_inventory' message='Failed asserting that an array contains |'Insufficient inventory|'.' details='C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php:125|n' duration='0' flowId='23684']
##teamcity[testFinished name='test_denies_insufficient_inventory' duration='2' flowId='23684']
##teamcity[testStarted name='test_allows_with_verified_customer' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_allows_with_verified_customer' flowId='23684']
##teamcity[testFinished name='test_allows_with_verified_customer' duration='2' flowId='23684']
##teamcity[testStarted name='test_is_applicable_to_orders_only' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_is_applicable_to_orders_only' flowId='23684']
##teamcity[testFinished name='test_is_applicable_to_orders_only' duration='2' flowId='23684']
##teamcity[testStarted name='test_requires_phone_verification_for_high_amount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_requires_phone_verification_for_high_amount' flowId='23684']
##teamcity[testFinished name='test_requires_phone_verification_for_high_amount' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_set_minimum_order_amount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_can_set_minimum_order_amount' flowId='23684']
##teamcity[testFinished name='test_can_set_minimum_order_amount' duration='2' flowId='23684']
##teamcity[testStarted name='test_requires_customer_verification' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_requires_customer_verification' flowId='23684']
##teamcity[testFinished name='test_requires_customer_verification' duration='2' flowId='23684']
##teamcity[testStarted name='test_denies_order_with_invalid_status' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_denies_order_with_invalid_status' flowId='23684']
##teamcity[testFinished name='test_denies_order_with_invalid_status' duration='2' flowId='23684']
##teamcity[testStarted name='test_express_rule_creation' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_express_rule_creation' flowId='23684']
##teamcity[testFinished name='test_express_rule_creation' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_disable_inventory_check' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_can_disable_inventory_check' flowId='23684']
##teamcity[testFinished name='test_can_disable_inventory_check' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_set_allowed_statuses' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_can_set_allowed_statuses' flowId='23684']
##teamcity[testFinished name='test_can_set_allowed_statuses' duration='2' flowId='23684']
##teamcity[testStarted name='test_denies_order_without_shipping_address' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_denies_order_without_shipping_address' flowId='23684']
##teamcity[testFinished name='test_denies_order_without_shipping_address' duration='2' flowId='23684']
##teamcity[testStarted name='test_denies_order_without_items' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_denies_order_without_items' flowId='23684']
##teamcity[testFailed name='test_denies_order_without_items' message='Failed asserting that true is false.' details='C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php:71|n' duration='0' flowId='23684']
##teamcity[testFinished name='test_denies_order_without_items' duration='2' flowId='23684']
##teamcity[testStarted name='test_requires_payment_method_selection' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_requires_payment_method_selection' flowId='23684']
##teamcity[testFinished name='test_requires_payment_method_selection' duration='3' flowId='23684']
##teamcity[testStarted name='test_denies_non_order_entity' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_denies_non_order_entity' flowId='23684']
##teamcity[testFailed name='test_denies_non_order_entity' message='Failed asserting that |'Order action denied|' |[ASCII|](length: 19) contains "not supported" |[ASCII|](length: 13).' details='C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php:165|n' duration='0' flowId='23684']
##teamcity[testFinished name='test_denies_non_order_entity' duration='2' flowId='23684']
##teamcity[testStarted name='test_can_disable_customer_verification' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_can_disable_customer_verification' flowId='23684']
##teamcity[testFinished name='test_can_disable_customer_verification' duration='2' flowId='23684']
##teamcity[testStarted name='test_denies_order_below_minimum_amount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_denies_order_below_minimum_amount' flowId='23684']
##teamcity[testFinished name='test_denies_order_below_minimum_amount' duration='2' flowId='23684']
##teamcity[testStarted name='test_rule_properties' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest::test_rule_properties' flowId='23684']
##teamcity[testFinished name='test_rule_properties' duration='2' flowId='23684']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Order\OrderValidationRuleTest' flowId='23684']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest' flowId='23684']
##teamcity[testFailed name='test_can_set_approval_required_transitions' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='0' flowId='23684']
##teamcity[testFailed name='test_strict_rule_creation' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='46' flowId='23684']
##teamcity[testFailed name='test_rule_properties' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='91' flowId='23684']
##teamcity[testFailed name='test_denies_refund_after_period_expired' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='139' flowId='23684']
##teamcity[testFailed name='test_allows_valid_transition_processing_to_shipped' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='185' flowId='23684']
##teamcity[testFailed name='test_denies_cancellation_from_shipped' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='232' flowId='23684']
##teamcity[testFailed name='test_allows_cancellation_from_confirmed' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='278' flowId='23684']
##teamcity[testFailed name='test_can_set_restricted_transitions' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='324' flowId='23684']
##teamcity[testFailed name='test_allows_transition_with_approval' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='372' flowId='23684']
##teamcity[testFailed name='test_allows_same_status_transition' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='419' flowId='23684']
##teamcity[testFailed name='test_standard_rule_creation' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='468' flowId='23684']
##teamcity[testFailed name='test_allows_valid_transition_confirmed_to_processing' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='519' flowId='23684']
##teamcity[testFailed name='test_denies_shipping_without_address' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='570' flowId='23684']
##teamcity[testFailed name='test_allows_valid_transition_shipped_to_delivered' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='617' flowId='23684']
##teamcity[testFailed name='test_allows_refund_from_delivered' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='666' flowId='23684']
##teamcity[testFailed name='test_returns_next_allowed_transitions' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='718' flowId='23684']
##teamcity[testFailed name='test_allows_cancellation_from_pending' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='769' flowId='23684']
##teamcity[testFailed name='test_denies_confirmation_without_payment' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='819' flowId='23684']
##teamcity[testFailed name='test_denies_transition_for_non_order_entity' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='868' flowId='23684']
##teamcity[testFailed name='test_denies_transition_without_target_status' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='919' flowId='23684']
##teamcity[testFailed name='test_is_applicable_with_target_status' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='967' flowId='23684']
##teamcity[testFailed name='test_denies_processing_without_stock' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1015' flowId='23684']
##teamcity[testFailed name='test_can_set_allowed_transitions' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1073' flowId='23684']
##teamcity[testFailed name='test_denies_refund_from_non_delivered' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1125' flowId='23684']
##teamcity[testFailed name='test_allows_valid_transition_pending_to_confirmed' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1172' flowId='23684']
##teamcity[testFailed name='test_denies_cancellation_from_delivered' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1218' flowId='23684']
##teamcity[testFailed name='test_allows_valid_transition_delivered_to_completed' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1265' flowId='23684']
##teamcity[testFailed name='test_requires_approval_for_specific_transitions' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php on line 437 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:437|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest.php:27|n' duration='1314' flowId='23684']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Order\OrderStatusTransitionRuleTest' flowId='23684']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php::\Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest' flowId='23684']
##teamcity[testFailed name='test_custom_priority' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1375' flowId='23684']
##teamcity[testFailed name='test_weight_based_rule_properties' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1422' flowId='23684']
##teamcity[testFailed name='test_weight_based_shipping_rule_second_tier' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1470' flowId='23684']
##teamcity[testFailed name='test_amount_based_rule_properties' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1518' flowId='23684']
##teamcity[testFailed name='test_amount_based_shipping_rule_without_order_amount' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1570' flowId='23684']
##teamcity[testFailed name='test_weight_based_shipping_rule_without_weight' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1621' flowId='23684']
##teamcity[testFailed name='test_free_shipping_rule_without_minimum' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1675' flowId='23684']
##teamcity[testFailed name='test_unknown_rule_type_returns_error' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1726' flowId='23684']
##teamcity[testFailed name='test_amount_based_shipping_rule_second_tier' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1776' flowId='23684']
##teamcity[testFailed name='test_get_cost_rules' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1821' flowId='23684']
##teamcity[testFailed name='test_free_shipping_rule_with_minimum_amount_met' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1866' flowId='23684']
##teamcity[testFailed name='test_free_shipping_rule_properties' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1911' flowId='23684']
##teamcity[testFailed name='test_weight_based_shipping_rule' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='1959' flowId='23684']
##teamcity[testFailed name='test_weight_based_shipping_rule_no_applicable_tier' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='2003' flowId='23684']
##teamcity[testFailed name='test_amount_based_shipping_rule_no_applicable_tier_uses_base_cost' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='2046' flowId='23684']
##teamcity[testFailed name='test_free_shipping_rule_with_minimum_amount_not_met' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='2089' flowId='23684']
##teamcity[testFailed name='test_rule_properties' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='2134' flowId='23684']
##teamcity[testFailed name='test_fixed_shipping_cost_rule' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='2178' flowId='23684']
##teamcity[testFailed name='test_amount_based_shipping_rule' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='2224' flowId='23684']
##teamcity[testFailed name='test_check_applicability' message='ArgumentCountError: Too few arguments to function App\Domain\Products\Entities\Product::__construct(), 0 passed in C:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php on line 412 and at least 6 expected' details='C:\laragon\www\modularecommerce\app\Domain\Products\Entities\Product.php:47|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:412|nC:\laragon\www\modularecommerce\tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest.php:23|n' duration='2272' flowId='23684']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Shared\Rules\Shipping\ShippingCostRuleTest' flowId='23684']
##teamcity[testSuiteFinished name='CLI Arguments' flowId='23684']
