##teamcity[testCount count='19' flowId='25504']
##teamcity[testSuiteStarted name='CLI Arguments' flowId='25504']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Products\Services\PricingDomainServiceTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest' flowId='25504']
##teamcity[testStarted name='it_calculates_bulk_price_with_discount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_bulk_price_with_discount' flowId='25504']
##teamcity[testFinished name='it_calculates_bulk_price_with_discount' duration='32' flowId='25504']
##teamcity[testStarted name='it_calculates_average_price' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_average_price' flowId='25504']
##teamcity[testFinished name='it_calculates_average_price' duration='4' flowId='25504']
##teamcity[testStarted name='it_calculates_discount_percentage' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_discount_percentage' flowId='25504']
##teamcity[testFinished name='it_calculates_discount_percentage' duration='10' flowId='25504']
##teamcity[testStarted name='it_compares_prices_correctly' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_compares_prices_correctly' flowId='25504']
##teamcity[testFinished name='it_compares_prices_correctly' duration='3' flowId='25504']
##teamcity[testStarted name='it_finds_lowest_price' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_finds_lowest_price' flowId='25504']
##teamcity[testFinished name='it_finds_lowest_price' duration='7' flowId='25504']
##teamcity[testStarted name='it_calculates_discount_amount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_discount_amount' flowId='25504']
##teamcity[testFinished name='it_calculates_discount_amount' duration='3' flowId='25504']
##teamcity[testStarted name='it_finds_highest_price' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_finds_highest_price' flowId='25504']
##teamcity[testFinished name='it_finds_highest_price' duration='2' flowId='25504']
##teamcity[testStarted name='it_calculates_current_price_with_active_sale' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_current_price_with_active_sale' flowId='25504']
##teamcity[testFinished name='it_calculates_current_price_with_active_sale' duration='3' flowId='25504']
##teamcity[testStarted name='it_calculates_variant_price' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_variant_price' flowId='25504']
##teamcity[testFinished name='it_calculates_variant_price' duration='4' flowId='25504']
##teamcity[testStarted name='it_calculates_price_with_tax' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_price_with_tax' flowId='25504']
##teamcity[testFinished name='it_calculates_price_with_tax' duration='2' flowId='25504']
##teamcity[testStarted name='it_checks_if_price_is_in_range' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_checks_if_price_is_in_range' flowId='25504']
##teamcity[testFinished name='it_checks_if_price_is_in_range' duration='3' flowId='25504']
##teamcity[testStarted name='it_calculates_tax_amount' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_tax_amount' flowId='25504']
##teamcity[testFinished name='it_calculates_tax_amount' duration='2' flowId='25504']
##teamcity[testStarted name='it_calculates_current_price_without_sale' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\PricingDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\PricingDomainServiceTest::it_calculates_current_price_without_sale' flowId='25504']
##teamcity[testFinished name='it_calculates_current_price_without_sale' duration='2' flowId='25504']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Products\Services\PricingDomainServiceTest' flowId='25504']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Products\Services\ProductDomainServiceTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest' flowId='25504']
##teamcity[testStarted name='it_can_update_product_basic_info' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_can_update_product_basic_info' flowId='25504']
##teamcity[testFinished name='it_can_update_product_basic_info' duration='6' flowId='25504']
##teamcity[testStarted name='it_throws_exception_when_sku_already_exists' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_throws_exception_when_sku_already_exists' flowId='25504']
##teamcity[testFinished name='it_throws_exception_when_sku_already_exists' duration='7' flowId='25504']
##teamcity[testStarted name='it_throws_exception_when_slug_already_exists' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_throws_exception_when_slug_already_exists' flowId='25504']
##teamcity[testFinished name='it_throws_exception_when_slug_already_exists' duration='2' flowId='25504']
##teamcity[testStarted name='it_can_create_a_product' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_can_create_a_product' flowId='25504']
##teamcity[testFinished name='it_can_create_a_product' duration='3' flowId='25504']
##teamcity[testStarted name='it_can_update_product_price' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_can_update_product_price' flowId='25504']
##teamcity[testFinished name='it_can_update_product_price' duration='2' flowId='25504']
##teamcity[testStarted name='it_can_increment_view_count' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_can_increment_view_count' flowId='25504']
##teamcity[testFinished name='it_can_increment_view_count' duration='3' flowId='25504']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Products\Services\ProductDomainServiceTest' flowId='25504']
##teamcity[testSuiteFinished name='CLI Arguments' flowId='25504']
