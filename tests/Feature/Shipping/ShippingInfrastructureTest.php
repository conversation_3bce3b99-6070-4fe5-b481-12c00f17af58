<?php

namespace Tests\Feature\Shipping;

use Tests\TestCase;
use App\Domain\Shipping\Repositories\ShipmentRepositoryInterface;
use App\Infrastructure\Shipping\Services\ShipmentTrackingService;
use App\Infrastructure\Shipping\Services\CarrierIntegrationService;
use App\Infrastructure\Shipping\Models\EloquentShipment;
use App\Infrastructure\Shipping\Models\EloquentCarrierIntegration;
use App\Models\Order;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * ShippingInfrastructureTest
 * Shipping Infrastructure Layer feature test'leri
 */
class ShippingInfrastructureTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private ShipmentRepositoryInterface $shipmentRepository;
    private ShipmentTrackingService $trackingService;
    private CarrierIntegrationService $integrationService;
    private EloquentCarrierIntegration $carrier;
    private Order $order;

    protected function setUp(): void
    {
        parent::setUp();

        $this->shipmentRepository = app(ShipmentRepositoryInterface::class);
        $this->trackingService = app(ShipmentTrackingService::class);
        $this->integrationService = app(CarrierIntegrationService::class);

        // Test data oluştur
        $this->createShippingFeatureTestData();
    }

    /** @test */
    public function it_can_manage_complete_shipment_lifecycle()
    {
        // Arrange - Shipment oluştur
        $shipment = EloquentShipment::create([
            'shipment_number' => 'SHP-2024-001',
            'order_id' => $this->order->id,
            'carrier_integration_id' => $this->carrier->id,
            'tracking_number' => 'TEST123456789',
            'origin_address' => [
                'name' => 'Test Sender',
                'address_line_1' => '123 Test Street',
                'city' => 'Istanbul',
                'country' => 'TR'
            ],
            'destination_address' => [
                'name' => 'Test Recipient',
                'address_line_1' => '456 Test Avenue',
                'city' => 'Ankara',
                'country' => 'TR'
            ],
            'package_info' => [
                'weight' => 2.5,
                'weight_unit' => 'kg',
                'length' => 30,
                'width' => 20,
                'height' => 15,
                'dimension_unit' => 'cm'
            ],
            'service_type' => 'standard',
            'status' => 'pending',
            'total_cost' => 25.50,
            'cost_currency' => 'TRY'
        ]);

        // Act & Assert - Tracking event ekle
        $trackingResult = $this->trackingService->addTrackingEvent(
            $shipment->id,
            'picked_up',
            'Package Picked Up',
            'Package has been picked up from sender',
            Carbon::now(),
            ['city' => 'Istanbul', 'country' => 'TR'],
            ['facility_name' => 'Istanbul Hub'],
            'carrier'
        );

        $this->assertEquals('picked_up', $trackingResult['event_code']);
        $this->assertEquals($shipment->id, $trackingResult['shipment_id']);

        // Shipment status'unun güncellendiğini kontrol et
        $shipment->refresh();
        $this->assertEquals('shipped', $shipment->status);

        // Act & Assert - İkinci tracking event ekle
        $this->trackingService->addTrackingEvent(
            $shipment->id,
            'in_transit',
            'In Transit',
            'Package is in transit',
            Carbon::now()->addHours(2),
            ['city' => 'Ankara', 'country' => 'TR'],
            ['facility_name' => 'Ankara Hub'],
            'carrier'
        );

        // Act & Assert - Delivery event ekle
        $this->trackingService->addTrackingEvent(
            $shipment->id,
            'delivered',
            'Package Delivered',
            'Package has been delivered successfully',
            Carbon::now()->addHours(24),
            ['city' => 'Ankara', 'country' => 'TR'],
            ['delivered_to' => 'Test Recipient'],
            'carrier'
        );

        // Final shipment durumunu kontrol et
        $shipment->refresh();
        $this->assertEquals('delivered', $shipment->status);
        $this->assertNotNull($shipment->actual_delivery_at);

        // Tracking geçmişini kontrol et
        $trackingHistory = $this->trackingService->getTrackingHistory($shipment->id);
        $this->assertCount(3, $trackingHistory);
        $this->assertEquals('delivered', $trackingHistory[0]['event_code']); // En son event
    }

    /** @test */
    public function it_can_handle_rate_calculation_and_comparison()
    {
        // Arrange - Mock carrier response (gerçek API çağrısı yapmadan)
        $originAddress = [
            'name' => 'Test Sender',
            'city' => 'Istanbul',
            'country' => 'TR'
        ];

        $destinationAddress = [
            'name' => 'Test Recipient',
            'city' => 'Ankara',
            'country' => 'TR'
        ];

        $packageInfo = [
            'weight' => 2.5,
            'weight_unit' => 'kg',
            'length' => 30,
            'width' => 20,
            'height' => 15,
            'dimension_unit' => 'cm'
        ];

        // Mock adapter behavior için test carrier oluştur
        $testCarrier = EloquentCarrierIntegration::create([
            'carrier_code' => 'test_mock',
            'carrier_name' => 'Test Mock Carrier',
            'integration_type' => 'mock',
            'is_active' => true,
            'supports_rate_calculation' => true,
            'api_credentials' => ['api_key' => 'test_key']
        ]);

        // Rate calculation test (mock olarak)
        try {
            // Bu gerçek bir API çağrısı yapmayacak çünkü mock carrier
            $rateResult = $this->integrationService->calculateShippingRate(
                'test_mock',
                $originAddress,
                $destinationAddress,
                $packageInfo,
                'standard'
            );

            // Mock adapter olmadığı için exception bekliyoruz
            $this->fail('Expected exception for unsupported carrier');
        } catch (\Exception $e) {
            $this->assertStringContainsString('Unsupported carrier', $e->getMessage());
        }

        // Rate calculation kaydının oluşturulduğunu kontrol et (başarılı olsaydı)
        $rateCalculations = DB::table('shipping_rate_calculations')->count();
        $this->assertEquals(0, $rateCalculations); // Mock başarısız olduğu için 0
    }

    /** @test */
    public function it_can_handle_shipment_creation_and_labeling()
    {
        // Arrange
        $originAddress = [
            'name' => 'Test Sender',
            'company' => 'Test Company',
            'address_line_1' => '123 Test Street',
            'city' => 'Istanbul',
            'state' => 'Istanbul',
            'postal_code' => '34000',
            'country' => 'TR',
            'phone' => '+905551234567',
            'email' => '<EMAIL>'
        ];

        $destinationAddress = [
            'name' => 'Test Recipient',
            'address_line_1' => '456 Test Avenue',
            'city' => 'Ankara',
            'state' => 'Ankara',
            'postal_code' => '06000',
            'country' => 'TR',
            'phone' => '+905559876543',
            'email' => '<EMAIL>'
        ];

        $packageInfo = [
            'weight' => 2.5,
            'weight_unit' => 'kg',
            'length' => 30,
            'width' => 20,
            'height' => 15,
            'dimension_unit' => 'cm',
            'package_type' => 'box'
        ];

        $options = [
            'declared_value' => 100.00,
            'insurance_required' => true,
            'signature_required' => false,
            'reference_number' => 'REF-001'
        ];

        // Act & Assert - Shipment creation test (mock olarak)
        try {
            $shipmentResult = $this->integrationService->createShipment(
                $this->carrier->carrier_code,
                $this->order->id,
                $originAddress,
                $destinationAddress,
                $packageInfo,
                'standard',
                $options
            );

            $this->fail('Expected exception for test carrier without real API');
        } catch (\Exception $e) {
            $this->assertStringContainsString('Unsupported carrier', $e->getMessage());
        }

        // Database'de shipment kaydının oluşmadığını kontrol et
        $shipmentsCount = EloquentShipment::count();
        $this->assertEquals(0, $shipmentsCount);
    }

    /** @test */
    public function it_can_handle_tracking_updates_and_notifications()
    {
        // Arrange - Shipment oluştur
        $shipment = EloquentShipment::create([
            'shipment_number' => 'SHP-2024-002',
            'order_id' => $this->order->id,
            'carrier_integration_id' => $this->carrier->id,
            'tracking_number' => 'TRACK123456789',
            'origin_address' => ['city' => 'Istanbul'],
            'destination_address' => ['city' => 'Ankara'],
            'package_info' => ['weight' => 1.0],
            'service_type' => 'express',
            'status' => 'shipped',
            'shipped_at' => Carbon::now()->subHours(2)
        ]);

        // Act - Multiple tracking events ekle
        $events = [
            [
                'code' => 'picked_up',
                'name' => 'Picked Up',
                'description' => 'Package picked up',
                'time' => Carbon::now()->subHours(2)
            ],
            [
                'code' => 'in_transit',
                'name' => 'In Transit',
                'description' => 'Package in transit',
                'time' => Carbon::now()->subHours(1)
            ],
            [
                'code' => 'out_for_delivery',
                'name' => 'Out for Delivery',
                'description' => 'Package out for delivery',
                'time' => Carbon::now()->subMinutes(30)
            ]
        ];

        foreach ($events as $event) {
            $this->trackingService->addTrackingEvent(
                $shipment->id,
                $event['code'],
                $event['name'],
                $event['description'],
                $event['time'],
                ['city' => 'Transit City'],
                ['source' => 'test'],
                'carrier'
            );
        }

        // Assert - Tracking events'lerin kaydedildiğini kontrol et
        $trackingEvents = DB::table('shipment_tracking_events')
            ->where('shipment_id', $shipment->id)
            ->orderBy('event_time', 'desc')
            ->get();

        $this->assertCount(3, $trackingEvents);
        $this->assertEquals('out_for_delivery', $trackingEvents->first()->event_code);

        // Current tracking status'u kontrol et
        $currentStatus = $this->trackingService->getCurrentTrackingStatus($shipment->id);
        $this->assertEquals($shipment->id, $currentStatus['shipment_id']);
        $this->assertEquals('TRACK123456789', $currentStatus['tracking_number']);
        $this->assertEquals('out_for_delivery', $currentStatus['current_status']);
        $this->assertFalse($currentStatus['is_delivered']);
    }

    /** @test */
    public function it_can_handle_delivery_attempts_and_failures()
    {
        // Arrange - Shipment oluştur
        $shipment = EloquentShipment::create([
            'shipment_number' => 'SHP-2024-003',
            'order_id' => $this->order->id,
            'carrier_integration_id' => $this->carrier->id,
            'tracking_number' => 'FAIL123456789',
            'origin_address' => ['city' => 'Istanbul'],
            'destination_address' => ['city' => 'Ankara'],
            'package_info' => ['weight' => 1.5],
            'service_type' => 'standard',
            'status' => 'out_for_delivery',
            'delivery_attempts' => 0
        ]);

        // Act - Failed delivery attempt
        $this->trackingService->addTrackingEvent(
            $shipment->id,
            'delivery_failed',
            'Delivery Failed',
            'Recipient not available',
            Carbon::now(),
            ['city' => 'Ankara'],
            [
                'failure_reason' => 'recipient_not_available',
                'next_attempt_scheduled' => Carbon::now()->addDays(1)->toISOString()
            ],
            'carrier'
        );

        // Delivery attempt kaydı oluştur
        DB::table('delivery_attempts')->insert([
            'shipment_id' => $shipment->id,
            'attempt_number' => 1,
            'attempted_at' => Carbon::now(),
            'attempt_status' => 'failed',
            'failure_reason' => 'recipient_not_available',
            'failure_details' => 'Recipient was not available at delivery address',
            'recipient_available' => false,
            'rescheduled_for' => Carbon::now()->addDays(1),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        // Assert - Delivery attempt kaydının oluştuğunu kontrol et
        $deliveryAttempts = DB::table('delivery_attempts')
            ->where('shipment_id', $shipment->id)
            ->get();

        $this->assertCount(1, $deliveryAttempts);
        $this->assertEquals('failed', $deliveryAttempts->first()->attempt_status);
        $this->assertEquals('recipient_not_available', $deliveryAttempts->first()->failure_reason);

        // Shipment status'unun güncellendiğini kontrol et
        $shipment->refresh();
        $this->assertEquals('failed', $shipment->status);
    }

    /** @test */
    public function it_can_generate_shipping_analytics_and_reports()
    {
        // Arrange - Multiple shipments oluştur
        $shipments = [
            // Delivered shipment
            EloquentShipment::create([
                'shipment_number' => 'SHP-2024-004',
                'order_id' => $this->order->id,
                'carrier_integration_id' => $this->carrier->id,
                'status' => 'delivered',
                'shipped_at' => Carbon::now()->subDays(2),
                'actual_delivery_at' => Carbon::now()->subDays(1),
                'total_cost' => 30.00,
                'origin_address' => ['city' => 'Istanbul'],
                'destination_address' => ['city' => 'Ankara'],
                'package_info' => ['weight' => 2.0],
                'service_type' => 'standard'
            ]),
            // In transit shipment
            EloquentShipment::create([
                'shipment_number' => 'SHP-2024-005',
                'order_id' => $this->order->id,
                'carrier_integration_id' => $this->carrier->id,
                'status' => 'in_transit',
                'shipped_at' => Carbon::now()->subHours(12),
                'estimated_delivery_at' => Carbon::now()->addHours(12),
                'total_cost' => 25.00,
                'origin_address' => ['city' => 'Istanbul'],
                'destination_address' => ['city' => 'Izmir'],
                'package_info' => ['weight' => 1.5],
                'service_type' => 'express'
            ]),
            // Failed shipment
            EloquentShipment::create([
                'shipment_number' => 'SHP-2024-006',
                'order_id' => $this->order->id,
                'carrier_integration_id' => $this->carrier->id,
                'status' => 'failed',
                'shipped_at' => Carbon::now()->subDays(3),
                'failure_reason' => 'address_not_found',
                'total_cost' => 20.00,
                'origin_address' => ['city' => 'Istanbul'],
                'destination_address' => ['city' => 'Antalya'],
                'package_info' => ['weight' => 1.0],
                'service_type' => 'standard'
            ])
        ];

        // Act - Repository statistics al
        $statistics = $this->shipmentRepository->getStatistics();

        // Assert - Statistics doğruluğunu kontrol et
        $this->assertEquals(3, $statistics['total_shipments']);
        $this->assertEquals(1, $statistics['delivered_count']);
        $this->assertEquals(1, $statistics['in_transit_count']);
        $this->assertEquals(1, $statistics['failed_count']);
        $this->assertEquals(75.00, $statistics['total_shipping_cost']);
        $this->assertEquals(25.00, $statistics['avg_shipping_cost']);

        // Delivery success rate hesaplama
        $expectedSuccessRate = (1 / 3) * 100; // 1 delivered out of 3 total
        $this->assertEquals(round($expectedSuccessRate, 2), $statistics['delivery_success_rate']);

        // Carrier performance kontrol et
        $this->assertArrayHasKey('carriers', $statistics);
        $this->assertCount(1, $statistics['carriers']); // Tek carrier var
        $this->assertEquals($this->carrier->carrier_code, $statistics['carriers'][0]['carrier_code']);
    }

    /**
     * Test data oluştur
     */
    protected function createShippingFeatureTestData(): void
    {
        // User oluştur
        $user = User::factory()->create();

        // Order oluştur
        $this->order = Order::factory()->create([
            'user_id' => $user->id
        ]);

        // Carrier integration oluştur
        $this->carrier = EloquentCarrierIntegration::create([
            'carrier_code' => 'test_carrier',
            'carrier_name' => 'Test Carrier',
            'integration_type' => 'api',
            'api_endpoint' => 'https://api.testcarrier.com',
            'is_active' => true,
            'supports_tracking' => true,
            'supports_rate_calculation' => true,
            'supports_label_generation' => true,
            'supports_pickup_scheduling' => false,
            'supports_delivery_confirmation' => true,
            'priority' => 10,
            'rate_limit_per_minute' => 60,
            'rate_limit_per_hour' => 1000,
            'rate_limit_per_day' => 10000,
            'supported_countries' => ['TR'],
            'supported_services' => ['standard', 'express', 'overnight']
        ]);
    }
}
