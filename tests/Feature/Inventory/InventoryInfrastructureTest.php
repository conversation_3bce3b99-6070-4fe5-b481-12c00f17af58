<?php

namespace Tests\Feature\Inventory;

use Tests\TestCase;
use App\Domain\Inventory\Repositories\StockRepositoryInterface;
use App\Infrastructure\Inventory\Services\StockTrackingService;
use App\Infrastructure\Inventory\Services\StockAlertService;
use App\Infrastructure\Inventory\Models\EloquentStock;
use App\Infrastructure\Inventory\Models\EloquentStockLocation;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;

/**
 * InventoryInfrastructureTest
 * Inventory Infrastructure Layer feature test'leri
 */
class InventoryInfrastructureTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private StockRepositoryInterface $stockRepository;
    private StockTrackingService $trackingService;
    private StockAlertService $alertService;
    private EloquentStockLocation $location;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->stockRepository = app(StockRepositoryInterface::class);
        $this->trackingService = app(StockTrackingService::class);
        $this->alertService = app(StockAlertService::class);

        // Test data oluştur
        $this->createFeatureTestData();
    }

    /** @test */
    public function it_can_manage_complete_stock_lifecycle()
    {
        // Arrange - Stok oluştur
        $stock = EloquentStock::create([
            'product_id' => $this->product->id,
            'location_id' => $this->location->id,
            'available_quantity' => 100,
            'reserved_quantity' => 0,
            'total_quantity' => 100,
            'low_stock_threshold' => 20,
            'reorder_level' => 30,
            'track_inventory' => true,
            'allow_backorder' => false,
            'is_active' => true
        ]);

        // Act & Assert - Stok girişi
        $inMovement = $this->trackingService->recordMovement(
            $stock->id,
            'in',
            50,
            'purchase_receipt',
            'purchase_order',
            123
        );

        $this->assertEquals('in', $inMovement['type']);
        $this->assertEquals(50, $inMovement['quantity']);
        $this->assertEquals(100, $inMovement['previous_quantity']);
        $this->assertEquals(150, $inMovement['new_quantity']);

        // Act & Assert - Stok rezervasyonu
        $reservation = $this->trackingService->createReservation(
            $stock->id,
            30,
            'order',
            456,
            'customer_order',
            60 // 1 hour expiry
        );

        $this->assertEquals('active', $reservation['status']);
        $this->assertEquals(30, $reservation['quantity']);
        $this->assertNotNull($reservation['reservation_id']);

        // Stok durumunu kontrol et
        $stock->refresh();
        $this->assertEquals(120, $stock->available_quantity); // 150 - 30
        $this->assertEquals(30, $stock->reserved_quantity);
        $this->assertEquals(150, $stock->total_quantity);

        // Act & Assert - Rezervasyonu tamamla
        $fulfillment = $this->trackingService->fulfillReservation(
            $reservation['reservation_id'],
            'order_shipped'
        );

        $this->assertEquals('fulfilled', $fulfillment['status']);

        // Final stok durumunu kontrol et
        $stock->refresh();
        $this->assertEquals(120, $stock->available_quantity);
        $this->assertEquals(0, $stock->reserved_quantity);
        $this->assertEquals(120, $stock->total_quantity);
    }

    /** @test */
    public function it_can_handle_stock_alerts_automatically()
    {
        // Arrange - Düşük stok threshold'u olan stok oluştur
        $stock = EloquentStock::create([
            'product_id' => $this->product->id,
            'location_id' => $this->location->id,
            'available_quantity' => 25,
            'reserved_quantity' => 0,
            'total_quantity' => 25,
            'low_stock_threshold' => 30,
            'reorder_level' => 40,
            'track_inventory' => true,
            'allow_backorder' => false,
            'is_active' => true
        ]);

        // Act - Düşük stok alert'i oluştur
        $lowStockAlert = $this->alertService->createLowStockAlert($stock->id);

        // Assert
        $this->assertEquals('created', $lowStockAlert['status']);
        $this->assertStringContainsString('Low Stock Alert', $lowStockAlert['title']);

        // Act - Stok çıkışı yaparak out of stock yap
        $this->trackingService->recordMovement(
            $stock->id,
            'out',
            25,
            'sale',
            'order',
            789
        );

        // Act - Out of stock alert oluştur
        $outOfStockAlert = $this->alertService->createOutOfStockAlert($stock->id);

        // Assert
        $this->assertEquals('created', $outOfStockAlert['status']);
        $this->assertEquals('critical', $outOfStockAlert['severity']);

        // Act - Reorder alert oluştur
        $reorderAlert = $this->alertService->createReorderAlert($stock->id);

        // Assert
        $this->assertEquals('created', $reorderAlert['status']);
        $this->assertEquals('high', $reorderAlert['severity']);
        $this->assertArrayHasKey('suggested_quantity', $reorderAlert);
    }

    /** @test */
    public function it_can_handle_reservation_expiry()
    {
        // Arrange
        $stock = EloquentStock::create([
            'product_id' => $this->product->id,
            'location_id' => $this->location->id,
            'available_quantity' => 100,
            'reserved_quantity' => 0,
            'total_quantity' => 100,
            'low_stock_threshold' => 10,
            'reorder_level' => 20,
            'track_inventory' => true,
            'allow_backorder' => false,
            'is_active' => true
        ]);

        // Act - Kısa süreli rezervasyon oluştur
        $reservation = $this->trackingService->createReservation(
            $stock->id,
            20,
            'cart',
            123,
            'shopping_cart',
            1 // 1 minute expiry
        );

        // Rezervasyon süresini geçmiş olarak güncelle
        DB::table('stock_reservations')
            ->where('reservation_id', $reservation['reservation_id'])
            ->update(['expires_at' => now()->subMinutes(5)]);

        // Act - Süresi dolmuş rezervasyonları temizle
        $cleanupResult = $this->trackingService->cleanupExpiredReservations();

        // Assert
        $this->assertEquals(1, $cleanupResult['total_expired']);
        $this->assertEquals(1, $cleanupResult['cleaned_count']);
        $this->assertEquals(0, $cleanupResult['errors_count']);

        // Stok durumunu kontrol et
        $stock->refresh();
        $this->assertEquals(100, $stock->available_quantity); // Rezervasyon serbest bırakıldı
        $this->assertEquals(0, $stock->reserved_quantity);
    }

    /** @test */
    public function it_can_generate_comprehensive_reports()
    {
        // Arrange - Çeşitli stok durumları oluştur
        $stocks = [
            // Normal stok
            EloquentStock::create([
                'product_id' => $this->product->id,
                'location_id' => $this->location->id,
                'available_quantity' => 100,
                'reserved_quantity' => 10,
                'total_quantity' => 110,
                'low_stock_threshold' => 20,
                'reorder_level' => 30,
                'track_inventory' => true,
                'is_active' => true
            ]),
            // Düşük stok
            EloquentStock::create([
                'product_id' => $this->product->id,
                'location_id' => $this->location->id,
                'available_quantity' => 5,
                'reserved_quantity' => 0,
                'total_quantity' => 5,
                'low_stock_threshold' => 20,
                'reorder_level' => 30,
                'track_inventory' => true,
                'is_active' => true
            ])
        ];

        // Act - Repository üzerinden istatistikleri al
        $statistics = $this->stockRepository->getStatistics();

        // Assert
        $this->assertIsArray($statistics);
        $this->assertEquals(2, $statistics['total_stocks']);
        $this->assertEquals(105, $statistics['total_available']); // 100 + 5
        $this->assertEquals(10, $statistics['total_reserved']);
        $this->assertEquals(115, $statistics['total_inventory']); // 110 + 5
        $this->assertEquals(1, $statistics['low_stock_count']); // Sadece ikinci stok
        $this->assertEquals(0, $statistics['out_of_stock_count']);

        // Act - Lokasyon özeti al
        $locationSummary = $this->stockRepository->getLocationSummary();

        // Assert
        $this->assertIsArray($locationSummary);
        $this->assertCount(1, $locationSummary); // Tek lokasyon
        $this->assertEquals($this->location->name, $locationSummary[0]['location_name']);
        $this->assertEquals(2, $locationSummary[0]['total_stocks']);
    }

    /** @test */
    public function it_can_handle_concurrent_stock_operations()
    {
        // Arrange
        $stock = EloquentStock::create([
            'product_id' => $this->product->id,
            'location_id' => $this->location->id,
            'available_quantity' => 100,
            'reserved_quantity' => 0,
            'total_quantity' => 100,
            'low_stock_threshold' => 10,
            'reorder_level' => 20,
            'track_inventory' => true,
            'allow_backorder' => false,
            'is_active' => true
        ]);

        // Act - Eşzamanlı rezervasyonlar oluştur
        $reservations = [];
        for ($i = 0; $i < 5; $i++) {
            $reservations[] = $this->trackingService->createReservation(
                $stock->id,
                10,
                'order',
                1000 + $i,
                'concurrent_test'
            );
        }

        // Assert - Tüm rezervasyonlar başarılı olmalı
        $this->assertCount(5, $reservations);
        foreach ($reservations as $reservation) {
            $this->assertEquals('active', $reservation['status']);
            $this->assertEquals(10, $reservation['quantity']);
        }

        // Stok durumunu kontrol et
        $stock->refresh();
        $this->assertEquals(50, $stock->available_quantity); // 100 - (5 * 10)
        $this->assertEquals(50, $stock->reserved_quantity); // 5 * 10
        $this->assertEquals(100, $stock->total_quantity);
    }

    /** @test */
    public function it_prevents_overselling_when_backorder_disabled()
    {
        // Arrange
        $stock = EloquentStock::create([
            'product_id' => $this->product->id,
            'location_id' => $this->location->id,
            'available_quantity' => 10,
            'reserved_quantity' => 0,
            'total_quantity' => 10,
            'low_stock_threshold' => 5,
            'reorder_level' => 8,
            'track_inventory' => true,
            'allow_backorder' => false, // Backorder kapalı
            'is_active' => true
        ]);

        // Act & Assert - Mevcut stoktan fazla rezervasyon yapmaya çalış
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Insufficient stock for reservation');

        $this->trackingService->createReservation(
            $stock->id,
            15, // Mevcut stoktan fazla
            'order',
            999,
            'oversell_test'
        );
    }

    /**
     * Test data oluştur
     */
    protected function createFeatureTestData(): void
    {
        // Category oluştur
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'status' => true
        ]);

        // Product oluştur
        $this->product = Product::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'description' => 'Test description',
            'price' => 100.00,
            'stock' => 50,
            'category_id' => $category->id,
            'status' => true,
            'sku' => 'TEST-PRODUCT-001'
        ]);

        // Stock location oluştur
        $this->location = EloquentStockLocation::create([
            'name' => 'Test Warehouse',
            'code' => 'TEST-WH',
            'type' => 'warehouse',
            'is_active' => true,
            'is_default' => true,
            'priority' => 1
        ]);
    }
}
