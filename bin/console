#!/usr/bin/env php
<?php

namespace scripts;
define('PATH_BASE', __DIR__);
chdir(PATH_BASE);
ini_set('memory_limit', -1);
require_once PATH_BASE.'/vendor/autoload.php';

use Phinx\Console\Command\Breakpoint;
use Phinx\Console\Command\Create;
use Phinx\Console\Command\ListAliases;
use Phinx\Console\Command\Migrate;
use Phinx\Console\Command\Rollback;
use Phinx\Console\Command\SeedCreate;
use Phinx\Console\Command\SeedRun;
use Phinx\Console\Command\Status;
use Symfony\Component\Console\Application;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Carbon\Carbon;
use Symfony\Component\Console\Command\Command;
use bin\Support\Config;
use bin\Commands\ExportJson;
use bin\Commands\ExportCsv;
use bin\Commands\ExportXml;
use bin\Commands\ExportYaml;
use bin\Commands\ExportCscNpm;
use bin\Commands\ExportPlist;
use bin\Commands\ExportSqlServer;

ini_set('display_errors', '1');
ini_set('display_startup_errors', '1');
error_reporting(E_ALL);

class ConsoleApplication extends Application
{
    public function __construct()
    {
        parent::__construct('World Database Exporter', '1.0.0');

        Config::getConfig();

        $commandsDir = PATH_BASE . '/Commands';
        $commands = [];

        if (!is_dir($commandsDir)) {
            mkdir($commandsDir, 0755, true);
        }

        foreach (new \DirectoryIterator($commandsDir) as $file) {
            if ($file->isDot() || $file->isDir()) {
                continue;
            }

            $className = 'bin\\Commands\\' . $file->getBasename('.php');
            if (class_exists($className)) {
                $command = new $className();
                if ($command instanceof Command) {
                    $commands[] = $command;
                }
            }
        }

        $this->addCommands($commands);
        $this->addCommands([
            (new Create())->setName('manage:create'),
            (new Migrate())->setName('manage:migrate'),
            (new Rollback())->setName('manage:rollback'),
            (new Status())->setName('manage:status'),
            (new Breakpoint())->setName('manage:breakpoint'),
            (new SeedCreate())->setName('seed:create'),
            (new SeedRun())->setName('seed:run'),
        ]);
        $this->add(new ExportJson());
        $this->add(new ExportCsv());
        $this->add(new ExportXml());
        $this->add(new ExportYaml());
        $this->add(new ExportCscNpm());
        $this->add(new ExportPlist());
        $this->add(new ExportSqlServer());
    }

    public function doRun(InputInterface $input, OutputInterface $output): int
    {
        if ($input->hasParameterOption(['--help', '-h']) ||
            ($input->getFirstArgument() !== null && $input->getFirstArgument() !== 'list')) {
            $output->writeln(sprintf('%s start at %s', $this->getLongVersion(), Carbon::now()));
            $output->writeln('');
        }

        return parent::doRun($input, $output);
    }
}

$application = new ConsoleApplication();
$application->run();
