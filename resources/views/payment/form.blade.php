<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ödeme Formu</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f7f7f7;
        }
        .container {
            width: 100%;
            max-width: 500px;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        form {
            display: flex;
            flex-direction: column;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 12px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 4px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .error {
            color: red;
            margin-top: 20px;
        }
        .success {
            color: green;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Ödeme Formu</h1>
        
        @if(session('error'))
            <div class="error">{{ session('error') }}</div>
        @endif
        
        @if(session('success'))
            <div class="success">{{ session('success') }}</div>
        @endif
        
        <form id="payment-form" method="POST" action="{{ $action }}">
            @csrf
            
            @foreach($formData as $key => $value)
                <input type="hidden" name="{{ $key }}" value="{{ $value }}">
            @endforeach
            
            @if($paymentMethod === 'credit_card')
                <div class="form-group">
                    <label for="card_holder_name">Kart Sahibi</label>
                    <input type="text" id="card_holder_name" name="card_holder_name" required>
                </div>
                
                <div class="form-group">
                    <label for="card_number">Kart Numarası</label>
                    <input type="text" id="card_number" name="card_number" required maxlength="16" placeholder="1234 5678 9012 3456">
                </div>
                
                <div style="display: flex; gap: 10px;">
                    <div class="form-group" style="flex: 1;">
                        <label for="expiry_month">Son Kullanma Ay</label>
                        <input type="text" id="expiry_month" name="expiry_month" required maxlength="2" placeholder="MM">
                    </div>
                    
                    <div class="form-group" style="flex: 1;">
                        <label for="expiry_year">Son Kullanma Yıl</label>
                        <input type="text" id="expiry_year" name="expiry_year" required maxlength="2" placeholder="YY">
                    </div>
                    
                    <div class="form-group" style="flex: 1;">
                        <label for="cvc">CVC</label>
                        <input type="text" id="cvc" name="cvc" required maxlength="3" placeholder="123">
                    </div>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="use_3d_secure" value="1" checked>
                        3D Secure ile öde
                    </label>
                </div>
            @endif
            
            <button type="submit">Ödemeyi Tamamla</button>
        </form>
    </div>
    
    <script>
        // Form gönderildiğinde debug bilgisi
        document.getElementById('payment-form').addEventListener('submit', function(e) {
            console.log('Form gönderiliyor...');
            console.log('Action:', this.action);
            console.log('Method:', this.method);
            
            // Form verilerini logla
            const formData = new FormData(this);
            const formDataObj = {};
            formData.forEach((value, key) => {
                formDataObj[key] = value;
            });
            console.log('Form verileri:', formDataObj);
        });
    </script>
</body>
</html>
