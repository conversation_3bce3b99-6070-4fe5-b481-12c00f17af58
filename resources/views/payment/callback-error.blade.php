<!DOCTYPE html>
<html lang="tr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ödeme <PERSON>ı</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            text-align: center;
        }

        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 500px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .error-icon {
            color: #e74c3c;
            font-size: 48px;
            margin-bottom: 20px;
        }

        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="error-icon">✕</div>
        <h1>Ödeme Hatası</h1>
        <p>{{ $message ?? 'Ödeme işlemi sırasında bir hata oluştu.' }}</p>

        <script>
            // Sayfa yüklendikten sonra parent window'a mesaj gönder
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(function() {
                    try {
                        // Eğer bu sayfa bir iframe içinde açıldıysa (popup)
                        if (window.self !== window.top) {
                            console.log('İframe içinde çalışıyor, parent window\'a hata mesajı gönderiliyor');
                            
                            // Parent window'a mesaj gönder
                            window.parent.postMessage({
                                type: 'iyzico:error',
                                message: '{{ $message ?? 'Ödeme işlemi sırasında bir hata oluştu.' }}',
                                redirectUrl: '{{ $redirect_url ?? route('checkout') }}'
                            }, '*');
                            
                            console.log('Hata mesajı gönderildi');
                        } else {
                            console.log('Normal sayfada çalışıyor, yönlendirme yapılıyor');
                            
                            // Normal sayfa ise doğrudan yönlendir
                            window.location.href = "{{ $redirect_url ?? route('checkout') }}";
                        }
                    } catch (e) {
                        console.error('Hata oluştu:', e);
                        
                        // Hata durumunda yine de yönlendir
                        window.location.href = "{{ $redirect_url ?? route('checkout') }}";
                    }
                }, 3000);
            });
        </script>

        <p><a href="{{ $redirect_url ?? route('checkout') }}" class="btn">Ödeme Sayfasına Dön</a></p>
    </div>
</body>

</html>
