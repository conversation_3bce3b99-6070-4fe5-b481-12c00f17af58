<!DOCTYPE html>
<html lang="tr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ödeme <PERSON></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            text-align: center;
        }

        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 500px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Ödeme İşlemi</h1>
        <div class="loader"></div>
        <p>Ödeme sayfasına yönlendiriliyorsunuz. Lütfen bekleyin...</p>

        @if (isset($html_content))
            <div id="payment-form" style="display: none;">
                {!! $html_content !!}
            </div>
            <script>
                // Sayfa yüklendikten sonra ödeme formunu otomatik olarak gönder
                document.addEventListener('DOMContentLoaded', function() {
                    setTimeout(function() {
                        // 3D Secure formunu otomatik olarak gönder
                        var form = document.querySelector('#payment-form form');
                        if (form) {
                            // Form action URL'sini al
                            var originalAction = form.getAttribute('action');

                            // Form hedefini iframe olarak ayarla
                            if (originalAction) {
                                console.log('Form action URL:', originalAction);

                                // Form içindeki tüm input alanlarını al
                                var inputs = form.querySelectorAll('input');
                                var inputData = {};

                                inputs.forEach(function(input) {
                                    if (input.name) {
                                        inputData[input.name] = input.value;
                                    }
                                });

                                console.log('Form verileri:', inputData);

                                // Iframe oluştur
                                var iframe = document.createElement('iframe');
                                iframe.name = 'iyzico3DSecureFrame';
                                iframe.id = 'iyzico3DSecureFrame';
                                iframe.style.width = '100%';
                                iframe.style.height = '600px';
                                iframe.style.border = 'none';

                                // Iframe'i sayfaya ekle
                                document.getElementById('payment-form').innerHTML = '';
                                document.getElementById('payment-form').style.display = 'block';
                                document.getElementById('payment-form').appendChild(iframe);

                                // Form hedefini iframe olarak ayarla
                                form.target = 'iyzico3DSecureFrame';

                                // Formu gönder
                                form.submit();
                            } else {
                                // Form action URL'si yoksa normal gönder
                                form.submit();
                            }
                        } else {
                            // Form bulunamazsa içeriği göster
                            document.getElementById('payment-form').style.display = 'block';
                        }
                    }, 1000);
                });
            </script>
        @elseif(isset($redirect_url))
            <script>
                // Sayfa yüklendikten sonra belirtilen URL'ye yönlendir
                document.addEventListener('DOMContentLoaded', function() {
                    setTimeout(function() {
                        // Eğer bu sayfa bir iframe içinde açıldıysa (popup)
                        if (window.self !== window.top) {
                            // Parent window'a mesaj gönder
                            window.parent.postMessage('iyzico:success:{{ $redirect_url }}', '*');
                        } else {
                            // Normal sayfa ise doğrudan yönlendir
                            window.location.href = "{{ $redirect_url }}";
                        }
                    }, 1000);
                });
            </script>
        @endif

        <p><a href="/" class="btn">Ana Sayfaya Dön</a></p>
    </div>
</body>

</html>
