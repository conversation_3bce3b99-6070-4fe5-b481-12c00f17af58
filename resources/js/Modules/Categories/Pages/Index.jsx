import React, { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import CategoryList from '../Components/CategoryList';
import CategoryForm from '../Components/CategoryForm';
import categoryApi from '../../../Services/modules/categoryApi';

const CategoriesIndex = () => {
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showForm, setShowForm] = useState(false);
    const [currentCategory, setCurrentCategory] = useState(null);

    useEffect(() => {
        fetchCategories();
    }, []);

    const fetchCategories = async () => {
        try {
            setLoading(true);
            const response = await categoryApi.getAll();
            setCategories(response.data.data);
        } catch (error) {
            console.error('Kategoriler yüklenirken hata oluştu:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleEdit = (category) => {
        setCurrentCategory(category);
        setShowForm(true);
    };

    const handleDelete = async (id) => {
        if (!confirm('Bu kategoriyi silmek istediğinizden emin misiniz?')) {
            return;
        }

        try {
            await categoryApi.delete(id);
            fetchCategories();
        } catch (error) {
            console.error('Kategori silinirken hata oluştu:', error);
        }
    };

    const handleFormSubmit = async (formData) => {
        try {
            if (currentCategory) {
                await categoryApi.update(currentCategory.id, formData);
            } else {
                await categoryApi.create(formData);
            }
            
            setShowForm(false);
            setCurrentCategory(null);
            fetchCategories();
        } catch (error) {
            console.error('Kategori kaydedilirken hata oluştu:', error);
            throw error;
        }
    };

    const handleFormCancel = () => {
        setShowForm(false);
        setCurrentCategory(null);
    };

    return (
        <>
            <Head title="Kategoriler" />
            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h1 className="text-2xl font-bold">Kategoriler</h1>
                                <button
                                    onClick={() => setShowForm(true)}
                                    className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded"
                                >
                                    Yeni Kategori Ekle
                                </button>
                            </div>

                            {showForm ? (
                                <div className="mb-6 p-4 border rounded-lg bg-gray-50">
                                    <h2 className="text-lg font-semibold mb-4">
                                        {currentCategory ? 'Kategori Düzenle' : 'Yeni Kategori Ekle'}
                                    </h2>
                                    <CategoryForm
                                        category={currentCategory}
                                        onSubmit={handleFormSubmit}
                                        onCancel={handleFormCancel}
                                    />
                                </div>
                            ) : null}

                            {loading ? (
                                <div className="flex justify-center items-center h-40">
                                    <p className="text-gray-500">Yükleniyor...</p>
                                </div>
                            ) : (
                                <CategoryList
                                    categories={categories}
                                    onEdit={handleEdit}
                                    onDelete={handleDelete}
                                />
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default CategoriesIndex;
