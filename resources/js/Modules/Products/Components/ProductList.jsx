import React from 'react';

const ProductList = ({ products, onEdit, onDelete }) => {
    return (
        <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
                <thead className="bg-gray-100">
                    <tr>
                        <th className="py-2 px-4 border-b text-left">ID</th>
                        <th className="py-2 px-4 border-b text-left"><PERSON><PERSON><PERSON><PERSON></th>
                        <th className="py-2 px-4 border-b text-left">Fiyat</th>
                        <th className="py-2 px-4 border-b text-left">Stok</th>
                        <th className="py-2 px-4 border-b text-left">Durum</th>
                        <th className="py-2 px-4 border-b text-left"><PERSON><PERSON><PERSON><PERSON></th>
                    </tr>
                </thead>
                <tbody>
                    {products.map(product => (
                        <tr key={product.id}>
                            <td className="py-2 px-4 border-b">{product.id}</td>
                            <td className="py-2 px-4 border-b">{product.name}</td>
                            <td className="py-2 px-4 border-b">{product.price} TL</td>
                            <td className="py-2 px-4 border-b">{product.stock}</td>
                            <td className="py-2 px-4 border-b">
                                <span className={`px-2 py-1 rounded text-xs ${product.status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                    {product.status ? 'Aktif' : 'Pasif'}
                                </span>
                            </td>
                            <td className="py-2 px-4 border-b">
                                <button 
                                    onClick={() => onEdit(product)} 
                                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mr-2"
                                >
                                    Düzenle
                                </button>
                                <button 
                                    onClick={() => onDelete(product.id)} 
                                    className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                                >
                                    Sil
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default ProductList;
