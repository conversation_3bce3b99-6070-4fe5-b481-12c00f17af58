import React, { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import ProductList from '../Components/ProductList';
import ProductForm from '../Components/ProductForm';
import productApi from '../../../Services/modules/productApi';

const ProductsIndex = () => {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showForm, setShowForm] = useState(false);
    const [currentProduct, setCurrentProduct] = useState(null);

    useEffect(() => {
        fetchProducts();
    }, []);

    const fetchProducts = async () => {
        try {
            setLoading(true);
            const response = await productApi.getAll();
            setProducts(response.data.data);
        } catch (error) {
            console.error('Ürünler yüklenirken hata oluştu:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleEdit = (product) => {
        setCurrentProduct(product);
        setShowForm(true);
    };

    const handleDelete = async (id) => {
        if (!confirm('Bu ürünü silmek istediğinizden emin misiniz?')) {
            return;
        }

        try {
            await productApi.delete(id);
            fetchProducts();
        } catch (error) {
            console.error('Ürün silinirken hata oluştu:', error);
        }
    };

    const handleFormSubmit = async (formData) => {
        try {
            if (currentProduct) {
                await productApi.update(currentProduct.id, formData);
            } else {
                await productApi.create(formData);
            }
            
            setShowForm(false);
            setCurrentProduct(null);
            fetchProducts();
        } catch (error) {
            console.error('Ürün kaydedilirken hata oluştu:', error);
            throw error;
        }
    };

    const handleFormCancel = () => {
        setShowForm(false);
        setCurrentProduct(null);
    };

    return (
        <>
            <Head title="Ürünler" />
            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h1 className="text-2xl font-bold">Ürünler</h1>
                                <button
                                    onClick={() => setShowForm(true)}
                                    className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded"
                                >
                                    Yeni Ürün Ekle
                                </button>
                            </div>

                            {showForm ? (
                                <div className="mb-6 p-4 border rounded-lg bg-gray-50">
                                    <h2 className="text-lg font-semibold mb-4">
                                        {currentProduct ? 'Ürün Düzenle' : 'Yeni Ürün Ekle'}
                                    </h2>
                                    <ProductForm
                                        product={currentProduct}
                                        onSubmit={handleFormSubmit}
                                        onCancel={handleFormCancel}
                                    />
                                </div>
                            ) : null}

                            {loading ? (
                                <div className="flex justify-center items-center h-40">
                                    <p className="text-gray-500">Yükleniyor...</p>
                                </div>
                            ) : (
                                <ProductList
                                    products={products}
                                    onEdit={handleEdit}
                                    onDelete={handleDelete}
                                />
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default ProductsIndex;
