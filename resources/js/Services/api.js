import axios from "axios";

const api = axios.create({
    baseURL: "", // API isteklerini tam URL ile yapmak için baseURL'i boş bırakıyoruz
    headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        "X-Requested-With": "XMLHttpRequest",
    },
});

// Request interceptor - örneğin token ekleme
api.interceptors.request.use((config) => {
    // CSRF token ekleme
    const token = document.head.querySelector('meta[name="csrf-token"]');
    if (token) {
        config.headers["X-CSRF-TOKEN"] = token.content;
    }
    return config;
});

// Response interceptor - hata yönetimi
api.interceptors.response.use(
    (response) => response,
    (error) => {
        // 401 hatası durumunda login sayfasına yönlendirme
        if (error.response && error.response.status === 401) {
            window.location = "/login";
        }
        return Promise.reject(error);
    }
);

export default api;
