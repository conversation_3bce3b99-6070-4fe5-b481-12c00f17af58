import axios from 'axios';

const CategoryService = {
    /**
     * <PERSON><PERSON><PERSON> ağ<PERSON>ını getir
     * 
     * @returns {Promise}
     */
    getCategoryTree: async () => {
        try {
            const response = await axios.get('/admin/categories/dropdown');
            return response.data.categories;
        } catch (error) {
            console.error('Kategori ağacı yüklenirken hata oluştu:', error);
            throw error;
        }
    },

    /**
     * Tüm kategorileri düz liste olarak getir
     * 
     * @returns {Promise}
     */
    getAllCategories: async () => {
        try {
            const response = await axios.get('/api/v1/categories');
            return response.data;
        } catch (error) {
            console.error('Kategoriler yüklenirken hata oluştu:', error);
            throw error;
        }
    },

    /**
     * Kategori ağacını düz listeye dönüştür
     * 
     * @param {Array} tree - <PERSON><PERSON><PERSON> ağacı
     * @param {Array} result - <PERSON><PERSON><PERSON> listesi
     * @param {Number} level - <PERSON><PERSON><PERSON> se<PERSON>
     * @returns {Array}
     */
    flattenCategoryTree: (tree, result = [], level = 0) => {
        tree.forEach(category => {
            // Kategori adının önüne seviyesine göre tire ekle
            const prefix = level > 0 ? '- '.repeat(level) : '';
            
            result.push({
                id: category.id,
                name: `${prefix}${category.name}`,
                originalName: category.name,
                level: level,
                parent_id: category.parent_id,
                status: category.status
            });
            
            if (category.children && category.children.length > 0) {
                CategoryService.flattenCategoryTree(category.children, result, level + 1);
            }
        });
        
        return result;
    }
};

export default CategoryService;
