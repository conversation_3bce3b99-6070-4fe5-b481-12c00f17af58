import React from "react";
import { <PERSON>, <PERSON> } from "@inertiajs/react";
import MainLayout from "@/Layouts/MainLayout";
import HomeProductCard from "@/Components/HomeProductCard";

export default function Home({
    auth,
    featuredProducts,
    onSaleProducts,
    mostViewedProducts,
    popularCategories,
    recentlyViewedProducts,
    favoriteProducts,
    personalizedRecommendations,
    meta,
}) {
    return (
        <MainLayout>
            <Head title={meta?.title || "Ana Sayfa"}>
                {meta?.description && (
                    <meta name="description" content={meta.description} />
                )}
                {meta?.keywords && (
                    <meta name="keywords" content={meta.keywords} />
                )}
                {meta?.canonical && (
                    <link rel="canonical" href={meta.canonical} />
                )}
                {meta?.robots && <meta name="robots" content={meta.robots} />}

                {/* Open Graph meta etiketleri */}
                {meta?.og?.title && (
                    <meta property="og:title" content={meta.og.title} />
                )}
                {meta?.og?.description && (
                    <meta
                        property="og:description"
                        content={meta.og.description}
                    />
                )}
                {meta?.og?.type && (
                    <meta property="og:type" content={meta.og.type} />
                )}
                {meta?.og?.url && (
                    <meta property="og:url" content={meta.og.url} />
                )}
                {meta?.og?.image && (
                    <meta property="og:image" content={meta.og.image} />
                )}
                {meta?.og?.site_name && (
                    <meta property="og:site_name" content={meta.og.site_name} />
                )}

                {/* Twitter Card meta etiketleri */}
                {meta?.twitter?.card && (
                    <meta name="twitter:card" content={meta.twitter.card} />
                )}
                {meta?.twitter?.title && (
                    <meta name="twitter:title" content={meta.twitter.title} />
                )}
                {meta?.twitter?.description && (
                    <meta
                        name="twitter:description"
                        content={meta.twitter.description}
                    />
                )}
                {meta?.twitter?.image && (
                    <meta name="twitter:image" content={meta.twitter.image} />
                )}
            </Head>

            {/* Hero Section */}
            <div className="bg-blue-600 text-white">
                <div className="container mx-auto px-4 py-16">
                    <div className="flex flex-col md:flex-row items-center">
                        <div className="md:w-1/2 mb-8 md:mb-0">
                            <h1 className="text-4xl md:text-5xl font-bold mb-4">
                                Alışverişin En Kolay Hali
                            </h1>
                            <p className="text-xl mb-6">
                                Binlerce ürün, uygun fiyatlar ve hızlı teslimat
                                ile alışverişin keyfini çıkarın.
                            </p>
                            <div className="flex flex-wrap gap-4">
                                <Link
                                    href="/products"
                                    className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-100 transition"
                                >
                                    Alışverişe Başla
                                </Link>
                                <Link
                                    href="/categories"
                                    className="bg-transparent border-2 border-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition"
                                >
                                    Kategorileri Keşfet
                                </Link>
                            </div>
                        </div>
                        <div className="md:w-1/2">
                            <img
                                src="/images/hero-image.jpg"
                                alt="Alışveriş"
                                className="rounded-lg shadow-lg"
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* Featured Categories */}
            <div className="py-16 bg-gray-50">
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12">
                        Popüler Kategoriler
                    </h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                        {popularCategories && popularCategories.length > 0
                            ? popularCategories.map((category) => (
                                  <div
                                      key={category.id}
                                      className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition"
                                  >
                                      <div className="h-40 bg-gray-200">
                                          {/* Kategori resmi burada olacak */}
                                      </div>
                                      <div className="p-4">
                                          <h3 className="font-semibold text-lg mb-2">
                                              {category.name}
                                          </h3>
                                          <Link
                                              href={`/${category.seo_url}`}
                                              className="text-blue-600 hover:text-blue-800"
                                          >
                                              {category.products_count} Ürün →
                                          </Link>
                                      </div>
                                  </div>
                              ))
                            : [
                                  "Elektronik",
                                  "Giyim",
                                  "Ev & Yaşam",
                                  "Kozmetik",
                              ].map((category, index) => (
                                  <div
                                      key={index}
                                      className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition"
                                  >
                                      <div className="h-40 bg-gray-200"></div>
                                      <div className="p-4">
                                          <h3 className="font-semibold text-lg mb-2">
                                              {category}
                                          </h3>
                                          <Link
                                              href={`/categories/${index + 1}`}
                                              className="text-blue-600 hover:text-blue-800"
                                          >
                                              Ürünleri Gör →
                                          </Link>
                                      </div>
                                  </div>
                              ))}
                    </div>
                </div>
            </div>

            {/* Featured Products */}
            <div className="py-16">
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12">
                        Öne Çıkan Ürünler
                    </h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                        {featuredProducts && featuredProducts.length > 0
                            ? featuredProducts.map((product) => (
                                  <HomeProductCard
                                      key={product.id}
                                      product={product}
                                  />
                              ))
                            : [1, 2, 3, 4, 5, 6, 7, 8].map((product) => (
                                  <div
                                      key={product}
                                      className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition"
                                  >
                                      <div className="h-48 bg-gray-200"></div>
                                      <div className="p-4">
                                          <h3 className="font-semibold text-lg mb-1">
                                              Ürün {product}
                                          </h3>
                                          <p className="text-gray-600 text-sm mb-2">
                                              Kategori
                                          </p>
                                          <div className="flex justify-between items-center">
                                              <span className="font-bold text-lg">
                                                  ₺199.99
                                              </span>
                                              <button className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                                  Sepete Ekle
                                              </button>
                                          </div>
                                      </div>
                                  </div>
                              ))}
                    </div>
                    <div className="text-center mt-8">
                        <Link
                            href="/products"
                            className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition"
                        >
                            Tüm Ürünleri Gör
                        </Link>
                    </div>
                </div>
            </div>

            {/* On Sale Products */}
            <div className="py-16 bg-gray-50">
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12">
                        İndirimli Ürünler
                    </h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                        {onSaleProducts && onSaleProducts.length > 0 ? (
                            onSaleProducts.map((product) => (
                                <HomeProductCard
                                    key={product.id}
                                    product={product}
                                />
                            ))
                        ) : (
                            <div className="col-span-4 text-center py-8">
                                <p className="text-gray-500">
                                    Şu anda indirimli ürün bulunmamaktadır.
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* User Specific Sections - Only show if user is logged in */}
            {auth?.user && (
                <>
                    {/* Recently Viewed Products */}
                    {recentlyViewedProducts &&
                        recentlyViewedProducts.length > 0 && (
                            <div className="py-16">
                                <div className="container mx-auto px-4">
                                    <h2 className="text-3xl font-bold text-center mb-12">
                                        Son Gezdiğiniz Ürünler
                                    </h2>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                                        {recentlyViewedProducts.map(
                                            (product) => (
                                                <HomeProductCard
                                                    key={product.id}
                                                    product={product}
                                                />
                                            )
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}

                    {/* Favorite Products */}
                    {favoriteProducts && favoriteProducts.length > 0 && (
                        <div className="py-16 bg-gray-50">
                            <div className="container mx-auto px-4">
                                <h2 className="text-3xl font-bold text-center mb-12">
                                    Favori Ürünleriniz
                                </h2>
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                                    {favoriteProducts.map((product) => (
                                        <HomeProductCard
                                            key={product.id}
                                            product={product}
                                        />
                                    ))}
                                </div>
                                <div className="text-center mt-8">
                                    <Link
                                        href="/customer/favorites"
                                        className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition"
                                    >
                                        Tüm Favorilerinizi Görüntüleyin
                                    </Link>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Personalized Recommendations */}
                    {personalizedRecommendations &&
                        personalizedRecommendations.length > 0 && (
                            <div className="py-16">
                                <div className="container mx-auto px-4">
                                    <h2 className="text-3xl font-bold text-center mb-12">
                                        Size Özel Öneriler
                                    </h2>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                                        {personalizedRecommendations.map(
                                            (product) => (
                                                <HomeProductCard
                                                    key={product.id}
                                                    product={product}
                                                />
                                            )
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}
                </>
            )}

            {/* Most Viewed Products */}
            <div className="py-16 bg-gray-50">
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12">
                        En Çok Görüntülenen Ürünler
                    </h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                        {mostViewedProducts && mostViewedProducts.length > 0 ? (
                            mostViewedProducts.map((product) => (
                                <HomeProductCard
                                    key={product.id}
                                    product={product}
                                />
                            ))
                        ) : (
                            <div className="col-span-4 text-center py-8">
                                <p className="text-gray-500">
                                    Henüz görüntülenen ürün bulunmamaktadır.
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Features */}
            <div className="py-16">
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12">
                        Neden Bizi Tercih Etmelisiniz?
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <div className="bg-blue-100 text-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-8 w-8"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">
                                Kaliteli Ürünler
                            </h3>
                            <p className="text-gray-600">
                                Tüm ürünlerimiz kalite kontrolünden geçerek
                                sizlere ulaşır.
                            </p>
                        </div>
                        <div className="text-center">
                            <div className="bg-blue-100 text-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-8 w-8"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">
                                Hızlı Teslimat
                            </h3>
                            <p className="text-gray-600">
                                Siparişleriniz en kısa sürede kapınıza teslim
                                edilir.
                            </p>
                        </div>
                        <div className="text-center">
                            <div className="bg-blue-100 text-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-8 w-8"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                                    />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">
                                Güvenli Ödeme
                            </h3>
                            <p className="text-gray-600">
                                Tüm ödeme işlemleriniz güvenli bir şekilde
                                gerçekleştirilir.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Newsletter */}
            <div className="py-16 bg-blue-600 text-white">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">
                        Fırsatlardan Haberdar Olun
                    </h2>
                    <p className="text-xl mb-8 max-w-2xl mx-auto">
                        E-bültenimize kayıt olarak özel indirimler ve
                        kampanyalardan haberdar olabilirsiniz.
                    </p>
                    <form className="max-w-md mx-auto flex">
                        <input
                            type="email"
                            placeholder="E-posta adresiniz"
                            className="flex-1 px-4 py-3 rounded-l-lg text-gray-900"
                        />
                        <button
                            type="submit"
                            className="bg-blue-800 px-6 py-3 rounded-r-lg font-semibold hover:bg-blue-900"
                        >
                            Abone Ol
                        </button>
                    </form>
                </div>
            </div>
        </MainLayout>
    );
}
