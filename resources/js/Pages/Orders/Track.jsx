import React, { useState } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import MainLayout from "@/Layouts/MainLayout";
import { formatPrice } from "@/Utils/cartUtils";
import { toast } from "react-toastify";

export default function Track({ order, error, orderNumber, email }) {
    const [copySuccess, setCopySuccess] = useState(false);
    
    const { data, setData, post, processing, errors } = useForm({
        order_number: orderNumber || "",
        email: email || "",
    });
    
    // Sipariş numarasını kopyala
    const copyOrderNumber = () => {
        if (order) {
            navigator.clipboard
                .writeText(order.order_number)
                .then(() => {
                    setCopySuccess(true);
                    toast.success("Sipariş numarası kopyalandı!");
                    setTimeout(() => setCopySuccess(false), 2000);
                })
                .catch((err) => {
                    console.error("Kopyalama başarısız oldu: ", err);
                    toast.error(
                        "Kopyalama başarısız oldu. Lütfen manuel olarak kopyalayın."
                    );
                });
        }
    };
    
    // IBAN kopyala
    const copyIban = () => {
        if (order && order.bank_account && order.bank_account.iban) {
            navigator.clipboard
                .writeText(order.bank_account.iban)
                .then(() => {
                    toast.success("IBAN kopyalandı!");
                })
                .catch((err) => {
                    console.error("IBAN kopyalama başarısız oldu: ", err);
                    toast.error(
                        "Kopyalama başarısız oldu. Lütfen manuel olarak kopyalayın."
                    );
                });
        }
    };
    
    // Form gönderildiğinde
    const handleSubmit = (e) => {
        e.preventDefault();
        post(route("orders.track.submit"));
    };
    
    return (
        <MainLayout>
            <Head title="Sipariş Takip" />
            
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-3xl mx-auto">
                    <h1 className="text-2xl font-bold text-gray-800 mb-6">Sipariş Takip</h1>
                    
                    {!order && (
                        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                            <h2 className="text-xl font-semibold mb-4">Siparişinizi Takip Edin</h2>
                            
                            {error && (
                                <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                                    <div className="flex">
                                        <div className="flex-shrink-0">
                                            <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <div className="ml-3">
                                            <p className="text-sm text-red-700">{error}</p>
                                        </div>
                                    </div>
                                </div>
                            )}
                            
                            <form onSubmit={handleSubmit}>
                                <div className="mb-4">
                                    <label htmlFor="order_number" className="block text-sm font-medium text-gray-700 mb-1">
                                        Sipariş Numarası
                                    </label>
                                    <input
                                        type="text"
                                        id="order_number"
                                        value={data.order_number}
                                        onChange={(e) => setData("order_number", e.target.value)}
                                        className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Örn: ORD-ABCD1234"
                                        required
                                    />
                                    {errors.order_number && (
                                        <p className="mt-1 text-sm text-red-600">{errors.order_number}</p>
                                    )}
                                </div>
                                
                                <div className="mb-6">
                                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                        E-posta Adresi
                                    </label>
                                    <input
                                        type="email"
                                        id="email"
                                        value={data.email}
                                        onChange={(e) => setData("email", e.target.value)}
                                        className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Siparişinizde kullandığınız e-posta"
                                        required
                                    />
                                    {errors.email && (
                                        <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                                    )}
                                </div>
                                
                                <div className="flex items-center justify-between">
                                    <button
                                        type="submit"
                                        disabled={processing}
                                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                                    >
                                        {processing ? "İşleniyor..." : "Siparişi Takip Et"}
                                    </button>
                                    
                                    <Link
                                        href="/"
                                        className="text-blue-600 hover:text-blue-800"
                                    >
                                        Ana Sayfaya Dön
                                    </Link>
                                </div>
                            </form>
                        </div>
                    )}
                    
                    {order && (
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <div className="flex items-center justify-between mb-6">
                                <h2 className="text-xl font-semibold">Sipariş Detayları</h2>
                                <Link
                                    href={route("orders.track")}
                                    className="text-blue-600 hover:text-blue-800 text-sm"
                                >
                                    Başka Bir Sipariş Ara
                                </Link>
                            </div>
                            
                            <div className="border-t border-b py-4 mb-6">
                                <div className="flex justify-between mb-2">
                                    <span className="font-medium">Sipariş Numarası:</span>
                                    <div className="flex items-center">
                                        <span>{order.order_number}</span>
                                        <button
                                            onClick={copyOrderNumber}
                                            className="ml-2 text-blue-600 hover:text-blue-800 focus:outline-none"
                                            title="Sipariş numarasını kopyala"
                                        >
                                            {copySuccess ? (
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className="h-5 w-5 text-green-600"
                                                    viewBox="0 0 20 20"
                                                    fill="currentColor"
                                                >
                                                    <path
                                                        fillRule="evenodd"
                                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                        clipRule="evenodd"
                                                    />
                                                </svg>
                                            ) : (
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className="h-5 w-5"
                                                    viewBox="0 0 20 20"
                                                    fill="currentColor"
                                                >
                                                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                                </svg>
                                            )}
                                        </button>
                                    </div>
                                </div>
                                
                                <div className="flex justify-between mb-2">
                                    <span className="font-medium">Sipariş Tarihi:</span>
                                    <span>
                                        {new Date(order.created_at).toLocaleDateString("tr-TR")}
                                    </span>
                                </div>
                                
                                <div className="flex justify-between mb-2">
                                    <span className="font-medium">Ödeme Yöntemi:</span>
                                    <span>
                                        {order.payment_method === "credit_card"
                                            ? "Kredi Kartı"
                                            : order.payment_method === "bank_transfer"
                                            ? "Havale/EFT"
                                            : order.payment_method === "iyzico"
                                            ? "iyzico"
                                            : order.payment_method}
                                    </span>
                                </div>
                                
                                <div className="flex justify-between mb-2">
                                    <span className="font-medium">Sipariş Durumu:</span>
                                    <span
                                        className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                            order.status === "pending"
                                                ? "bg-yellow-100 text-yellow-800"
                                                : order.status === "processing"
                                                ? "bg-blue-100 text-blue-800"
                                                : order.status === "shipped"
                                                ? "bg-purple-100 text-purple-800"
                                                : order.status === "delivered"
                                                ? "bg-green-100 text-green-800"
                                                : order.status === "cancelled"
                                                ? "bg-red-100 text-red-800"
                                                : "bg-gray-100 text-gray-800"
                                        }`}
                                    >
                                        {order.status === "pending"
                                            ? "Beklemede"
                                            : order.status === "processing"
                                            ? "İşleniyor"
                                            : order.status === "shipped"
                                            ? "Kargoya Verildi"
                                            : order.status === "delivered"
                                            ? "Teslim Edildi"
                                            : order.status === "cancelled"
                                            ? "İptal Edildi"
                                            : order.status}
                                    </span>
                                </div>
                                
                                <div className="flex justify-between mb-2">
                                    <span className="font-medium">Ödeme Durumu:</span>
                                    <span
                                        className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                            order.payment_status === "pending"
                                                ? "bg-yellow-100 text-yellow-800"
                                                : order.payment_status === "paid"
                                                ? "bg-green-100 text-green-800"
                                                : order.payment_status === "failed"
                                                ? "bg-red-100 text-red-800"
                                                : order.payment_status === "refunded"
                                                ? "bg-purple-100 text-purple-800"
                                                : "bg-gray-100 text-gray-800"
                                        }`}
                                    >
                                        {order.payment_status === "pending"
                                            ? "Beklemede"
                                            : order.payment_status === "paid"
                                            ? "Ödendi"
                                            : order.payment_status === "failed"
                                            ? "Başarısız"
                                            : order.payment_status === "refunded"
                                            ? "İade Edildi"
                                            : order.payment_status}
                                    </span>
                                </div>
                                
                                <div className="flex justify-between">
                                    <span className="font-medium">Toplam Tutar:</span>
                                    <span className="font-bold">
                                        {formatPrice(order.total_amount)}
                                    </span>
                                </div>
                            </div>
                            
                            {order.payment_method === "bank_transfer" &&
                                order.payment_status === "pending" && (
                                    <div className="bg-yellow-50 border-2 border-yellow-300 rounded-lg p-6 mb-6">
                                        <div className="flex items-center mb-3">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                className="h-6 w-6 text-yellow-600 mr-2"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                                />
                                            </svg>
                                            <h3 className="font-bold text-yellow-800 text-lg">
                                                Ödeme Bilgileri
                                            </h3>
                                        </div>
                                        <div className="bg-white border border-yellow-200 rounded p-4 mb-4">
                                            <p className="text-yellow-800 font-medium mb-2">
                                                Siparişinizi tamamlamak için aşağıdaki
                                                banka hesabına ödeme yapmanız
                                                gerekmektedir.
                                            </p>
                                            <p className="text-red-600 font-bold mb-2">
                                                Ödeme açıklamasına sipariş numaranızı (
                                                {order.order_number}) yazmayı unutmayın!
                                            </p>
                                            
                                            {order.bank_account && (
                                                <div className="text-sm bg-gray-50 p-4 rounded border border-gray-200 mt-3">
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                        <div className="p-2 bg-white rounded border border-gray-100">
                                                            <div className="font-bold text-gray-700 mb-1">
                                                                Banka
                                                            </div>
                                                            <div className="text-gray-800">
                                                                {
                                                                    order.bank_account
                                                                        .bank_name
                                                                }
                                                            </div>
                                                        </div>
                                                        
                                                        <div className="p-2 bg-white rounded border border-gray-100">
                                                            <div className="font-bold text-gray-700 mb-1">
                                                                Hesap Sahibi
                                                            </div>
                                                            <div className="text-gray-800">
                                                                {
                                                                    order.bank_account
                                                                        .account_name
                                                                }
                                                            </div>
                                                        </div>
                                                        
                                                        <div className="p-2 bg-white rounded border border-gray-100 md:col-span-2">
                                                            <div className="font-bold text-gray-700 mb-1">
                                                                IBAN
                                                            </div>
                                                            <div className="flex items-center">
                                                                <span className="text-gray-800 font-mono mr-2">
                                                                    {
                                                                        order
                                                                            .bank_account
                                                                            .iban
                                                                    }
                                                                </span>
                                                                <button
                                                                    onClick={copyIban}
                                                                    className="bg-blue-100 hover:bg-blue-200 text-blue-700 px-2 py-1 rounded flex items-center text-xs"
                                                                    title="IBAN'ı kopyala"
                                                                >
                                                                    <svg
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                        className="h-4 w-4 mr-1"
                                                                        viewBox="0 0 20 20"
                                                                        fill="currentColor"
                                                                    >
                                                                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                                                        <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                                                    </svg>
                                                                    Kopyala
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    {order.bank_account.description && (
                                                        <div className="mt-3 p-2 bg-yellow-50 text-yellow-700 rounded border border-yellow-200">
                                                            {
                                                                order.bank_account
                                                                    .description
                                                            }
                                                        </div>
                                                    )}
                                                </div>
                                            )}
                                        </div>
                                        <div className="mt-4 bg-blue-50 border border-blue-200 rounded p-3 text-sm text-blue-800">
                                            <p className="flex items-center">
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className="h-5 w-5 mr-2 text-blue-600"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                                    />
                                                </svg>
                                                Ödemeniz banka hesabımıza ulaştıktan
                                                sonra (genellikle 1-2 iş günü içinde)
                                                siparişiniz işleme alınacaktır.
                                            </p>
                                        </div>
                                    </div>
                                )}
                            
                            <div className="mt-6">
                                <h3 className="font-semibold text-lg mb-3">Sipariş Öğeleri</h3>
                                <div className="border rounded-lg overflow-hidden">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Ürün
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Fiyat
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Adet
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Toplam
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {order.items.map((item) => (
                                                <tr key={item.id}>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {item.product_name}
                                                        </div>
                                                        {item.options && Object.keys(item.options).length > 0 && (
                                                            <div className="text-xs text-gray-500 mt-1">
                                                                {Object.entries(item.options).map(([key, value]) => (
                                                                    <span key={key} className="mr-2">
                                                                        {key}: {value}
                                                                    </span>
                                                                ))}
                                                            </div>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            {formatPrice(item.price)}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            {item.quantity}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {formatPrice(item.subtotal)}
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                        <tfoot className="bg-gray-50">
                                            <tr>
                                                <td colSpan="3" className="px-6 py-3 text-right text-sm font-medium text-gray-500">
                                                    Ara Toplam:
                                                </td>
                                                <td className="px-6 py-3 text-sm font-medium text-gray-900">
                                                    {formatPrice(
                                                        order.items.reduce(
                                                            (total, item) => total + item.subtotal,
                                                            0
                                                        )
                                                    )}
                                                </td>
                                            </tr>
                                            {order.discount_amount > 0 && (
                                                <tr>
                                                    <td colSpan="3" className="px-6 py-3 text-right text-sm font-medium text-gray-500">
                                                        İndirim:
                                                    </td>
                                                    <td className="px-6 py-3 text-sm font-medium text-red-600">
                                                        -{formatPrice(order.discount_amount)}
                                                    </td>
                                                </tr>
                                            )}
                                            {order.shipping_cost > 0 && (
                                                <tr>
                                                    <td colSpan="3" className="px-6 py-3 text-right text-sm font-medium text-gray-500">
                                                        Kargo:
                                                    </td>
                                                    <td className="px-6 py-3 text-sm font-medium text-gray-900">
                                                        {formatPrice(order.shipping_cost)}
                                                    </td>
                                                </tr>
                                            )}
                                            <tr>
                                                <td colSpan="3" className="px-6 py-3 text-right text-sm font-bold text-gray-700">
                                                    Genel Toplam:
                                                </td>
                                                <td className="px-6 py-3 text-sm font-bold text-gray-900">
                                                    {formatPrice(order.total_amount)}
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                            
                            <div className="mt-8 flex justify-center">
                                <Link
                                    href="/"
                                    className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700"
                                >
                                    Ana Sayfaya Dön
                                </Link>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </MainLayout>
    );
}
