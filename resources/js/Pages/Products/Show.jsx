import React, { useState, useEffect } from "react";
import { Head, Link, router, usePage } from "@inertiajs/react";
import MainLayout from "@/Layouts/MainLayout";
import { toast } from "react-hot-toast";
import axios from "axios";

export default function Show(props) {
    // Güvenli bir şekilde props'ları al
    const safeProps = usePage().props;

    // Varsayılan değerlerle birlikte props'ları kullan
    const product = safeProps.product || {};
    const relatedProducts = safeProps.relatedProducts || [];
    const variantAttributes = safeProps.variantAttributes || [];
    const variantOptions = safeProps.variantOptions || [];
    const defaultVariant = safeProps.defaultVariant || null;
    const seoUrl = safeProps.seo_url || "";
    const url = safeProps.url || "";

    const [quantity, setQuantity] = useState(1);
    const [selectedVariant, setSelectedVariant] = useState(defaultVariant);
    const [selectedAttributes, setSelectedAttributes] = useState(
        defaultVariant ? defaultVariant.attribute_values : {}
    );
    const [availableVariants, setAvailableVariants] = useState([]);
    const [currentPrice, setCurrentPrice] = useState(
        defaultVariant ? defaultVariant.price : product.price || 0
    );
    const [isAdding, setIsAdding] = useState(false);

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    // URL'yi güncelle
    useEffect(() => {
        if (selectedVariant && selectedVariant.url) {
            // URL'yi güncelle ama sayfayı yeniden yükleme
            window.history.replaceState(null, "", selectedVariant.url);
        } else if (url) {
            window.history.replaceState(null, "", url);
        }
    }, [selectedVariant, url]);

    // Varyant seçimlerini izle ve uygun varyantı bul
    useEffect(() => {
        if (variantAttributes.length > 0) {
            // Seçilen özelliklere göre uygun varyantları filtrele
            const filteredVariants = variantOptions.filter((variant) => {
                // Tüm seçilen özellikler için kontrol yap
                for (const [code, value] of Object.entries(
                    selectedAttributes
                )) {
                    if (
                        !variant.attribute_values[code] ||
                        variant.attribute_values[code] !== value
                    ) {
                        return false;
                    }
                }
                return true;
            });

            setAvailableVariants(filteredVariants);

            // Eğer tam olarak bir varyant seçilmişse, onu aktif varyant olarak ayarla
            if (
                filteredVariants.length === 1 &&
                Object.keys(selectedAttributes).length ===
                    variantAttributes.length
            ) {
                setSelectedVariant(filteredVariants[0]);
                setCurrentPrice(filteredVariants[0].price);
            } else if (filteredVariants.length === 0) {
                // Eğer hiç varyant bulunamazsa, seçimi sıfırla
                setSelectedVariant(null);
                setCurrentPrice(product.price);
            } else {
                // Kısmi eşleşme durumunda varyant seçilmemiş olarak işaretle
                setSelectedVariant(null);
                setCurrentPrice(product.price);
            }
        }
    }, [selectedAttributes, variantAttributes, variantOptions, product.price]);

    // Özellik seçimini güncelle
    const handleAttributeChange = (attributeCode, value) => {
        setSelectedAttributes((prev) => ({
            ...prev,
            [attributeCode]: value,
        }));
    };

    // Sepete ekle
    const addToCart = () => {
        if (isAdding) return;

        setIsAdding(true);

        const cartData = {
            product_id: product.id,
            quantity: quantity,
        };

        // Eğer varyant seçilmişse, varyant ID'sini ekle
        if (selectedVariant) {
            cartData.variant_id = selectedVariant.id;
        }

        // Sunucuya yük binmemesi için kısa bir gecikme ekle (500ms)
        setTimeout(() => {
            // Axios ile POST isteği gönder
            axios
                .post(route("cart.add"), cartData)
                .then((response) => {
                    toast.success("Ürün sepete eklendi");

                    // Sepet sayısını güncelle
                    if (
                        response.data &&
                        response.data.cartCount !== undefined
                    ) {
                        // Özel olay tetikle - Bu, tüm komponentlerin sepet sayısını güncellemesini sağlar
                        window.dispatchEvent(
                            new CustomEvent("cart-updated", {
                                detail: { cartCount: response.data.cartCount },
                            })
                        );
                    } else {
                        // Eğer response'da cartCount yoksa, API'den sepet sayısını al
                        import("@/Utils/cartUtils").then(
                            ({ updateCartCount }) => {
                                updateCartCount();
                            }
                        );
                    }
                    setIsAdding(false);
                })
                .catch((error) => {
                    toast.error("Ürün sepete eklenirken bir hata oluştu");
                    console.error("Sepete ekleme hatası:", error);
                    setIsAdding(false);
                });
        }, 500); // 500ms gecikme
    };

    // Favorilere ekle
    const addToFavorites = () => {
        // İleriki aşamada eklenecek
        toast.success("Ürün favorilere eklendi");
    };

    return (
        <MainLayout>
            <Head title={product.name} />

            <div className="bg-gray-100 py-8">
                <div className="container mx-auto px-4">
                    {/* Breadcrumb */}
                    <nav className="flex mb-6 text-sm">
                        <Link
                            href="/"
                            className="text-gray-600 hover:text-blue-600"
                        >
                            Ana Sayfa
                        </Link>
                        <span className="mx-2 text-gray-500">/</span>
                        <Link
                            href={route("products.index")}
                            className="text-gray-600 hover:text-blue-600"
                        >
                            Ürünler
                        </Link>
                        {product.category && (
                            <>
                                <span className="mx-2 text-gray-500">/</span>
                                <Link
                                    href={route("products.index", {
                                        category: product.category.id,
                                    })}
                                    className="text-gray-600 hover:text-blue-600"
                                >
                                    {product.category.name}
                                </Link>
                            </>
                        )}
                        <span className="mx-2 text-gray-500">/</span>
                        <span className="text-gray-800 font-medium">
                            {product.name}
                        </span>
                    </nav>

                    {/* Ürün Detayı */}
                    <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
                        <div className="md:flex">
                            {/* Ürün Görseli */}
                            <div className="md:w-1/2">
                                <img
                                    src={
                                        product.image ||
                                        "/images/placeholder.png"
                                    }
                                    alt={product.name}
                                    className="w-full h-96 object-cover"
                                />
                            </div>

                            {/* Ürün Bilgileri */}
                            <div className="md:w-1/2 p-6">
                                <h1 className="text-3xl font-bold mb-2">
                                    {product.name}
                                </h1>

                                {product.category && (
                                    <Link
                                        href={route("products.index", {
                                            category: product.category.id,
                                        })}
                                        className="text-blue-600 hover:text-blue-800 mb-4 inline-block"
                                    >
                                        {product.category.name}
                                    </Link>
                                )}

                                <div className="text-2xl font-bold text-blue-600 mb-4">
                                    {formatPrice(currentPrice)}
                                    {selectedVariant &&
                                        selectedVariant.additional_price >
                                            0 && (
                                            <span className="text-sm text-gray-500 ml-2">
                                                (Temel fiyat:{" "}
                                                {formatPrice(product.price)})
                                            </span>
                                        )}
                                </div>

                                <div className="mb-6">
                                    <p className="text-gray-700">
                                        {product.description}
                                    </p>
                                </div>

                                {/* Varyant seçenekleri */}
                                {variantAttributes.length > 0 && (
                                    <div className="mb-6">
                                        <h3 className="text-lg font-semibold mb-3">
                                            Varyant Seçenekleri
                                        </h3>

                                        {variantAttributes.map((attribute) => (
                                            <div
                                                key={attribute.id}
                                                className="mb-4"
                                            >
                                                <label className="block text-gray-700 mb-2">
                                                    {attribute.name}:
                                                </label>
                                                <div className="flex flex-wrap gap-2">
                                                    {attribute.values.map(
                                                        (value) => (
                                                            <button
                                                                key={
                                                                    value.value
                                                                }
                                                                type="button"
                                                                onClick={() =>
                                                                    handleAttributeChange(
                                                                        attribute.code,
                                                                        value.value
                                                                    )
                                                                }
                                                                className={`px-3 py-2 border rounded-md ${
                                                                    selectedAttributes[
                                                                        attribute
                                                                            .code
                                                                    ] ===
                                                                    value.value
                                                                        ? "border-blue-500 bg-blue-50 text-blue-700"
                                                                        : "border-gray-300 hover:border-gray-400"
                                                                }`}
                                                            >
                                                                {value.label}
                                                            </button>
                                                        )
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}

                                <div className="mb-6">
                                    <div className="flex items-center mb-4">
                                        <span className="text-gray-700 mr-3">
                                            Stok Durumu:
                                        </span>
                                        {selectedVariant ? (
                                            // Varyant seçilmişse varyantın stok durumunu göster
                                            selectedVariant.stock > 0 &&
                                            selectedVariant.status ===
                                                "in_stock" ? (
                                                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                                                    Stokta (
                                                    {selectedVariant.stock}{" "}
                                                    adet)
                                                </span>
                                            ) : (
                                                <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
                                                    Stokta Yok
                                                </span>
                                            )
                                        ) : // Varyant seçilmemişse ürünün stok durumunu göster
                                        product.stock > 0 ? (
                                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                                                Stokta ({product.stock} adet)
                                            </span>
                                        ) : (
                                            <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
                                                Stokta Yok
                                            </span>
                                        )}
                                    </div>

                                    {/* Seçilen varyant veya ürün stokta varsa miktar seçiciyi göster */}
                                    {((selectedVariant &&
                                        selectedVariant.stock > 0 &&
                                        selectedVariant.status ===
                                            "in_stock") ||
                                        (!selectedVariant &&
                                            product.stock > 0)) && (
                                        <div className="flex items-center mb-4">
                                            <span className="text-gray-700 mr-3">
                                                Adet:
                                            </span>
                                            <div className="flex items-center">
                                                <button
                                                    onClick={() =>
                                                        setQuantity(
                                                            Math.max(
                                                                1,
                                                                quantity - 1
                                                            )
                                                        )
                                                    }
                                                    className="w-8 h-8 rounded-l border border-gray-300 flex items-center justify-center hover:bg-gray-100"
                                                >
                                                    -
                                                </button>
                                                <div className="w-12 h-8 border-t border-b border-gray-300 flex items-center justify-center">
                                                    {quantity}
                                                </div>
                                                <button
                                                    onClick={() =>
                                                        setQuantity(
                                                            Math.min(
                                                                selectedVariant
                                                                    ? selectedVariant.stock
                                                                    : product.stock,
                                                                quantity + 1
                                                            )
                                                        )
                                                    }
                                                    className="w-8 h-8 rounded-r border border-gray-300 flex items-center justify-center hover:bg-gray-100"
                                                >
                                                    +
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                <div className="flex flex-wrap gap-3">
                                    <button
                                        onClick={addToCart}
                                        disabled={
                                            (selectedVariant &&
                                                (selectedVariant.stock <= 0 ||
                                                    selectedVariant.status !==
                                                        "in_stock")) ||
                                            (!selectedVariant &&
                                                product.stock <= 0) ||
                                            (variantAttributes.length > 0 &&
                                                !selectedVariant)
                                        }
                                        className={`flex-1 py-3 px-4 rounded-lg font-semibold ${
                                            (selectedVariant &&
                                                selectedVariant.stock > 0 &&
                                                selectedVariant.status ===
                                                    "in_stock") ||
                                            (!selectedVariant &&
                                                product.stock > 0 &&
                                                variantAttributes.length === 0)
                                                ? "bg-blue-600 text-white hover:bg-blue-700"
                                                : "bg-gray-300 text-gray-500 cursor-not-allowed"
                                        }`}
                                    >
                                        {variantAttributes.length > 0 &&
                                        !selectedVariant
                                            ? "Lütfen varyant seçiniz"
                                            : (selectedVariant &&
                                                  selectedVariant.stock > 0 &&
                                                  selectedVariant.status ===
                                                      "in_stock") ||
                                              (!selectedVariant &&
                                                  product.stock > 0)
                                            ? "Sepete Ekle"
                                            : "Stokta Yok"}
                                    </button>

                                    <button
                                        onClick={addToFavorites}
                                        className="w-12 h-12 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-100"
                                    >
                                        <svg
                                            className="w-6 h-6 text-red-500"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                                            />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Ürün Detayları Sekmeleri */}
                        <div className="border-t border-gray-200">
                            <div className="p-6">
                                <h2 className="text-xl font-semibold mb-4">
                                    Ürün Detayları
                                </h2>
                                <div className="prose max-w-none">
                                    <p>{product.description}</p>
                                </div>

                                {/* Varyant bilgileri */}
                                {variantOptions.length > 0 && (
                                    <div className="mt-6">
                                        <h3 className="text-lg font-semibold mb-3">
                                            Mevcut Varyantlar
                                        </h3>
                                        <div className="overflow-x-auto">
                                            <table className="min-w-full bg-white border border-gray-200">
                                                <thead>
                                                    <tr>
                                                        <th className="py-2 px-4 border-b text-left">
                                                            Varyant
                                                        </th>
                                                        <th className="py-2 px-4 border-b text-left">
                                                            Fiyat
                                                        </th>
                                                        <th className="py-2 px-4 border-b text-left">
                                                            Stok
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {variantOptions.map(
                                                        (variant) => (
                                                            <tr
                                                                key={variant.id}
                                                                className={
                                                                    selectedVariant &&
                                                                    selectedVariant.id ===
                                                                        variant.id
                                                                        ? "bg-blue-50"
                                                                        : ""
                                                                }
                                                            >
                                                                <td className="py-2 px-4 border-b">
                                                                    {Object.entries(
                                                                        variant.formatted_attributes
                                                                    ).map(
                                                                        ([
                                                                            name,
                                                                            value,
                                                                        ]) => (
                                                                            <span
                                                                                key={
                                                                                    name
                                                                                }
                                                                                className="inline-block mr-2"
                                                                            >
                                                                                <span className="font-semibold">
                                                                                    {
                                                                                        name
                                                                                    }

                                                                                    :
                                                                                </span>{" "}
                                                                                {
                                                                                    value
                                                                                }
                                                                            </span>
                                                                        )
                                                                    )}
                                                                </td>
                                                                <td className="py-2 px-4 border-b">
                                                                    {formatPrice(
                                                                        variant.price
                                                                    )}
                                                                </td>
                                                                <td className="py-2 px-4 border-b">
                                                                    {variant.stock >
                                                                        0 &&
                                                                    variant.status ===
                                                                        "in_stock" ? (
                                                                        <span className="text-green-600">
                                                                            {
                                                                                variant.stock
                                                                            }{" "}
                                                                            adet
                                                                        </span>
                                                                    ) : (
                                                                        <span className="text-red-600">
                                                                            Stokta
                                                                            yok
                                                                        </span>
                                                                    )}
                                                                </td>
                                                            </tr>
                                                        )
                                                    )}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Benzer Ürünler */}
                    {relatedProducts && relatedProducts.length > 0 && (
                        <div className="mb-8">
                            <h2 className="text-2xl font-bold mb-6">
                                Benzer Ürünler
                            </h2>

                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                                {relatedProducts.map((relatedProduct) => (
                                    <div
                                        key={relatedProduct.id}
                                        className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition"
                                    >
                                        <Link
                                            href={`/products/${relatedProduct.id}`}
                                        >
                                            <img
                                                src={
                                                    relatedProduct.image ||
                                                    "/images/placeholder.png"
                                                }
                                                alt={relatedProduct.name}
                                                className="w-full h-48 object-cover"
                                            />
                                        </Link>
                                        <div className="p-4">
                                            <Link
                                                href={`/products/${relatedProduct.id}`}
                                            >
                                                <h3 className="font-semibold text-lg mb-1 hover:text-blue-600">
                                                    {relatedProduct.name}
                                                </h3>
                                            </Link>
                                            <div className="flex justify-between items-center">
                                                <span className="font-bold">
                                                    {formatPrice(
                                                        relatedProduct.price
                                                    )}
                                                </span>
                                                <button
                                                    onClick={() => {
                                                        router.post(
                                                            route("cart.add"),
                                                            {
                                                                product_id:
                                                                    relatedProduct.id,
                                                                quantity: 1,
                                                            },
                                                            {
                                                                onSuccess:
                                                                    () => {
                                                                        toast.success(
                                                                            "Ürün sepete eklendi"
                                                                        );
                                                                    },
                                                                onError: () => {
                                                                    toast.error(
                                                                        "Ürün sepete eklenirken bir hata oluştu"
                                                                    );
                                                                },
                                                            }
                                                        );
                                                    }}
                                                    className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
                                                >
                                                    Sepete Ekle
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </MainLayout>
    );
}
