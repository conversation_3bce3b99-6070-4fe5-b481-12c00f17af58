import React, { useState, useEffect } from "react";
import { Link, router } from "@inertiajs/react";
import MainLayout from "@/Layouts/MainLayout";
import Pagination from "@/Components/Pagination";
import ProductCard from "@/Components/ProductCard";
import PriceRangeSlider from "@/Components/PriceRangeSlider";
import FilterGroup from "@/Components/FilterGroup";
import ActiveFilters from "@/Components/ActiveFilters";
import SeoMeta from "@/Components/SeoMeta";
import { updateCartCount } from "@/Utils/cartUtils";
import { debounce } from "lodash";

export default function CategoryProducts({
    auth,
    products,
    category,
    categories,
    childCategories,
    filters,
    breadcrumb,
    priceRange = { min: 0, max: 1000 },
    price_ranges = [],
    meta,
}) {
    const activeFilters = filters?.active || {};
    const availableFilters = filters?.available || {};
    const priceRanges = price_ranges;

    // State'ler
    const [searchQuery, setSearchQuery] = useState(activeFilters.q || "");
    const [sortBy, setSortBy] = useState("");
    const [priceMin, setPriceMin] = useState(activeFilters.price_min || "");
    const [priceMax, setPriceMax] = useState(activeFilters.price_max || "");
    const [inStock, setInStock] = useState(activeFilters.in_stock || false);
    const [selectedAttributes, setSelectedAttributes] = useState(
        activeFilters.attributes || {}
    );
    const [selectedVariants, setSelectedVariants] = useState(
        activeFilters.variants || {}
    );
    const [isFilterOpen, setIsFilterOpen] = useState(false);

    // Sayfa yüklenirken sepet sayısını güncelle ve filtreleri ayarla
    useEffect(() => {
        updateCartCount();

        // Aktif filtrelerden sıralama değerini ayarla
        if (activeFilters?.sorting) {
            setSortBy(activeFilters.sorting);
        }

        // URL'den gelen parametreleri kontrol et
        const urlParams = new URLSearchParams(window.location.search);

        // URL'de filtreler parametresi varsa, seçili özellikleri ve varyantları güncelle
        if (urlParams.has("filtreler")) {
            const filterString = urlParams.get("filtreler");
            console.log("URL'den alınan filtre string:", filterString);

            if (filterString) {
                const filterParts = filterString.split(";");

                filterParts.forEach((part) => {
                    const [key, valueStr] = part.split(":");
                    if (key && valueStr) {
                        const values = valueStr.split(",");

                        // Özellik mi varyant mı kontrol et (burada basit bir kontrol yapıyoruz)
                        // Gerçek uygulamada daha karmaşık bir kontrol gerekebilir
                        if (
                            availableFilters.attributes &&
                            availableFilters.attributes[key]
                        ) {
                            // Bu bir özellik
                            setSelectedAttributes((prev) => ({
                                ...prev,
                                [key]: values,
                            }));
                        } else if (
                            availableFilters.variants &&
                            availableFilters.variants[key]
                        ) {
                            // Bu bir varyant
                            setSelectedVariants((prev) => ({
                                ...prev,
                                [key]: values,
                            }));
                        } else {
                            // Bilinmeyen bir filtre, varsayılan olarak özellik olarak kabul et
                            setSelectedAttributes((prev) => ({
                                ...prev,
                                [key]: values,
                            }));
                        }
                    }
                });
            }
        }

        console.log("Aktif filtreler:", activeFilters);
    }, []);

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    // Fiyat aralığı değiştiğinde
    const handlePriceChange = debounce(({ min, max }) => {
        setPriceMin(min);
        setPriceMax(max);
    }, 300);

    // Özellik değiştiğinde
    const handleAttributeChange = (code, value, checked) => {
        setSelectedAttributes((prev) => {
            const newAttributes = { ...prev };

            if (!newAttributes[code]) {
                newAttributes[code] = [];
            }

            if (checked) {
                if (!newAttributes[code].includes(value)) {
                    newAttributes[code] = [...newAttributes[code], value];
                }
            } else {
                newAttributes[code] = newAttributes[code].filter(
                    (v) => v !== value
                );
                if (newAttributes[code].length === 0) {
                    delete newAttributes[code];
                }
            }

            return newAttributes;
        });
    };

    // Varyant değiştiğinde
    const handleVariantChange = (code, value, checked) => {
        setSelectedVariants((prev) => {
            const newVariants = { ...prev };

            if (!newVariants[code]) {
                newVariants[code] = [];
            }

            if (checked) {
                if (!newVariants[code].includes(value)) {
                    newVariants[code] = [...newVariants[code], value];
                }
            } else {
                newVariants[code] = newVariants[code].filter(
                    (v) => v !== value
                );
                if (newVariants[code].length === 0) {
                    delete newVariants[code];
                }
            }

            return newVariants;
        });
    };

    // Arama formunu gönder
    const handleSearch = (e) => {
        e.preventDefault();
        applyFilters();
    };

    // Filtreleri uygula
    const applyFilters = () => {
        const params = {};
        const filterParts = [];

        // Temel filtreler
        if (searchQuery) params.q = searchQuery;
        // Sort değeri boş değilse ekle
        if (sortBy && sortBy !== "" && sortBy !== null && sortBy !== undefined)
            params.sort = sortBy;

        // Fiyat aralığı
        if (priceMin !== "") params.price_min = priceMin;
        if (priceMax !== "") params.price_max = priceMax;

        // Stok durumu
        if (inStock) params.in_stock = "1";

        // Özellik filtreleri
        if (Object.keys(selectedAttributes).length > 0) {
            // SEO dostu URL için filtre parçalarını oluştur
            Object.entries(selectedAttributes).forEach(([code, values]) => {
                if (values.length > 0) {
                    filterParts.push(`${code}:${values.join(",")}`);
                }
            });
        }

        // Varyant filtreleri
        if (Object.keys(selectedVariants).length > 0) {
            // SEO dostu URL için filtre parçalarını oluştur
            Object.entries(selectedVariants).forEach(([code, values]) => {
                if (values.length > 0) {
                    filterParts.push(`${code}:${values.join(",")}`);
                }
            });
        }

        // Debug için konsola yazdır
        console.log("Filter params:", params);
        console.log("Filter parts:", filterParts);

        // Eğer filtre parçaları varsa, SEO dostu URL oluştur
        if (filterParts.length > 0) {
            params.filtreler = filterParts.join(";");
        }

        // Kategori sayfasına git
        router.get(route("categories.products", category.id), params);
    };

    // Filtreyi kaldır
    const removeFilter = (key, value = null) => {
        if (key === "q") {
            setSearchQuery("");
        } else if (key === "sort") {
            setSortBy("");
        } else if (key === "price_min") {
            setPriceMin("");
        } else if (key === "price_max") {
            setPriceMax("");
        } else if (key === "in_stock") {
            setInStock(false);
        } else if (key.startsWith("attributes.")) {
            const code = key.split(".")[1];
            handleAttributeChange(code, value, false);
        } else if (key.startsWith("variants.")) {
            const code = key.split(".")[1];
            handleVariantChange(code, value, false);
        }

        // Filtreleri hemen uygula
        setTimeout(applyFilters, 100);
    };

    // Tüm filtreleri temizle
    const clearAllFilters = () => {
        setSearchQuery("");
        setSortBy("");
        setPriceMin("");
        setPriceMax("");
        setInStock(false);
        setSelectedAttributes({});
        setSelectedVariants({});

        router.get(route("categories.products", category.id));
    };

    // Sıralama değiştiğinde
    const handleSortChange = (e) => {
        const newSortValue = e.target.value;
        setSortBy(newSortValue);
        setTimeout(() => {
            // Boş değer kontrolü
            if (newSortValue === null || newSortValue === undefined) {
                setSortBy("");
            }
            applyFilters();
        }, 100);
    };

    return (
        <MainLayout>
            <SeoMeta meta={meta} />

            <div className="bg-gray-100 py-8">
                <div className="container mx-auto px-4">
                    {/* Breadcrumb */}
                    <nav className="flex mb-6 text-sm">
                        <Link
                            href="/"
                            className="text-gray-600 hover:text-blue-600"
                        >
                            Ana Sayfa
                        </Link>
                        <span className="mx-2 text-gray-500">/</span>
                        <Link
                            href={route("categories.index")}
                            className="text-gray-600 hover:text-blue-600"
                        >
                            Kategoriler
                        </Link>

                        {breadcrumb &&
                            breadcrumb.map((item, index) => (
                                <React.Fragment key={item.id}>
                                    <span className="mx-2 text-gray-500">
                                        /
                                    </span>
                                    {index === breadcrumb.length - 1 ? (
                                        <span className="text-gray-800 font-medium">
                                            {item.name}
                                        </span>
                                    ) : (
                                        <Link
                                            href={route(
                                                "categories.products",
                                                item.id
                                            )}
                                            className="text-gray-600 hover:text-blue-600"
                                        >
                                            {item.name}
                                        </Link>
                                    )}
                                </React.Fragment>
                            ))}

                        {!breadcrumb && (
                            <>
                                <span className="mx-2 text-gray-500">/</span>
                                <span className="text-gray-800 font-medium">
                                    {category.name}
                                </span>
                            </>
                        )}
                    </nav>

                    {/* Mobil filtre butonu */}
                    <div className="md:hidden mb-4">
                        <button
                            onClick={() => setIsFilterOpen(!isFilterOpen)}
                            className="w-full bg-white rounded-lg shadow-md p-3 flex justify-between items-center"
                        >
                            <span className="font-medium">Filtreler</span>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                            >
                                <path
                                    fillRule="evenodd"
                                    d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                                    clipRule="evenodd"
                                />
                            </svg>
                        </button>
                    </div>

                    {/* Aktif filtreler */}
                    {Object.keys(activeFilters).length > 0 && (
                        <ActiveFilters
                            filters={{ ...activeFilters, sort: undefined }}
                            onRemove={removeFilter}
                            onClearAll={clearAllFilters}
                        />
                    )}

                    <div className="flex flex-col md:flex-row gap-6">
                        {/* Sidebar */}
                        <div
                            className={`md:w-1/4 ${
                                isFilterOpen ? "block" : "hidden md:block"
                            }`}
                        >
                            {/* Kategori Listesi */}
                            <div className="bg-white rounded-lg shadow-md p-4 mb-4">
                                <h2 className="text-lg font-semibold mb-4">
                                    Kategoriler
                                </h2>
                                <ul className="space-y-2">
                                    {categories.map((cat) => (
                                        <li key={cat.id}>
                                            <Link
                                                href={route(
                                                    "categories.products",
                                                    cat.id
                                                )}
                                                className={`block py-1 ${
                                                    cat.id === category.id
                                                        ? "text-blue-600 font-medium"
                                                        : "text-gray-700 hover:text-blue-600"
                                                }`}
                                            >
                                                {cat.name}
                                            </Link>

                                            {/* Alt kategoriler */}
                                            {cat.id === category.id &&
                                                childCategories &&
                                                childCategories.length > 0 && (
                                                    <ul className="pl-4 mt-2 space-y-1">
                                                        {childCategories.map(
                                                            (child) => (
                                                                <li
                                                                    key={
                                                                        child.id
                                                                    }
                                                                >
                                                                    <Link
                                                                        href={route(
                                                                            "categories.products",
                                                                            child.id
                                                                        )}
                                                                        className="block py-1 text-sm text-gray-700 hover:text-blue-600"
                                                                    >
                                                                        {
                                                                            child.name
                                                                        }
                                                                    </Link>
                                                                </li>
                                                            )
                                                        )}
                                                    </ul>
                                                )}
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            {/* Filtreler */}
                            <div className="bg-white rounded-lg shadow-md p-4 mb-4">
                                <form onSubmit={handleSearch}>
                                    <div className="mb-4">
                                        <label
                                            htmlFor="search"
                                            className="block text-sm font-medium text-gray-700 mb-1"
                                        >
                                            Arama
                                        </label>
                                        <div className="flex">
                                            <input
                                                type="text"
                                                id="search"
                                                placeholder="Ürün ara..."
                                                className="border border-gray-300 rounded-l px-4 py-2 w-full"
                                                value={searchQuery}
                                                onChange={(e) =>
                                                    setSearchQuery(
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            <button
                                                type="submit"
                                                className="bg-blue-600 text-white px-4 py-2 rounded-r hover:bg-blue-700"
                                            >
                                                Ara
                                            </button>
                                        </div>
                                    </div>

                                    <div className="mb-4">
                                        <label
                                            htmlFor="sort"
                                            className="block text-sm font-medium text-gray-700 mb-1"
                                        >
                                            Sıralama
                                        </label>
                                        <select
                                            id="sort"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                            value={sortBy || ""}
                                            onChange={handleSortChange}
                                        >
                                            <option value="">
                                                Sıralama Seçiniz
                                            </option>
                                            <option value="newest">
                                                En Yeniler
                                            </option>
                                            <option value="price_asc">
                                                Fiyat (Düşükten Yükseğe)
                                            </option>
                                            <option value="price_desc">
                                                Fiyat (Yüksekten Düşüğe)
                                            </option>
                                            <option value="name_asc">
                                                İsim (A-Z)
                                            </option>
                                            <option value="name_desc">
                                                İsim (Z-A)
                                            </option>
                                            <option value="popularity">
                                                Popülerlik
                                            </option>
                                            <option value="discount">
                                                İndirim Oranı
                                            </option>
                                        </select>
                                    </div>

                                    <FilterGroup
                                        title="Fiyat Aralığı"
                                        initialOpen={true}
                                    >
                                        <PriceRangeSlider
                                            min={priceRange.min}
                                            max={priceRange.max}
                                            initialMin={priceMin}
                                            initialMax={priceMax}
                                            priceRanges={priceRanges}
                                            onChange={handlePriceChange}
                                        />
                                    </FilterGroup>

                                    <FilterGroup
                                        title="Stok Durumu"
                                        initialOpen={true}
                                    >
                                        <div className="flex items-center">
                                            <input
                                                id="in_stock"
                                                type="checkbox"
                                                className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                                                checked={inStock}
                                                onChange={(e) =>
                                                    setInStock(e.target.checked)
                                                }
                                            />
                                            <label
                                                htmlFor="in_stock"
                                                className="ml-2 text-sm text-gray-700"
                                            >
                                                Sadece Stokta Olanlar
                                            </label>
                                        </div>
                                    </FilterGroup>

                                    {/* Filtrele butonu */}
                                    <div className="mt-6">
                                        <button
                                            type="button"
                                            onClick={applyFilters}
                                            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-200"
                                        >
                                            Filtrele
                                        </button>
                                    </div>

                                    {/* Özellik filtreleri */}
                                    {availableFilters.attributes &&
                                        Object.entries(
                                            availableFilters.attributes
                                        ).map(([code, attribute]) => (
                                            <FilterGroup
                                                key={code}
                                                title={attribute.name}
                                            >
                                                <div className="space-y-2">
                                                    {attribute.values.map(
                                                        (value) => (
                                                            <div
                                                                key={value}
                                                                className="flex items-center"
                                                            >
                                                                <input
                                                                    id={`attr_${code}_${value}`}
                                                                    type="checkbox"
                                                                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                                                                    checked={
                                                                        selectedAttributes[
                                                                            code
                                                                        ]?.includes(
                                                                            value
                                                                        ) ||
                                                                        false
                                                                    }
                                                                    onChange={(
                                                                        e
                                                                    ) =>
                                                                        handleAttributeChange(
                                                                            code,
                                                                            value,
                                                                            e
                                                                                .target
                                                                                .checked
                                                                        )
                                                                    }
                                                                />
                                                                <label
                                                                    htmlFor={`attr_${code}_${value}`}
                                                                    className="ml-2 text-sm text-gray-700"
                                                                >
                                                                    {value}
                                                                </label>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            </FilterGroup>
                                        ))}

                                    {/* Varyant filtreleri */}
                                    {availableFilters.variants &&
                                        Object.entries(
                                            availableFilters.variants
                                        ).map(([code, variant]) => (
                                            <FilterGroup
                                                key={code}
                                                title={variant.name}
                                            >
                                                <div className="space-y-2">
                                                    {variant.values.map(
                                                        (value) => (
                                                            <div
                                                                key={value}
                                                                className="flex items-center"
                                                            >
                                                                <input
                                                                    id={`var_${code}_${value}`}
                                                                    type="checkbox"
                                                                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                                                                    checked={
                                                                        selectedVariants[
                                                                            code
                                                                        ]?.includes(
                                                                            value
                                                                        ) ||
                                                                        false
                                                                    }
                                                                    onChange={(
                                                                        e
                                                                    ) =>
                                                                        handleVariantChange(
                                                                            code,
                                                                            value,
                                                                            e
                                                                                .target
                                                                                .checked
                                                                        )
                                                                    }
                                                                />
                                                                <label
                                                                    htmlFor={`var_${code}_${value}`}
                                                                    className="ml-2 text-sm text-gray-700"
                                                                >
                                                                    {value}
                                                                </label>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            </FilterGroup>
                                        ))}
                                </form>
                            </div>
                        </div>

                        {/* Main Content */}
                        <div className="md:w-3/4">
                            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
                                <h1 className="text-2xl font-bold mb-4">
                                    {category.name} Ürünleri
                                </h1>
                                {category.description && (
                                    <p className="text-gray-600 mb-4">
                                        {category.description}
                                    </p>
                                )}

                                {/* Ürün Sayısı ve Mobil Sıralama */}
                                <div className="flex justify-between items-center mb-6">
                                    <p className="text-gray-700">
                                        <strong>{products.total}</strong> ürün
                                        bulundu
                                    </p>
                                    <div className="flex items-center">
                                        <label
                                            htmlFor="mobile-sort"
                                            className="mr-2 text-sm text-gray-600 hidden sm:inline"
                                        >
                                            Sırala:
                                        </label>
                                        <select
                                            id="mobile-sort"
                                            className="px-2 py-1 border border-gray-300 rounded-md text-sm"
                                            value={sortBy || ""}
                                            onChange={(e) => {
                                                const newSortValue =
                                                    e.target.value;
                                                setSortBy(newSortValue);
                                                setTimeout(() => {
                                                    // Boş değer kontrolü
                                                    if (
                                                        newSortValue === null ||
                                                        newSortValue ===
                                                            undefined
                                                    ) {
                                                        setSortBy("");
                                                    }
                                                    applyFilters();
                                                }, 100);
                                            }}
                                        >
                                            <option value="">
                                                Sıralama Seçiniz
                                            </option>
                                            <option value="newest">
                                                En Yeniler
                                            </option>
                                            <option value="price_asc">
                                                Fiyat (Düşükten Yükseğe)
                                            </option>
                                            <option value="price_desc">
                                                Fiyat (Yüksekten Düşüğe)
                                            </option>
                                            <option value="name_asc">
                                                İsim (A-Z)
                                            </option>
                                            <option value="name_desc">
                                                İsim (Z-A)
                                            </option>
                                            <option value="popularity">
                                                Popülerlik
                                            </option>
                                            <option value="discount">
                                                İndirim Oranı
                                            </option>
                                        </select>
                                    </div>
                                </div>

                                {/* Products */}
                                {products.data.length > 0 ? (
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                        {products.data.map((product) => (
                                            <ProductCard
                                                key={product.id}
                                                product={product}
                                            />
                                        ))}
                                    </div>
                                ) : (
                                    <div className="text-center py-8">
                                        <p className="text-gray-500 mb-4">
                                            Bu kategoride ürün bulunamadı.
                                        </p>
                                        <button
                                            onClick={clearAllFilters}
                                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                                        >
                                            Filtreleri Temizle
                                        </button>
                                    </div>
                                )}

                                {/* Pagination */}
                                <div className="mt-8">
                                    <Pagination links={products.links} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
