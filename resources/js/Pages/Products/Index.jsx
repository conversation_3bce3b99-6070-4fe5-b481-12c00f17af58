import React, { useState, useEffect } from "react";
import { Link, usePage, router } from "@inertiajs/react";
import MainLayout from "@/Layouts/MainLayout";
import Pagination from "@/Components/Pagination";
import ProductCard from "@/Components/ProductCard";
import PriceRangeSlider from "@/Components/PriceRangeSlider";
import FilterGroup from "@/Components/FilterGroup";
import ActiveFilters from "@/Components/ActiveFilters";
import SeoMeta from "@/Components/SeoMeta";
import { updateCartCount } from "@/Utils/cartUtils";
import { debounce } from "lodash";

export default function Index(props) {
    // Varsayılan değerleri tanımla
    const safeProducts = props.products || { data: [], links: [] };
    const safeCategories = props.categories || [];
    const priceRange = props.priceRange || { min: 0, max: 1000 };
    const priceRanges = props.price_ranges || [];
    const activeFilters = props.filters?.active || {};
    const availableFilters = props.filters?.available || {};

    // State'ler
    const [searchTerm, setSearchTerm] = useState(activeFilters?.q || "");
    const [selectedCategory, setSelectedCategory] = useState(
        activeFilters?.category ? String(activeFilters.category) : ""
    );
    const [sortBy, setSortBy] = useState("");
    const [priceMin, setPriceMin] = useState(activeFilters?.price_min || "");
    const [priceMax, setPriceMax] = useState(activeFilters?.price_max || "");
    const [inStock, setInStock] = useState(activeFilters?.in_stock || false);
    const [selectedAttributes, setSelectedAttributes] = useState(
        activeFilters?.attributes || {}
    );
    const [selectedVariants, setSelectedVariants] = useState(
        activeFilters?.variants || {}
    );
    const [isFilterOpen, setIsFilterOpen] = useState(false);

    // Sayfa yüklenirken sepet sayısını güncelle ve filtreleri ayarla
    useEffect(() => {
        updateCartCount();

        // Aktif filtrelerden sıralama değerini ayarla
        if (activeFilters?.sorting) {
            setSortBy(activeFilters.sorting);
        }

        // URL'den gelen parametreleri kontrol et
        const urlParams = new URLSearchParams(window.location.search);

        // URL'de filtreler parametresi varsa, seçili özellikleri ve varyantları güncelle
        if (urlParams.has("filtreler")) {
            const filterString = urlParams.get("filtreler");
            console.log("URL'den alınan filtre string:", filterString);

            if (filterString) {
                const filterParts = filterString.split(";");

                filterParts.forEach((part) => {
                    const [key, valueStr] = part.split(":");
                    if (key && valueStr) {
                        const values = valueStr.split(",");

                        // Özellik mi varyant mı kontrol et (burada basit bir kontrol yapıyoruz)
                        // Gerçek uygulamada daha karmaşık bir kontrol gerekebilir
                        if (
                            availableFilters.attributes &&
                            availableFilters.attributes[key]
                        ) {
                            // Bu bir özellik
                            setSelectedAttributes((prev) => ({
                                ...prev,
                                [key]: values,
                            }));
                        } else if (
                            availableFilters.variants &&
                            availableFilters.variants[key]
                        ) {
                            // Bu bir varyant
                            setSelectedVariants((prev) => ({
                                ...prev,
                                [key]: values,
                            }));
                        } else {
                            // Bilinmeyen bir filtre, varsayılan olarak özellik olarak kabul et
                            setSelectedAttributes((prev) => ({
                                ...prev,
                                [key]: values,
                            }));
                        }
                    }
                });
            }
        }

        // Diğer özellikler için de benzer kontroller yapılabilir
        console.log(
            "URL parametreleri:",
            Object.fromEntries(urlParams.entries())
        );
        console.log("Aktif filtreler:", activeFilters);
    }, []);

    // Fiyat aralığı değiştiğinde
    const handlePriceChange = debounce(({ min, max }) => {
        setPriceMin(min);
        setPriceMax(max);
        // Filtreleme işlemi "Filtrele" butonuna tıklandığında yapılacak
    }, 500);

    // Özellik değiştiğinde
    const handleAttributeChange = (code, value, checked) => {
        // Sadece state'i güncelle, filtreleme işlemi "Filtrele" butonuna tıklandığında yapılacak
        setSelectedAttributes((prev) => {
            const newAttributes = { ...prev };

            if (!newAttributes[code]) {
                newAttributes[code] = [];
            }

            if (checked) {
                if (!newAttributes[code].includes(value)) {
                    newAttributes[code] = [...newAttributes[code], value];
                }
            } else {
                newAttributes[code] = newAttributes[code].filter(
                    (v) => v !== value
                );
                if (newAttributes[code].length === 0) {
                    delete newAttributes[code];
                }
            }

            return newAttributes;
        });
    };

    // Varyant değiştiğinde
    const handleVariantChange = (code, value, checked) => {
        // Sadece state'i güncelle, filtreleme işlemi "Filtrele" butonuna tıklandığında yapılacak
        setSelectedVariants((prev) => {
            const newVariants = { ...prev };

            if (!newVariants[code]) {
                newVariants[code] = [];
            }

            if (checked) {
                if (!newVariants[code].includes(value)) {
                    newVariants[code] = [...newVariants[code], value];
                }
            } else {
                newVariants[code] = newVariants[code].filter(
                    (v) => v !== value
                );
                if (newVariants[code].length === 0) {
                    delete newVariants[code];
                }
            }

            return newVariants;
        });
    };

    // Arama formunu gönder
    const handleSearch = (e) => {
        e.preventDefault();
        applyFilters();
    };

    // Filtreleri uygula
    const applyFilters = () => {
        // Filtre parametrelerini oluştur
        const params = {};
        const filterParts = [];

        // Temel filtreler
        if (searchTerm) params.q = searchTerm;
        if (selectedCategory) params.category = selectedCategory;
        // Sort değeri boş değilse ekle
        if (sortBy && sortBy !== "" && sortBy !== null && sortBy !== undefined)
            params.sort = sortBy;

        // Fiyat aralığı
        if (priceMin !== "") params.price_min = priceMin;
        if (priceMax !== "") params.price_max = priceMax;

        // Stok durumu
        if (inStock) params.in_stock = "1";

        // Özellik filtreleri
        if (Object.keys(selectedAttributes).length > 0) {
            // SEO dostu URL için filtre parçalarını oluştur
            Object.entries(selectedAttributes).forEach(([code, values]) => {
                if (values.length > 0) {
                    filterParts.push(`${code}:${values.join(",")}`);
                }
            });
        }

        // Varyant filtreleri
        if (Object.keys(selectedVariants).length > 0) {
            // SEO dostu URL için filtre parçalarını oluştur
            Object.entries(selectedVariants).forEach(([code, values]) => {
                if (values.length > 0) {
                    filterParts.push(`${code}:${values.join(",")}`);
                }
            });
        }

        // Sayfa başına ürün sayısı artık sabit (21)

        // Debug için konsola yazdır
        console.log("Filter params:", params);
        console.log("Filter parts:", filterParts);

        // Eğer filtre parçaları varsa, SEO dostu URL oluştur
        if (filterParts.length > 0) {
            const filterUrl = `/products?filtreler=${filterParts.join(";")}`;
            console.log("Using SEO friendly filter URL:", filterUrl);

            // Inertia.js ile sayfa yenilemeden URL'i güncelle
            router.visit(filterUrl, {
                preserveState: true,
                replace: true,
                preserveScroll: true,
                only: ["products", "filters"],
            });
        } else {
            // Filtre yoksa normal URL'e git
            console.log("Using standard URL with params");
            router.visit("/products", {
                data: params,
                preserveState: true,
                replace: true,
                preserveScroll: true,
                only: ["products", "filters"],
            });
        }
    };

    // Filtreyi kaldır
    const removeFilter = (key, value = null) => {
        if (key === "q") {
            setSearchTerm("");
        } else if (key === "category") {
            setSelectedCategory("");
        } else if (key === "sort") {
            setSortBy("");
        } else if (key === "price_min") {
            setPriceMin(priceRange.min);
        } else if (key === "price_max") {
            setPriceMax(priceRange.max);
        } else if (key === "in_stock") {
            setInStock(false);
        } else if (key.startsWith("attributes.")) {
            const code = key.split(".")[1];
            handleAttributeChange(code, value, false);
        } else if (key.startsWith("variants.")) {
            const code = key.split(".")[1];
            handleVariantChange(code, value, false);
        }

        // Filtreleri uygula
        applyFilters();
    };

    // Tüm filtreleri temizle
    const clearAllFilters = () => {
        setSearchTerm("");
        setSelectedCategory("");
        setSortBy("");
        setPriceMin("");
        setPriceMax("");
        setInStock(false);
        setSelectedAttributes({});
        setSelectedVariants({});

        // Inertia.js ile sayfa yenilemeden URL'i güncelle
        router.visit("/products", {
            preserveState: true,
            replace: true,
            preserveScroll: true,
            only: ["products", "filters"],
        });
    };

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    return (
        <MainLayout>
            <SeoMeta meta={props.meta} />

            <div className="bg-gray-100 py-8">
                <div className="container mx-auto px-4">
                    <h1 className="text-2xl font-bold mb-6">Ürünler</h1>

                    {/* Mobil filtre butonu */}
                    <div className="md:hidden mb-4">
                        <button
                            onClick={() => setIsFilterOpen(!isFilterOpen)}
                            className="w-full bg-white rounded-lg shadow-md p-3 flex justify-between items-center"
                        >
                            <span className="font-medium">Filtreler</span>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                            >
                                <path
                                    fillRule="evenodd"
                                    d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                                    clipRule="evenodd"
                                />
                            </svg>
                        </button>
                    </div>

                    {/* Aktif filtreler */}
                    {Object.keys(activeFilters).length > 0 && (
                        <ActiveFilters
                            filters={{ ...activeFilters, sort: undefined }}
                            onRemove={removeFilter}
                            onClearAll={clearAllFilters}
                        />
                    )}

                    <div className="flex flex-col md:flex-row gap-6">
                        {/* Filtreler */}
                        <div
                            className={`md:w-1/4 ${
                                isFilterOpen ? "block" : "hidden md:block"
                            }`}
                        >
                            <div className="bg-white rounded-lg shadow-md p-4 mb-4">
                                <form onSubmit={handleSearch}>
                                    <div className="mb-4">
                                        <label
                                            htmlFor="category"
                                            className="block text-sm font-medium text-gray-700 mb-1"
                                        >
                                            Kategori
                                        </label>
                                        <select
                                            id="category"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                            value={selectedCategory}
                                            onChange={(e) => {
                                                setSelectedCategory(
                                                    e.target.value
                                                );
                                            }}
                                        >
                                            <option value="">
                                                Tüm Kategoriler
                                            </option>
                                            {safeCategories.map((category) => (
                                                <option
                                                    key={category.id}
                                                    value={category.id}
                                                >
                                                    {category.name}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    <div className="mb-4">
                                        <label
                                            htmlFor="sort"
                                            className="block text-sm font-medium text-gray-700 mb-1"
                                        >
                                            Sıralama
                                        </label>
                                        <select
                                            id="sort"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                            value={sortBy || ""}
                                            onChange={(e) => {
                                                const newSortValue =
                                                    e.target.value;
                                                setSortBy(newSortValue);
                                            }}
                                        >
                                            <option value="">
                                                Sıralama Seçiniz
                                            </option>
                                            <option value="newest">
                                                En Yeniler
                                            </option>
                                            <option value="price_asc">
                                                Fiyat (Düşükten Yükseğe)
                                            </option>
                                            <option value="price_desc">
                                                Fiyat (Yüksekten Düşüğe)
                                            </option>
                                            <option value="name_asc">
                                                İsim (A-Z)
                                            </option>
                                            <option value="name_desc">
                                                İsim (Z-A)
                                            </option>
                                            <option value="popularity">
                                                Popülerlik
                                            </option>
                                            <option value="discount">
                                                İndirim Oranı
                                            </option>
                                        </select>
                                    </div>

                                    <div className="mb-4">
                                        <h3 className="text-sm font-medium text-gray-900 mb-3">
                                            Fiyat Aralığı
                                        </h3>
                                        <PriceRangeSlider
                                            min={priceRange.min}
                                            max={priceRange.max}
                                            initialMin={priceMin}
                                            initialMax={priceMax}
                                            priceRanges={priceRanges}
                                            onChange={handlePriceChange}
                                        />
                                    </div>

                                    <FilterGroup
                                        title="Stok Durumu"
                                        initialOpen={true}
                                    >
                                        <div className="flex items-center">
                                            <input
                                                id="in_stock"
                                                type="checkbox"
                                                className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                                                checked={inStock}
                                                onChange={(e) => {
                                                    setInStock(
                                                        e.target.checked
                                                    );
                                                }}
                                            />
                                            <label
                                                htmlFor="in_stock"
                                                className="ml-2 text-sm text-gray-700"
                                            >
                                                Sadece Stokta Olanlar
                                            </label>
                                        </div>
                                    </FilterGroup>

                                    {/* Filtrele butonu */}
                                    <div className="mt-6">
                                        <button
                                            type="button"
                                            onClick={applyFilters}
                                            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-200"
                                        >
                                            Filtrele
                                        </button>
                                    </div>

                                    {/* Özellik filtreleri */}
                                    {availableFilters.attributes &&
                                        Object.entries(
                                            availableFilters.attributes
                                        ).map(([code, attribute]) => (
                                            <FilterGroup
                                                key={code}
                                                title={attribute.name}
                                                initialOpen={
                                                    selectedAttributes[code]
                                                        ?.length > 0
                                                        ? true
                                                        : "empty"
                                                }
                                            >
                                                <div className="space-y-2">
                                                    {attribute.values.map(
                                                        (value) => (
                                                            <div
                                                                key={value}
                                                                className="flex items-center"
                                                            >
                                                                <div
                                                                    className="w-full flex items-center cursor-pointer py-1 px-1 hover:bg-gray-100 rounded"
                                                                    onClick={(
                                                                        e
                                                                    ) => {
                                                                        e.preventDefault();
                                                                        e.stopPropagation();
                                                                        const isChecked =
                                                                            selectedAttributes[
                                                                                code
                                                                            ]?.includes(
                                                                                value
                                                                            ) ||
                                                                            false;

                                                                        // Özellik değişikliğini işle
                                                                        handleAttributeChange(
                                                                            code,
                                                                            value,
                                                                            !isChecked
                                                                        );
                                                                    }}
                                                                >
                                                                    <input
                                                                        id={`attr_${code}_${value}`}
                                                                        type="checkbox"
                                                                        className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                                                                        checked={
                                                                            selectedAttributes[
                                                                                code
                                                                            ]?.includes(
                                                                                value
                                                                            ) ||
                                                                            false
                                                                        }
                                                                        onChange={(
                                                                            e
                                                                        ) => {
                                                                            e.stopPropagation();
                                                                            handleAttributeChange(
                                                                                code,
                                                                                value,
                                                                                e
                                                                                    .target
                                                                                    .checked
                                                                            );
                                                                        }}
                                                                    />
                                                                    <label
                                                                        htmlFor={`attr_${code}_${value}`}
                                                                        className={`ml-2 text-sm ${
                                                                            selectedAttributes[
                                                                                code
                                                                            ]?.includes(
                                                                                value
                                                                            )
                                                                                ? "font-semibold text-blue-600"
                                                                                : "text-gray-700"
                                                                        } cursor-pointer w-full`}
                                                                    >
                                                                        {value}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            </FilterGroup>
                                        ))}

                                    {/* Varyant filtreleri */}
                                    {availableFilters.variants &&
                                        Object.entries(
                                            availableFilters.variants
                                        ).map(([code, variant]) => (
                                            <FilterGroup
                                                key={code}
                                                title={variant.name}
                                                initialOpen={
                                                    selectedVariants[code]
                                                        ?.length > 0
                                                        ? true
                                                        : "empty"
                                                }
                                            >
                                                <div className="space-y-2">
                                                    {variant.values.map(
                                                        (value) => (
                                                            <div
                                                                key={value}
                                                                className="flex items-center"
                                                            >
                                                                <div
                                                                    className="w-full flex items-center cursor-pointer py-1 px-1 hover:bg-gray-100 rounded"
                                                                    onClick={(
                                                                        e
                                                                    ) => {
                                                                        e.preventDefault();
                                                                        e.stopPropagation();
                                                                        const isChecked =
                                                                            selectedVariants[
                                                                                code
                                                                            ]?.includes(
                                                                                value
                                                                            ) ||
                                                                            false;
                                                                        handleVariantChange(
                                                                            code,
                                                                            value,
                                                                            !isChecked
                                                                        );

                                                                        // Checkbox'ı manuel olarak güncelle
                                                                        const checkbox =
                                                                            document.getElementById(
                                                                                `var_${code}_${value}`
                                                                            );
                                                                        if (
                                                                            checkbox
                                                                        ) {
                                                                            checkbox.checked =
                                                                                !isChecked;
                                                                        }
                                                                    }}
                                                                >
                                                                    <input
                                                                        id={`var_${code}_${value}`}
                                                                        type="checkbox"
                                                                        className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                                                                        checked={
                                                                            selectedVariants[
                                                                                code
                                                                            ]?.includes(
                                                                                value
                                                                            ) ||
                                                                            false
                                                                        }
                                                                        onChange={(
                                                                            e
                                                                        ) => {
                                                                            e.stopPropagation();
                                                                            handleVariantChange(
                                                                                code,
                                                                                value,
                                                                                e
                                                                                    .target
                                                                                    .checked
                                                                            );
                                                                        }}
                                                                        onClick={(
                                                                            e
                                                                        ) =>
                                                                            e.stopPropagation()
                                                                        }
                                                                    />
                                                                    <label
                                                                        htmlFor={`var_${code}_${value}`}
                                                                        className={`ml-2 text-sm ${
                                                                            selectedVariants[
                                                                                code
                                                                            ]?.includes(
                                                                                value
                                                                            )
                                                                                ? "font-semibold text-blue-600"
                                                                                : "text-gray-700"
                                                                        } cursor-pointer w-full`}
                                                                    >
                                                                        {value}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            </FilterGroup>
                                        ))}

                                    <button
                                        type="submit"
                                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 mt-4"
                                    >
                                        Filtrele
                                    </button>
                                </form>
                            </div>
                        </div>

                        {/* Ürün Listesi */}
                        <div className="md:w-3/4">
                            {safeProducts.data.length === 0 ? (
                                <div className="bg-white rounded-lg shadow-md p-6 text-center">
                                    <p className="text-gray-500 mb-4">
                                        Aradığınız kriterlere uygun ürün
                                        bulunamadı.
                                    </p>
                                    <button
                                        onClick={clearAllFilters}
                                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                                    >
                                        Filtreleri Temizle
                                    </button>
                                </div>
                            ) : (
                                <>
                                    <div className="bg-white rounded-lg shadow-md p-4 mb-4 flex justify-between items-center">
                                        <p className="text-gray-700">
                                            <strong>
                                                {safeProducts.total}
                                            </strong>{" "}
                                            ürün bulundu
                                        </p>
                                        <div className="flex items-center">
                                            <label
                                                htmlFor="mobile-sort"
                                                className="mr-2 text-sm text-gray-600 hidden sm:inline"
                                            >
                                                Sırala:
                                            </label>
                                            <select
                                                id="mobile-sort"
                                                className="px-2 py-1 border border-gray-300 rounded-md text-sm"
                                                value={sortBy || ""}
                                                onChange={(e) => {
                                                    const newSortValue =
                                                        e.target.value;
                                                    setSortBy(newSortValue);

                                                    // Yeni URL oluştur ve yönlendir
                                                    const params =
                                                        new URLSearchParams(
                                                            window.location.search
                                                        );

                                                    // Boş değer kontrolü
                                                    if (newSortValue) {
                                                        params.set(
                                                            "sort",
                                                            newSortValue
                                                        );
                                                    } else {
                                                        params.delete("sort");
                                                    }
                                                    window.location.href = `/products?${params.toString()}`;
                                                }}
                                            >
                                                <option value="">
                                                    Sıralama Seçiniz
                                                </option>
                                                <option value="newest">
                                                    En Yeniler
                                                </option>
                                                <option value="price_asc">
                                                    Fiyat (Düşükten Yükseğe)
                                                </option>
                                                <option value="price_desc">
                                                    Fiyat (Yüksekten Düşüğe)
                                                </option>
                                                <option value="name_asc">
                                                    İsim (A-Z)
                                                </option>
                                                <option value="name_desc">
                                                    İsim (Z-A)
                                                </option>
                                                <option value="popularity">
                                                    Popülerlik
                                                </option>
                                                <option value="discount">
                                                    İndirim Oranı
                                                </option>
                                            </select>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                                        {safeProducts.data.map((product) => (
                                            <ProductCard
                                                key={product.id}
                                                product={product}
                                            />
                                        ))}
                                    </div>

                                    <Pagination links={safeProducts.links} />
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
