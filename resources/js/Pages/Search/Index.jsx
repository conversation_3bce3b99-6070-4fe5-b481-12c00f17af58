import React, { useState, useEffect } from "react";
import { Head } from "@inertiajs/react";
import MainLayout from "@/Layouts/MainLayout";
import ProductCard from "@/Components/ProductCard";
import Pagination from "@/Components/Pagination";
import { updateCartCount } from "@/Utils/cartUtils";

export default function Index(props) {
    // Varsayılan değerleri tanımla
    const safeProducts = props.products || { data: [], links: [] };
    const safeCategories = props.categories || [];
    const safeAllCategories = props.allCategories || [];
    const query = props.query || "";
    const filters = props.filters || {};

    // State'ler
    const [searchTerm, setSearchTerm] = useState(query);
    const [selectedCategory, setSelectedCategory] = useState(
        filters.category || ""
    );
    const [sortBy, setSortBy] = useState("relevance");

    // <PERSON><PERSON> yüklenirken sepet sayısını güncelle ve filtreleri ayarla
    useEffect(() => {
        updateCartCount();

        // Aktif filtrelerden sıralama değerini ayarla
        if (filters?.sort) {
            setSortBy(filters.sort);
        }
    }, []);

    // Arama formunu gönder
    const handleSearch = (e) => {
        e.preventDefault();
        let url = `/search?q=${encodeURIComponent(searchTerm)}`;

        if (selectedCategory) {
            url += `&category=${encodeURIComponent(selectedCategory)}`;
        }

        if (
            sortBy &&
            sortBy !== "" &&
            sortBy !== null &&
            sortBy !== undefined
        ) {
            url += `&sort=${encodeURIComponent(sortBy)}`;
        }

        window.location.href = url;
    };

    // Kategori değiştiğinde
    const handleCategoryChange = (e) => {
        setSelectedCategory(e.target.value);
    };

    // Sıralama değiştiğinde
    const handleSortChange = (e) => {
        setSortBy(e.target.value);
    };

    return (
        <MainLayout>
            <Head>
                <title>{`"${query}" için arama sonuçları`}</title>
                <meta
                    name="description"
                    content={`${query} için arama sonuçları`}
                />
            </Head>

            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <h1 className="text-2xl font-semibold text-gray-900 mb-6">
                        "{query}" için arama sonuçları
                    </h1>

                    <div className="flex flex-col md:flex-row gap-6">
                        {/* Filtreler */}
                        <div className="md:w-1/4">
                            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
                                <h2 className="text-lg font-medium mb-4">
                                    Arama ve Filtreler
                                </h2>
                                <form onSubmit={handleSearch}>
                                    <div className="mb-4">
                                        <label
                                            htmlFor="search"
                                            className="block text-sm font-medium text-gray-700 mb-1"
                                        >
                                            Arama
                                        </label>
                                        <input
                                            type="text"
                                            id="search"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                            value={searchTerm}
                                            onChange={(e) =>
                                                setSearchTerm(e.target.value)
                                            }
                                            placeholder="Ürün ara..."
                                        />
                                    </div>

                                    <div className="mb-4">
                                        <label
                                            htmlFor="category"
                                            className="block text-sm font-medium text-gray-700 mb-1"
                                        >
                                            Kategori
                                        </label>
                                        <select
                                            id="category"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                            value={selectedCategory}
                                            onChange={handleCategoryChange}
                                        >
                                            <option value="">
                                                Tüm Kategoriler
                                            </option>
                                            {safeAllCategories.map(
                                                (category) => (
                                                    <option
                                                        key={category.id}
                                                        value={category.id}
                                                    >
                                                        {category.name}
                                                    </option>
                                                )
                                            )}
                                        </select>
                                    </div>

                                    <div className="mb-4">
                                        <label
                                            htmlFor="sort"
                                            className="block text-sm font-medium text-gray-700 mb-1"
                                        >
                                            Sıralama
                                        </label>
                                        <select
                                            id="sort"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                            value={sortBy || ""}
                                            onChange={handleSortChange}
                                        >
                                            <option value="relevance">
                                                İlgililik
                                            </option>
                                            <option value="newest">
                                                En Yeniler
                                            </option>
                                            <option value="price_asc">
                                                Fiyat (Düşükten Yükseğe)
                                            </option>
                                            <option value="price_desc">
                                                Fiyat (Yüksekten Düşüğe)
                                            </option>
                                            <option value="name_asc">
                                                İsim (A-Z)
                                            </option>
                                            <option value="name_desc">
                                                İsim (Z-A)
                                            </option>
                                        </select>
                                    </div>

                                    <button
                                        type="submit"
                                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
                                    >
                                        Filtrele
                                    </button>
                                </form>
                            </div>

                            {/* İlgili Kategoriler */}
                            {safeCategories.length > 0 && (
                                <div className="bg-white rounded-lg shadow-md p-4">
                                    <h2 className="text-lg font-medium mb-4">
                                        İlgili Kategoriler
                                    </h2>
                                    <ul className="space-y-2">
                                        {safeCategories.map((category) => (
                                            <li key={category.id}>
                                                <a
                                                    href={category.url}
                                                    className="text-blue-600 hover:underline"
                                                >
                                                    {category.name}
                                                </a>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>

                        {/* Ürün Listesi */}
                        <div className="md:w-3/4">
                            {safeProducts.data.length === 0 ? (
                                <div className="bg-white rounded-lg shadow-md p-6 text-center">
                                    <p className="text-gray-500 mb-4">
                                        Aradığınız kriterlere uygun ürün
                                        bulunamadı.
                                    </p>
                                    <button
                                        onClick={() => {
                                            setSearchTerm("");
                                            setSelectedCategory("");
                                            setSortBy("");
                                            window.location.href = "/products";
                                        }}
                                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                                    >
                                        Tüm Ürünleri Görüntüle
                                    </button>
                                </div>
                            ) : (
                                <>
                                    <div className="mb-4 bg-white rounded-lg shadow-md p-4">
                                        <p className="text-gray-700">
                                            <strong>
                                                {safeProducts.total}
                                            </strong>{" "}
                                            sonuç bulundu.
                                        </p>
                                    </div>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                                        {safeProducts.data.map((product) => (
                                            <ProductCard
                                                key={product.id}
                                                product={product}
                                            />
                                        ))}
                                    </div>

                                    <Pagination links={safeProducts.links} />
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
