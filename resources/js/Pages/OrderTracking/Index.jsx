import React, { useState } from "react";
import { Head, useForm } from "@inertiajs/react";
import MainLayout from "@/Layouts/MainLayout";
import InputError from "@/Components/InputError";

export default function Index({ errors }) {
    const { data, setData, post, processing, reset } = useForm({
        order_number: "",
        email: "",
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route("order.track"));
    };

    return (
        <MainLayout>
            <Head title="Sipariş Takip" />

            <div className="py-12">
                <div className="max-w-2xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6">
                            <h2 className="text-2xl font-bold mb-6 text-center">
                                Sipar<PERSON>ş Takip
                            </h2>

                            <p className="mb-6 text-gray-600 text-center">
                                Siparişinizin durumunu kontrol etmek için lütfen sipariş numaranızı ve sipariş verirken kullandığınız e-posta adresini girin.
                            </p>

                            <form onSubmit={handleSubmit}>
                                <div className="mb-4">
                                    <label
                                        htmlFor="order_number"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Sipariş Numarası
                                    </label>
                                    <input
                                        id="order_number"
                                        type="text"
                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        value={data.order_number}
                                        onChange={(e) =>
                                            setData("order_number", e.target.value)
                                        }
                                        required
                                        autoFocus
                                        placeholder="Örn: ORD-ABCD1234"
                                    />
                                    <InputError
                                        message={errors.order_number}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-6">
                                    <label
                                        htmlFor="email"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        E-posta Adresi
                                    </label>
                                    <input
                                        id="email"
                                        type="email"
                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        value={data.email}
                                        onChange={(e) =>
                                            setData("email", e.target.value)
                                        }
                                        required
                                        placeholder="Örn: <EMAIL>"
                                    />
                                    <InputError
                                        message={errors.email}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="flex items-center justify-center">
                                    <button
                                        type="submit"
                                        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded focus:outline-none focus:shadow-outline"
                                        disabled={processing}
                                    >
                                        {processing
                                            ? "Aranıyor..."
                                            : "Siparişi Ara"}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
