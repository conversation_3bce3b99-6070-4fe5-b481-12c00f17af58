import React from "react";
import { Head, Link } from "@inertiajs/react";
import CustomerLayout from "@/Layouts/CustomerLayout";
import Pagination from "@/Components/Pagination";
import { formatPrice } from "@/Utils/cartUtils";

export default function Index({ orders }) {
    // Sipariş durumuna göre renk ve metin belirle
    const getStatusInfo = (status) => {
        switch (status) {
            case "completed":
                return {
                    color: "bg-green-100 text-green-800",
                    text: "Tamamlandı",
                };
            case "processing":
                return {
                    color: "bg-blue-100 text-blue-800",
                    text: "İşleniyor",
                };
            case "pending":
                return {
                    color: "bg-yellow-100 text-yellow-800",
                    text: "Beklemede",
                };
            case "cancelled":
                return {
                    color: "bg-red-100 text-red-800",
                    text: "İptal Edildi",
                };
            default:
                return {
                    color: "bg-gray-100 text-gray-800",
                    text: status,
                };
        }
    };

    // Ödeme durumuna göre renk ve metin belirle
    const getPaymentStatusInfo = (status) => {
        switch (status) {
            case "paid":
                return {
                    color: "bg-green-100 text-green-800",
                    text: "Ödendi",
                };
            case "pending":
                return {
                    color: "bg-yellow-100 text-yellow-800",
                    text: "Beklemede",
                };
            case "failed":
                return {
                    color: "bg-red-100 text-red-800",
                    text: "Başarısız",
                };
            case "refunded":
                return {
                    color: "bg-purple-100 text-purple-800",
                    text: "İade Edildi",
                };
            default:
                return {
                    color: "bg-gray-100 text-gray-800",
                    text: status,
                };
        }
    };

    return (
        <CustomerLayout>
            <Head title="Siparişlerim" />

            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                        Siparişlerim
                    </h2>

                    <div className="bg-white overflow-hidden shadow-sm rounded-lg">
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Sipariş No
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Tarih
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Tutar
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Durum
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Ödeme Durumu
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            İşlem
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {orders.data.length > 0 ? (
                                        orders.data.map((order) => {
                                            const statusInfo = getStatusInfo(
                                                order.status
                                            );
                                            return (
                                                <tr key={order.id}>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                        #{order.order_number}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {new Date(
                                                            order.created_at
                                                        ).toLocaleDateString(
                                                            "tr-TR"
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {formatPrice(
                                                            order.total_amount
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span
                                                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusInfo.color}`}
                                                        >
                                                            {statusInfo.text}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span
                                                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                                getPaymentStatusInfo(
                                                                    order.payment_status
                                                                ).color
                                                            }`}
                                                        >
                                                            {
                                                                getPaymentStatusInfo(
                                                                    order.payment_status
                                                                ).text
                                                            }
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        <Link
                                                            href={route(
                                                                "customer.orders.show",
                                                                order.order_number
                                                            )}
                                                            className="text-blue-600 hover:text-blue-900"
                                                        >
                                                            Detaylar
                                                        </Link>
                                                    </td>
                                                </tr>
                                            );
                                        })
                                    ) : (
                                        <tr>
                                            <td
                                                colSpan="6"
                                                className="px-6 py-4 text-center text-sm text-gray-500"
                                            >
                                                Henüz sipariş bulunmamaktadır.
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>
                        <div className="px-6 py-4 border-t border-gray-200">
                            <Pagination links={orders.links} />
                        </div>
                    </div>
                </div>
            </div>
        </CustomerLayout>
    );
}
