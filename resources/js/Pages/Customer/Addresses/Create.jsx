import React from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import CustomerLayout from "@/Layouts/CustomerLayout";
import InputError from "@/Components/InputError";
import { toast } from "react-hot-toast";
import LocationFields from "@/Components/Location/LocationFields";

export default function Create({ turkeyId }) {
    const { data, setData, post, processing, errors, reset } = useForm({
        title: "",
        name: "",
        phone: "",
        address: "",
        district: "",
        city: "",
        zip: "",
        is_default: false,
        country_id: turkeyId || "",
        state_id: "",
        city_id: "",
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route("customer.addresses.store"), {
            preserveScroll: true,
            onSuccess: () => {
                toast.success("Adres başarıyla eklendi");
                reset();
            },
        });
    };

    return (
        <CustomerLayout>
            <Head title="Yeni Adres Ekle" />

            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-2xl font-semibold text-gray-900">
                            Yeni Adres Ekle
                        </h2>
                        <Link
                            href={route("customer.addresses")}
                            className="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300"
                        >
                            Adreslerime Dön
                        </Link>
                    </div>

                    <div className="bg-white overflow-hidden shadow-sm rounded-lg">
                        <div className="p-6">
                            <form onSubmit={handleSubmit}>
                                <div className="mb-4">
                                    <label
                                        htmlFor="title"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Adres Başlığı
                                    </label>
                                    <input
                                        id="title"
                                        type="text"
                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        value={data.title}
                                        onChange={(e) =>
                                            setData("title", e.target.value)
                                        }
                                        placeholder="Örn: Ev, İş"
                                        required
                                    />
                                    <InputError
                                        message={errors.title}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <label
                                        htmlFor="name"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Ad Soyad
                                    </label>
                                    <input
                                        id="name"
                                        type="text"
                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        value={data.name}
                                        onChange={(e) =>
                                            setData("name", e.target.value)
                                        }
                                        required
                                    />
                                    <InputError
                                        message={errors.name}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <label
                                        htmlFor="phone"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Telefon
                                    </label>
                                    <input
                                        id="phone"
                                        type="text"
                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        value={data.phone}
                                        onChange={(e) =>
                                            setData("phone", e.target.value)
                                        }
                                        required
                                    />
                                    <InputError
                                        message={errors.phone}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <label
                                        htmlFor="address"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Adres
                                    </label>
                                    <textarea
                                        id="address"
                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        value={data.address}
                                        onChange={(e) =>
                                            setData("address", e.target.value)
                                        }
                                        rows="3"
                                        required
                                    ></textarea>
                                    <InputError
                                        message={errors.address}
                                        className="mt-2"
                                    />
                                </div>

                                {/* Konum Seçimi */}
                                <div className="mb-4">
                                    <LocationFields
                                        countryId={data.country_id}
                                        stateId={data.state_id}
                                        cityId={data.city_id}
                                        onCountryChange={(value) =>
                                            setData({
                                                ...data,
                                                country_id: value,
                                                state_id: "",
                                                city_id: "",
                                            })
                                        }
                                        onStateChange={(value) =>
                                            setData({
                                                ...data,
                                                state_id: value,
                                                city_id: "",
                                            })
                                        }
                                        onCityChange={(value) =>
                                            setData({ ...data, city_id: value })
                                        }
                                        required={true}
                                    />
                                    <InputError
                                        message={errors.country_id}
                                        className="mt-2"
                                    />
                                    <InputError
                                        message={errors.state_id}
                                        className="mt-2"
                                    />
                                    <InputError
                                        message={errors.city_id}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                    <div>
                                        <label
                                            htmlFor="district"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            İlçe/Mahalle
                                        </label>
                                        <input
                                            id="district"
                                            type="text"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.district}
                                            onChange={(e) =>
                                                setData(
                                                    "district",
                                                    e.target.value
                                                )
                                            }
                                            required
                                        />
                                        <InputError
                                            message={errors.district}
                                            className="mt-2"
                                        />
                                    </div>

                                    <div>
                                        <label
                                            htmlFor="city"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            İl Adı
                                        </label>
                                        <input
                                            id="city"
                                            type="text"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.city}
                                            onChange={(e) =>
                                                setData("city", e.target.value)
                                            }
                                            required
                                        />
                                        <InputError
                                            message={errors.city}
                                            className="mt-2"
                                        />
                                    </div>

                                    <div>
                                        <label
                                            htmlFor="zip"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Posta Kodu
                                        </label>
                                        <input
                                            id="zip"
                                            type="text"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.zip}
                                            onChange={(e) =>
                                                setData("zip", e.target.value)
                                            }
                                        />
                                        <InputError
                                            message={errors.zip}
                                            className="mt-2"
                                        />
                                    </div>
                                </div>

                                <div className="mb-4">
                                    <div className="flex items-center">
                                        <input
                                            id="is_default"
                                            type="checkbox"
                                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                            checked={data.is_default}
                                            onChange={(e) =>
                                                setData(
                                                    "is_default",
                                                    e.target.checked
                                                )
                                            }
                                        />
                                        <label
                                            htmlFor="is_default"
                                            className="ml-2 block text-sm text-gray-700"
                                        >
                                            Varsayılan adres olarak ayarla
                                        </label>
                                    </div>
                                    <InputError
                                        message={errors.is_default}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="flex items-center justify-end mt-6">
                                    <Link
                                        href={route("customer.addresses")}
                                        className="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 mr-2"
                                    >
                                        İptal
                                    </Link>
                                    <button
                                        type="submit"
                                        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                                        disabled={processing}
                                    >
                                        {processing
                                            ? "Kaydediliyor..."
                                            : "Kaydet"}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </CustomerLayout>
    );
}
