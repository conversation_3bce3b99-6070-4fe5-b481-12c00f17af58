import React, { useState } from "react";
import { Head, Link, router } from "@inertiajs/react";
import CustomerLayout from "@/Layouts/CustomerLayout";
import { toast } from "react-hot-toast";

export default function Index({ addresses }) {
    const [loading, setLoading] = useState(false);

    const deleteAddress = (id) => {
        if (!confirm("Bu adresi silmek istediğinize emin misiniz?")) return;

        setLoading(true);
        router.delete(route("customer.addresses.destroy", id), {
            preserveScroll: true,
            onSuccess: () => {
                toast.success("Adres başarıyla silindi");
                setLoading(false);
            },
            onError: () => {
                toast.error("Adres silinirken bir hata olu<PERSON>tu");
                setLoading(false);
            },
        });
    };

    return (
        <CustomerLayout>
            <Head title="Adreslerim" />

            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-2xl font-semibold text-gray-900">
                            Adreslerim
                        </h2>
                        <Link
                            href={route("customer.addresses.create")}
                            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                        >
                            Yeni Adres Ekle
                        </Link>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {addresses.length > 0 ? (
                            addresses.map((address) => (
                                <div
                                    key={address.id}
                                    className="bg-white overflow-hidden shadow-sm rounded-lg"
                                >
                                    <div className="p-6">
                                        <div className="flex justify-between items-start mb-4">
                                            <h3 className="text-lg font-medium text-gray-900">
                                                {address.title}
                                            </h3>
                                            <div className="flex space-x-2">
                                                <Link
                                                    href={route(
                                                        "customer.addresses.edit",
                                                        address.id
                                                    )}
                                                    className="text-blue-600 hover:text-blue-900"
                                                >
                                                    Düzenle
                                                </Link>
                                                <button
                                                    onClick={() =>
                                                        deleteAddress(address.id)
                                                    }
                                                    disabled={loading}
                                                    className="text-red-600 hover:text-red-900"
                                                >
                                                    Sil
                                                </button>
                                            </div>
                                        </div>
                                        <div className="text-sm text-gray-600">
                                            <p className="mb-1">
                                                <span className="font-medium">
                                                    Ad Soyad:
                                                </span>{" "}
                                                {address.name}
                                            </p>
                                            <p className="mb-1">
                                                <span className="font-medium">
                                                    Telefon:
                                                </span>{" "}
                                                {address.phone}
                                            </p>
                                            <p className="mb-1">
                                                <span className="font-medium">
                                                    Adres:
                                                </span>{" "}
                                                {address.address}
                                            </p>
                                            <p className="mb-1">
                                                <span className="font-medium">
                                                    İlçe/İl:
                                                </span>{" "}
                                                {address.district}, {address.city}
                                            </p>
                                            {address.zip && (
                                                <p className="mb-1">
                                                    <span className="font-medium">
                                                        Posta Kodu:
                                                    </span>{" "}
                                                    {address.zip}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="col-span-2 bg-white overflow-hidden shadow-sm rounded-lg">
                                <div className="p-6 text-center">
                                    <p className="text-gray-500 mb-4">
                                        Henüz kayıtlı adresiniz bulunmamaktadır.
                                    </p>
                                    <Link
                                        href={route("customer.addresses.create")}
                                        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                    >
                                        Yeni Adres Ekle
                                    </Link>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </CustomerLayout>
    );
}
