import React from "react";
import { Head, Link, usePage } from "@inertiajs/react";
import MainLayout from "@/Layouts/MainLayout";

export default function Index(props) {
    // Güvenli bir şekilde props'ları al
    const safeProps = usePage().props;

    // Varsayılan değerlerle birlikte props'ları kullan
    const categories = safeProps.categories || [];
    return (
        <MainLayout>
            <Head title="Kategoriler" />

            <div className="py-8 bg-gray-100">
                <div className="container px-4 mx-auto">
                    <h1 className="mb-6 text-2xl font-bold">Kategoriler</h1>

                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                        {categories.map((category) => (
                            <Link
                                key={category.id}
                                href={`/${
                                    category.slug ||
                                    category.name
                                        .toLowerCase()
                                        .replace(/\s+/g, "-")
                                }-c-${category.id}`}
                                className="overflow-hidden transition bg-white rounded-lg shadow-md hover:shadow-lg"
                            >
                                <div className="relative h-40 bg-gray-200">
                                    {category.image ? (
                                        <img
                                            src={category.image}
                                            alt={category.name}
                                            className="object-cover w-full h-full"
                                        />
                                    ) : (
                                        <div className="flex items-center justify-center w-full h-full text-gray-400">
                                            <svg
                                                className="w-12 h-12"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                                />
                                            </svg>
                                        </div>
                                    )}
                                    <div className="absolute inset-0 flex items-center justify-center transition bg-black opacity-0 bg-opacity-30 hover:opacity-100">
                                        <span className="px-4 py-2 font-semibold text-white bg-blue-600 rounded-full">
                                            Ürünleri Gör
                                        </span>
                                    </div>
                                </div>
                                <div className="p-4">
                                    <h3 className="text-lg font-semibold">
                                        {category.name}
                                    </h3>
                                    <div className="mt-1 flex items-center">
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {category.total_product_count ||
                                                category.product_count ||
                                                0}{" "}
                                            ürün
                                        </span>
                                        {category.total_product_count >
                                            category.product_count && (
                                            <span className="ml-2 text-xs text-gray-500">
                                                (Alt kategoriler dahil)
                                            </span>
                                        )}
                                    </div>
                                </div>
                            </Link>
                        ))}
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
