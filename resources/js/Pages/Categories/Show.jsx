import React, { useState, useEffect } from "react";
import { Head, Link } from "@inertiajs/react";
import MainLayout from "@/Layouts/MainLayout";
import Pagination from "@/Components/Pagination";
import ProductCard from "@/Components/ProductCard";
import { toast } from "react-hot-toast";
import axios from "axios";

export default function Show({
    auth,
    category,
    products,
    filters,
    attributeFilters,
    categoryAttributes,
    breadcrumb,
}) {
    // Ürün URL'si oluşturan yardımcı fonksiyon
    const createProductUrl = (product) => {
        // Ürün slug'ını al
        const slug =
            product.slug || product.name.toLowerCase().replace(/\s+/g, "-");

        let stockCode = product.stock_code || "main";

        // Ürün URL'sini oluştur
        if (product.has_variants) {
            // Filtreleme var mı kontrol et
            if (Object.keys(selectedAttributes).length > 0) {
                // Seçilen filtrelere uygun varyant var mı kontrol et
                let matchingVariant = null;

                // Tüm varyantları kontrol et
                if (product.variants) {
                    for (const variant of product.variants) {
                        let isMatch = true;

                        // Her filtre için kontrol et
                        for (const [attributeCode, values] of Object.entries(
                            selectedAttributes
                        )) {
                            if (values && values.length > 0) {
                                // Varyantın bu özelliği var mı ve seçilen değerlerden biri mi?
                                const variantValue =
                                    variant.attribute_values?.[attributeCode];
                                if (
                                    !variantValue ||
                                    !values.includes(variantValue)
                                ) {
                                    isMatch = false;
                                    break;
                                }
                            }
                        }

                        // Eğer tüm filtrelere uyuyorsa, bu varyantı kullan
                        if (isMatch) {
                            matchingVariant = variant;
                            break;
                        }
                    }
                }

                // Eğer eşleşen varyant bulunduysa, onun stok kodunu kullan
                if (matchingVariant) {
                    stockCode =
                        matchingVariant.stock_code ||
                        matchingVariant.sku ||
                        "variant";
                    console.log("Eşleşen varyant bulundu:", matchingVariant);
                } else {
                    // Eşleşen varyant yoksa, varsayılan varyantı kullan
                    const defaultVariant = product.variants?.find(
                        (v) => v.is_default
                    );
                    if (defaultVariant) {
                        stockCode =
                            defaultVariant.stock_code ||
                            defaultVariant.sku ||
                            "default";
                    }
                    console.log(
                        "Eşleşen varyant bulunamadı, varsayılan kullanılıyor"
                    );
                }
            } else {
                // Filtreleme yoksa, varsayılan varyantın stok kodunu kullan
                const defaultVariant = product.variants?.find(
                    (v) => v.is_default
                );
                if (defaultVariant) {
                    stockCode =
                        defaultVariant.stock_code ||
                        defaultVariant.sku ||
                        "default";
                }
            }
        }

        // Filtre parametrelerini oluştur
        let filterParts = [];

        // Özellik filtreleri
        for (const [attributeCode, values] of Object.entries(
            selectedAttributes
        )) {
            if (values && values.length > 0) {
                // Türkçe karakterleri ve özel karakterleri koruyarak birleştir
                filterParts.push(`${attributeCode}:${values.join(",")}`);
            }
        }

        // Temel URL - Yeni SEO dostu format
        let url = `/products/${slug}-p-${stockCode}`;

        // Filtre parametresi
        if (filterParts.length > 0) {
            url += `?filtreler=${filterParts.join(";")}`;
        }

        return url;
    };
    // Tüm props'lar için güvenli varsayılan değerler
    const safeCategory = category || {};
    const safeProducts = products || { data: [], links: [] };
    const safeFilters = filters || {};
    const safeAttributeFilters = attributeFilters || {};
    const safeCategoryAttributes = categoryAttributes || [];
    const safeBreadcrumb = breadcrumb || [];

    // Arama terimi ve sıralama için state
    const [searchTerm, setSearchTerm] = useState(
        typeof safeFilters.search === "string" ? safeFilters.search : ""
    );

    const [sortBy, setSortBy] = useState(
        typeof safeFilters.sort === "string" ? safeFilters.sort : "newest"
    );

    // Seçilen özellikler için state
    const [selectedAttributes, setSelectedAttributes] = useState({});

    // Konsola debug bilgisi
    console.log("Filters:", safeFilters);
    console.log("Sort By:", sortBy);

    // Sepet sayısını güncellemek için fonksiyon
    const updateCartCount = () => {
        axios
            .get("/api/cart/count") // Doğrudan URL kullanarak Ziggy bağımlılığını kaldırıyoruz
            .then((response) => {
                // Sepet sayısını güncelle
                const cartCountElement = document.getElementById("cart-count");
                if (cartCountElement) {
                    cartCountElement.textContent = response.data.count;

                    // Eğer sepet boşsa gizle, doluysa göster
                    if (response.data.count > 0) {
                        cartCountElement.classList.remove("hidden");
                    } else {
                        cartCountElement.classList.add("hidden");
                    }
                }

                // Özel olay tetikle - Bu, tüm komponentlerin sepet sayısını güncellemesini sağlar
                window.dispatchEvent(
                    new CustomEvent("cart-updated", {
                        detail: { cartCount: response.data.count },
                    })
                );
            })
            .catch((error) => {
                console.error("Sepet sayısı alınamadı:", error);
            });
    };

    // Sayfa yüklendiğinde URL'den filtreleri al ve sepet sayısını güncelle
    useEffect(() => {
        // URL'den filtreleri al
        if (
            safeAttributeFilters &&
            Object.keys(safeAttributeFilters).length > 0
        ) {
            setSelectedAttributes(safeAttributeFilters);
        }

        // Sepet sayısını güncelle
        updateCartCount();
    }, [safeAttributeFilters]);

    // Özellik değiştiğinde
    const handleAttributeChange = (attributeCode, value, checked) => {
        setSelectedAttributes((prevAttributes) => {
            const newAttributes = { ...prevAttributes };

            // Eğer özellik daha önce seçilmemişse, yeni bir dizi oluştur
            if (!newAttributes[attributeCode]) {
                newAttributes[attributeCode] = [];
            }

            // Eğer işaretlendiyse, değeri ekle
            if (checked) {
                if (!newAttributes[attributeCode].includes(value)) {
                    newAttributes[attributeCode] = [
                        ...newAttributes[attributeCode],
                        value,
                    ];
                }
            } else {
                // İşaret kaldırıldıysa, değeri çıkar
                newAttributes[attributeCode] = newAttributes[
                    attributeCode
                ].filter((v) => v !== value);

                // Eğer dizi boşsa, özelliği tamamen kaldır
                if (newAttributes[attributeCode].length === 0) {
                    delete newAttributes[attributeCode];
                }
            }

            // Hata ayıklama için konsola yazdır
            console.log("Seçilen özellikler:", newAttributes);

            return newAttributes;
        });
    };

    // Arama formunu gönder
    const handleSearch = (e) => {
        e.preventDefault();

        // SEO dostu URL oluştur
        const categorySlug =
            safeCategory.slug ||
            safeCategory.name.toLowerCase().replace(/\s+/g, "-");
        const categoryId = safeCategory.id;
        const seoUrl = `/${categorySlug}-c-${categoryId}`;

        // Filtre parametrelerini oluştur
        let filterParts = [];

        // Özellik filtreleri - URL encoding yapmadan doğrudan kullan
        for (const [attributeCode, values] of Object.entries(
            selectedAttributes
        )) {
            if (values && values.length > 0) {
                // Türkçe karakterleri ve özel karakterleri koruyarak birleştir
                filterParts.push(`${attributeCode}:${values.join(",")}`);
            }
        }

        // URL'yi oluştur
        let url = seoUrl;
        let queryParams = [];

        // Arama parametresi
        if (searchTerm) {
            queryParams.push(`search=${searchTerm}`);
        }

        // Sıralama parametresi
        if (sortBy) {
            queryParams.push(`sort=${sortBy}`);
        }

        // Filtre parametresi
        if (filterParts.length > 0) {
            // Özel karakterleri encode etmeden ekle
            queryParams.push(`filtreler=${filterParts.join(";")}`);
        }

        // Query parametrelerini URL'ye ekle
        if (queryParams.length > 0) {
            url += "?" + queryParams.join("&");
        }

        // Debug için konsola yazdır
        console.log("Selected attributes:", selectedAttributes);
        console.log("Filter parts:", filterParts);
        console.log("Query params:", queryParams);
        console.log(`Yönlendirilecek URL: ${url}`);

        // Yeni URL'ye yönlendir - Doğrudan window.location'ı kullan
        window.location = url;
    };

    // Sıralama değiştiğinde
    const handleSortChange = (e) => {
        setSortBy(e.target.value);
    };

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    return (
        <MainLayout>
            <Head title={safeCategory.name || "Kategori"} />

            <div className="bg-gray-100 py-8">
                <div className="container mx-auto px-4">
                    {/* Breadcrumb */}
                    <nav className="flex mb-6 text-sm">
                        <Link
                            href="/"
                            className="text-gray-600 hover:text-blue-600"
                        >
                            Ana Sayfa
                        </Link>
                        <span className="mx-2 text-gray-500">/</span>
                        <Link
                            href={route("categories.index")}
                            className="text-gray-600 hover:text-blue-600"
                        >
                            Kategoriler
                        </Link>

                        {safeBreadcrumb.length > 0 &&
                            safeBreadcrumb.map((item, index) => (
                                <React.Fragment key={item.id}>
                                    <span className="mx-2 text-gray-500">
                                        /
                                    </span>
                                    {index === safeBreadcrumb.length - 1 ? (
                                        <span className="text-gray-800 font-medium">
                                            {item.name}
                                        </span>
                                    ) : (
                                        <Link
                                            href={`/${
                                                item.slug ||
                                                item.name
                                                    .toLowerCase()
                                                    .replace(/\s+/g, "-")
                                            }-c-${item.id}`}
                                            className="text-gray-600 hover:text-blue-600"
                                        >
                                            {item.name}
                                        </Link>
                                    )}
                                </React.Fragment>
                            ))}
                    </nav>

                    {/* Alt Kategoriler */}
                    {safeCategory.children &&
                        safeCategory.children.length > 0 && (
                            <div className="mb-8">
                                <h2 className="text-xl font-semibold mb-4">
                                    Alt Kategoriler
                                </h2>
                                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                                    {safeCategory.children.map(
                                        (subCategory) => (
                                            <Link
                                                key={subCategory.id}
                                                href={`/${
                                                    subCategory.slug ||
                                                    subCategory.name
                                                        .toLowerCase()
                                                        .replace(/\s+/g, "-")
                                                }-c-${subCategory.id}`}
                                                className="bg-white p-4 rounded-lg shadow hover:shadow-md transition flex items-center justify-center text-center"
                                            >
                                                <span>{subCategory.name}</span>
                                            </Link>
                                        )
                                    )}
                                </div>
                            </div>
                        )}

                    {/* Kategori Başlık */}
                    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                        <h1 className="text-2xl font-bold">
                            {safeCategory.name || "Kategori"}
                        </h1>
                        {safeCategory.description && (
                            <p className="text-gray-600 mt-2">
                                {safeCategory.description}
                            </p>
                        )}
                    </div>

                    <div className="flex flex-col md:flex-row gap-6">
                        {/* Filtreler */}
                        <div className="md:w-1/4">
                            <div className="bg-white rounded-lg shadow-md p-4 mb-4">
                                <h2 className="text-lg font-semibold mb-4">
                                    Filtreler
                                </h2>

                                <form onSubmit={handleSearch}>
                                    <div className="mb-4">
                                        <label
                                            htmlFor="search"
                                            className="block text-sm font-medium text-gray-700 mb-1"
                                        >
                                            Arama
                                        </label>
                                        <input
                                            type="text"
                                            id="search"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                            placeholder="Ürün ara..."
                                            value={searchTerm}
                                            onChange={(e) =>
                                                setSearchTerm(e.target.value)
                                            }
                                        />
                                    </div>

                                    <div className="mb-4">
                                        <label
                                            htmlFor="sort"
                                            className="block text-sm font-medium text-gray-700 mb-1"
                                        >
                                            Sıralama
                                        </label>
                                        <select
                                            id="sort"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                            value={sortBy}
                                            onChange={handleSortChange}
                                        >
                                            <option value="newest">
                                                En Yeniler
                                            </option>
                                            <option value="price_asc">
                                                Fiyat (Düşükten Yükseğe)
                                            </option>
                                            <option value="price_desc">
                                                Fiyat (Yüksekten Düşüğe)
                                            </option>
                                            <option value="name_asc">
                                                İsim (A-Z)
                                            </option>
                                            <option value="name_desc">
                                                İsim (Z-A)
                                            </option>
                                        </select>
                                    </div>

                                    {/* Kategori Özellikleri Filtreleri */}
                                    {safeCategoryAttributes &&
                                        safeCategoryAttributes.length > 0 && (
                                            <div className="mb-6">
                                                <h3 className="text-md font-medium mb-3">
                                                    Özellik Filtreleri
                                                </h3>

                                                {safeCategoryAttributes.map(
                                                    (attribute) => (
                                                        <div
                                                            key={attribute.id}
                                                            className="mb-4"
                                                        >
                                                            <h4 className="text-sm font-medium mb-2">
                                                                {attribute.name}
                                                            </h4>
                                                            <div className="space-y-2">
                                                                {attribute.values &&
                                                                    attribute.values.map(
                                                                        (
                                                                            value
                                                                        ) => (
                                                                            <div
                                                                                key={
                                                                                    value.id
                                                                                }
                                                                                className="flex items-center"
                                                                            >
                                                                                <input
                                                                                    type="checkbox"
                                                                                    id={`attr-${attribute.code}-${value.value}`}
                                                                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                                                    checked={
                                                                                        selectedAttributes[
                                                                                            attribute
                                                                                                .code
                                                                                        ]?.includes(
                                                                                            value.value
                                                                                        ) ||
                                                                                        false
                                                                                    }
                                                                                    onChange={(
                                                                                        e
                                                                                    ) =>
                                                                                        handleAttributeChange(
                                                                                            attribute.code,
                                                                                            value.value,
                                                                                            e
                                                                                                .target
                                                                                                .checked
                                                                                        )
                                                                                    }
                                                                                />
                                                                                <label
                                                                                    htmlFor={`attr-${attribute.code}-${value.value}`}
                                                                                    className="ml-2 block text-sm text-gray-700"
                                                                                >
                                                                                    {value.label ||
                                                                                        value.value}
                                                                                </label>
                                                                            </div>
                                                                        )
                                                                    )}
                                                            </div>
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        )}

                                    <button
                                        type="submit"
                                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
                                    >
                                        Filtrele
                                    </button>
                                </form>
                            </div>
                        </div>

                        {/* Ürün Listesi */}
                        <div className="md:w-3/4">
                            {safeProducts.data.length === 0 ? (
                                <div className="bg-white rounded-lg shadow-md p-6 text-center">
                                    <p className="text-gray-500 mb-4">
                                        Bu kategoride ürün bulunamadı.
                                    </p>
                                    <Link
                                        href="/products"
                                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                                    >
                                        Tüm Ürünleri Gör
                                    </Link>
                                </div>
                            ) : (
                                <>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                                        {safeProducts.data.map((product) => (
                                            <ProductCard
                                                key={product.id}
                                                product={product}
                                                attributeFilters={
                                                    selectedAttributes
                                                }
                                                forceFilteredUrl={true}
                                            />
                                        ))}
                                    </div>

                                    <Pagination links={safeProducts.links} />
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
