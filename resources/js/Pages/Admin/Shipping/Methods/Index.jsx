import React from "react";
import { Head, <PERSON> } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import { PlusIcon } from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";
import { axiosDelete } from "@/Utils/inertiaHelper";

export default function ShippingMethodsIndex({ methods }) {
    return (
        <AdminLayout>
            <Head title="Kargo Metodları" />

            <div className="container mx-auto py-6">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-semibold text-gray-900">
                        Kargo Metodları
                    </h1>
                    <Link
                        href={route("admin.shipping.methods.create")}
                        className="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        <PlusIcon className="w-5 h-5 mr-2" />
                        Yeni <PERSON>od Ekle
                    </Link>
                </div>

                <div className="bg-white shadow-md rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    ID
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Metod Adı
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Kod
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Açıklama
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Kargo Şirketi
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Bölge Sayısı
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Durum
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    İşlemler
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {methods && methods.length > 0 ? (
                                methods.map((method) => (
                                    <tr key={method.id}>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {method.id}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {method.name}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {method.code}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {method.description || "-"}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {method.company
                                                ? method.company.name
                                                : "-"}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {method.zone_methods_count || 0}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <span
                                                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                    method.is_active
                                                        ? "bg-green-100 text-green-800"
                                                        : "bg-red-100 text-red-800"
                                                }`}
                                            >
                                                {method.is_active
                                                    ? "Aktif"
                                                    : "Pasif"}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end space-x-2">
                                                <Link
                                                    href={route(
                                                        "admin.shipping.methods.edit",
                                                        method.id
                                                    )}
                                                    className="text-yellow-600 hover:text-yellow-900"
                                                >
                                                    Düzenle
                                                </Link>
                                                <button
                                                    onClick={() => {
                                                        if (
                                                            confirm(
                                                                "Bu kargo metodunu silmek istediğinize emin misiniz?"
                                                            )
                                                        ) {
                                                            // Delete method
                                                            const url = route(
                                                                "admin.shipping.methods.destroy",
                                                                method.id
                                                            );

                                                            // axiosDelete yardımcı fonksiyonu ile silme işlemi yap
                                                            axiosDelete(url)
                                                                .then(
                                                                    (
                                                                        response
                                                                    ) => {
                                                                        if (
                                                                            response.data &&
                                                                            response
                                                                                .data
                                                                                .success
                                                                        ) {
                                                                            toast.success(
                                                                                response
                                                                                    .data
                                                                                    .message ||
                                                                                    "Kargo metodu silindi"
                                                                            );
                                                                            window.location.reload();
                                                                        } else {
                                                                            toast.error(
                                                                                "Kargo metodu silinirken bir hata oluştu"
                                                                            );
                                                                        }
                                                                    }
                                                                )
                                                                .catch(
                                                                    (error) => {
                                                                        console.error(
                                                                            error
                                                                        );
                                                                        let errorMessage =
                                                                            "Kargo metodu silinirken bir hata oluştu";

                                                                        if (
                                                                            error.response &&
                                                                            error
                                                                                .response
                                                                                .data &&
                                                                            error
                                                                                .response
                                                                                .data
                                                                                .message
                                                                        ) {
                                                                            errorMessage =
                                                                                error
                                                                                    .response
                                                                                    .data
                                                                                    .message;
                                                                        }

                                                                        toast.error(
                                                                            errorMessage
                                                                        );
                                                                    }
                                                                );
                                                        }
                                                    }}
                                                    className="text-red-600 hover:text-red-900"
                                                >
                                                    Sil
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td
                                        colSpan="7"
                                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center"
                                    >
                                        Henüz kargo metodu bulunmuyor.
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </AdminLayout>
    );
}
