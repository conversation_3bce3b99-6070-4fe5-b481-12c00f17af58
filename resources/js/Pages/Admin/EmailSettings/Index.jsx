import React, { useState } from "react";
import { Head, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import InputLabel from "@/Components/InputLabel";
import TextInput from "@/Components/TextInput";
import InputError from "@/Components/InputError";
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryButton from "@/Components/SecondaryButton";
import Checkbox from "@/Components/Checkbox";

export default function Index({ settings }) {
    const [showTestEmailForm, setShowTestEmailForm] = useState(false);

    const { data, setData, put, processing, errors } = useForm({
        mail_driver: settings.mail_driver || "smtp",
        mail_host: settings.mail_host || "",
        mail_port: settings.mail_port || "",
        mail_username: settings.mail_username || "",
        mail_password: settings.mail_password ? "********" : "",
        mail_encryption: settings.mail_encryption || "",
        mail_from_address: settings.mail_from_address || "",
        mail_from_name: settings.mail_from_name || "",
        queue_emails: settings.queue_emails || false,
        emails_per_batch: settings.emails_per_batch || 50,
        track_emails: settings.track_emails || false,
        notification_settings: settings.notification_settings || {
            welcome: true,
            order_confirmation: true,
            order_status: true,
            password_reset: true,
        },
        // Mailchimp ayarları
        mailchimp_api_key: settings.mailchimp_api_key || "",
        mailchimp_list_id: settings.mailchimp_list_id || "",
        mailchimp_enabled: settings.mailchimp_enabled || false,
        // Mailtrap ayarları
        mailtrap_enabled: settings.mailtrap_enabled || false,
        mailtrap_inbox_id: settings.mailtrap_inbox_id || "",
        mailtrap_api_token: settings.mailtrap_api_token || "",
    });

    const {
        data: testData,
        setData: setTestData,
        post: postTest,
        processing: testProcessing,
        errors: testErrors,
        reset: resetTest,
    } = useForm({
        email: "",
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route("admin.email-settings.update"));
    };

    const handleTestSubmit = (e) => {
        e.preventDefault();
        postTest(route("admin.email-settings.send-test"), {
            onSuccess: () => {
                setShowTestEmailForm(false);
                resetTest();
            },
        });
    };

    const handleNotificationSettingChange = (key, value) => {
        setData("notification_settings", {
            ...data.notification_settings,
            [key]: value,
        });
    };

    return (
        <AdminLayout title="E-posta Ayarları">
            <Head title="E-posta Ayarları" />

            <div className="bg-white shadow-md rounded-lg overflow-hidden">
                <div className="p-6">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-xl font-semibold text-gray-800">
                            E-posta Ayarları
                        </h2>
                        <SecondaryButton
                            onClick={() =>
                                setShowTestEmailForm(!showTestEmailForm)
                            }
                        >
                            Test E-postası Gönder
                        </SecondaryButton>
                    </div>

                    {showTestEmailForm && (
                        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                            <h3 className="text-lg font-medium mb-2">
                                Test E-postası Gönder
                            </h3>
                            <form
                                onSubmit={handleTestSubmit}
                                className="flex items-end"
                            >
                                <div className="flex-1 mr-2">
                                    <InputLabel
                                        htmlFor="test_email"
                                        value="E-posta Adresi"
                                    />
                                    <TextInput
                                        id="test_email"
                                        type="email"
                                        name="email"
                                        value={testData.email}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setTestData("email", e.target.value)
                                        }
                                        required
                                    />
                                    <InputError
                                        message={testErrors.email}
                                        className="mt-2"
                                    />
                                </div>
                                <PrimaryButton
                                    className="ml-4"
                                    disabled={testProcessing}
                                >
                                    {testProcessing
                                        ? "Gönderiliyor..."
                                        : "Gönder"}
                                </PrimaryButton>
                            </form>
                        </div>
                    )}

                    <form onSubmit={handleSubmit}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 className="text-lg font-medium mb-4">
                                    SMTP Ayarları
                                </h3>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="mail_driver"
                                        value="Mail Sürücüsü"
                                    />
                                    <select
                                        id="mail_driver"
                                        name="mail_driver"
                                        value={data.mail_driver}
                                        className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                        onChange={(e) =>
                                            setData(
                                                "mail_driver",
                                                e.target.value
                                            )
                                        }
                                    >
                                        <option value="smtp">SMTP</option>
                                        <option value="sendmail">
                                            Sendmail
                                        </option>
                                        <option value="mailgun">Mailgun</option>
                                        <option value="ses">Amazon SES</option>
                                        <option value="postmark">
                                            Postmark
                                        </option>
                                        <option value="log">Log</option>
                                        <option value="array">
                                            Array (Test)
                                        </option>
                                    </select>
                                    <InputError
                                        message={errors.mail_driver}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="mail_host"
                                        value="Mail Sunucusu"
                                    />
                                    <TextInput
                                        id="mail_host"
                                        type="text"
                                        name="mail_host"
                                        value={data.mail_host}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData("mail_host", e.target.value)
                                        }
                                    />
                                    <InputError
                                        message={errors.mail_host}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="mail_port"
                                        value="Port"
                                    />
                                    <TextInput
                                        id="mail_port"
                                        type="number"
                                        name="mail_port"
                                        value={data.mail_port}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData("mail_port", e.target.value)
                                        }
                                    />
                                    <InputError
                                        message={errors.mail_port}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="mail_username"
                                        value="Kullanıcı Adı"
                                    />
                                    <TextInput
                                        id="mail_username"
                                        type="text"
                                        name="mail_username"
                                        value={data.mail_username}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "mail_username",
                                                e.target.value
                                            )
                                        }
                                    />
                                    <InputError
                                        message={errors.mail_username}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="mail_password"
                                        value="Şifre"
                                    />
                                    <TextInput
                                        id="mail_password"
                                        type="password"
                                        name="mail_password"
                                        value={data.mail_password}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "mail_password",
                                                e.target.value
                                            )
                                        }
                                    />
                                    <p className="text-xs text-gray-500 mt-1">
                                        Değiştirmek istemiyorsanız boş bırakın.
                                    </p>
                                    <InputError
                                        message={errors.mail_password}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="mail_encryption"
                                        value="Şifreleme"
                                    />
                                    <select
                                        id="mail_encryption"
                                        name="mail_encryption"
                                        value={data.mail_encryption}
                                        className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                        onChange={(e) =>
                                            setData(
                                                "mail_encryption",
                                                e.target.value
                                            )
                                        }
                                    >
                                        <option value="">Yok</option>
                                        <option value="tls">TLS</option>
                                        <option value="ssl">SSL</option>
                                    </select>
                                    <InputError
                                        message={errors.mail_encryption}
                                        className="mt-2"
                                    />
                                </div>
                            </div>

                            <div>
                                <h3 className="text-lg font-medium mb-4">
                                    Gönderici Bilgileri
                                </h3>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="mail_from_address"
                                        value="Gönderen E-posta"
                                    />
                                    <TextInput
                                        id="mail_from_address"
                                        type="email"
                                        name="mail_from_address"
                                        value={data.mail_from_address}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "mail_from_address",
                                                e.target.value
                                            )
                                        }
                                    />
                                    <InputError
                                        message={errors.mail_from_address}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="mail_from_name"
                                        value="Gönderen Adı"
                                    />
                                    <TextInput
                                        id="mail_from_name"
                                        type="text"
                                        name="mail_from_name"
                                        value={data.mail_from_name}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "mail_from_name",
                                                e.target.value
                                            )
                                        }
                                    />
                                    <InputError
                                        message={errors.mail_from_name}
                                        className="mt-2"
                                    />
                                </div>

                                <h3 className="text-lg font-medium mb-4 mt-8">
                                    Gelişmiş Ayarlar
                                </h3>

                                <div className="mb-4">
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="queue_emails"
                                            name="queue_emails"
                                            checked={data.queue_emails}
                                            onChange={(e) =>
                                                setData(
                                                    "queue_emails",
                                                    e.target.checked
                                                )
                                            }
                                        />
                                        <InputLabel
                                            htmlFor="queue_emails"
                                            value="E-postaları kuyruğa al"
                                            className="ml-2"
                                        />
                                    </div>
                                    <p className="text-xs text-gray-500 mt-1">
                                        E-postalar arka planda gönderilir, bu
                                        sayede sayfa yüklenme süresi kısalır.
                                    </p>
                                    <InputError
                                        message={errors.queue_emails}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="emails_per_batch"
                                        value="Toplu E-posta Sayısı"
                                    />
                                    <TextInput
                                        id="emails_per_batch"
                                        type="number"
                                        name="emails_per_batch"
                                        value={data.emails_per_batch}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "emails_per_batch",
                                                e.target.value
                                            )
                                        }
                                    />
                                    <p className="text-xs text-gray-500 mt-1">
                                        Bir seferde gönderilecek maksimum
                                        e-posta sayısı.
                                    </p>
                                    <InputError
                                        message={errors.emails_per_batch}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="track_emails"
                                            name="track_emails"
                                            checked={data.track_emails}
                                            onChange={(e) =>
                                                setData(
                                                    "track_emails",
                                                    e.target.checked
                                                )
                                            }
                                        />
                                        <InputLabel
                                            htmlFor="track_emails"
                                            value="E-postaları takip et"
                                            className="ml-2"
                                        />
                                    </div>
                                    <p className="text-xs text-gray-500 mt-1">
                                        E-postaların gönderilip gönderilmediğini
                                        takip et.
                                    </p>
                                    <InputError
                                        message={errors.track_emails}
                                        className="mt-2"
                                    />
                                </div>
                            </div>
                        </div>

                        <div className="mt-8">
                            <h3 className="text-lg font-medium mb-4">
                                Mailchimp Entegrasyonu
                            </h3>
                            <div className="bg-gray-50 p-4 rounded-lg mb-6">
                                <div className="mb-4">
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="mailchimp_enabled"
                                            name="mailchimp_enabled"
                                            checked={data.mailchimp_enabled}
                                            onChange={(e) =>
                                                setData(
                                                    "mailchimp_enabled",
                                                    e.target.checked
                                                )
                                            }
                                        />
                                        <InputLabel
                                            htmlFor="mailchimp_enabled"
                                            value="Mailchimp Entegrasyonunu Etkinleştir"
                                            className="ml-2"
                                        />
                                    </div>
                                    <p className="text-xs text-gray-500 mt-1">
                                        Mailchimp ile e-posta pazarlama
                                        kampanyaları oluşturun ve
                                        müşterilerinizle iletişimde kalın.
                                    </p>
                                </div>

                                {data.mailchimp_enabled && (
                                    <>
                                        <div className="mb-4">
                                            <InputLabel
                                                htmlFor="mailchimp_api_key"
                                                value="Mailchimp API Anahtarı"
                                            />
                                            <TextInput
                                                id="mailchimp_api_key"
                                                type="text"
                                                name="mailchimp_api_key"
                                                value={data.mailchimp_api_key}
                                                className="mt-1 block w-full"
                                                onChange={(e) =>
                                                    setData(
                                                        "mailchimp_api_key",
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            <p className="text-xs text-gray-500 mt-1">
                                                Mailchimp hesabınızdan API
                                                anahtarınızı alabilirsiniz.
                                            </p>
                                            <InputError
                                                message={
                                                    errors.mailchimp_api_key
                                                }
                                                className="mt-2"
                                            />
                                        </div>

                                        <div className="mb-4">
                                            <InputLabel
                                                htmlFor="mailchimp_list_id"
                                                value="Mailchimp Liste ID"
                                            />
                                            <TextInput
                                                id="mailchimp_list_id"
                                                type="text"
                                                name="mailchimp_list_id"
                                                value={data.mailchimp_list_id}
                                                className="mt-1 block w-full"
                                                onChange={(e) =>
                                                    setData(
                                                        "mailchimp_list_id",
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            <p className="text-xs text-gray-500 mt-1">
                                                Mailchimp'teki hedef kitlenizin
                                                (audience) ID'si.
                                            </p>
                                            <InputError
                                                message={
                                                    errors.mailchimp_list_id
                                                }
                                                className="mt-2"
                                            />
                                        </div>

                                        <div className="mb-4">
                                            <form
                                                onSubmit={(e) => {
                                                    e.preventDefault();
                                                    postTest(
                                                        route(
                                                            "admin.email-settings.test-mailchimp"
                                                        ),
                                                        {
                                                            data: {
                                                                email:
                                                                    testData.email ||
                                                                    "<EMAIL>",
                                                            },
                                                        }
                                                    );
                                                }}
                                            >
                                                <PrimaryButton
                                                    type="submit"
                                                    className="mt-2"
                                                    disabled={testProcessing}
                                                >
                                                    {testProcessing
                                                        ? "Test Ediliyor..."
                                                        : "Mailchimp Bağlantısını Test Et"}
                                                </PrimaryButton>
                                                <p className="text-xs text-gray-500 mt-1">
                                                    Bu test, Mailchimp listesine
                                                    bir test abonesi
                                                    ekleyecektir.
                                                </p>
                                            </form>
                                        </div>
                                    </>
                                )}
                            </div>

                            <h3 className="text-lg font-medium mb-4">
                                Mailtrap Entegrasyonu
                            </h3>
                            <div className="bg-gray-50 p-4 rounded-lg mb-6">
                                <div className="mb-4">
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="mailtrap_enabled"
                                            name="mailtrap_enabled"
                                            checked={data.mailtrap_enabled}
                                            onChange={(e) =>
                                                setData(
                                                    "mailtrap_enabled",
                                                    e.target.checked
                                                )
                                            }
                                        />
                                        <InputLabel
                                            htmlFor="mailtrap_enabled"
                                            value="Mailtrap Entegrasyonunu Etkinleştir"
                                            className="ml-2"
                                        />
                                    </div>
                                    <p className="text-xs text-gray-500 mt-1">
                                        Mailtrap ile e-postaları test edin ve
                                        gerçek alıcılara göndermeden önce
                                        kontrol edin.
                                    </p>
                                </div>

                                {data.mailtrap_enabled && (
                                    <>
                                        <div className="mb-4">
                                            <InputLabel
                                                htmlFor="mailtrap_inbox_id"
                                                value="Mailtrap Inbox ID"
                                            />
                                            <TextInput
                                                id="mailtrap_inbox_id"
                                                type="text"
                                                name="mailtrap_inbox_id"
                                                value={data.mailtrap_inbox_id}
                                                className="mt-1 block w-full"
                                                onChange={(e) =>
                                                    setData(
                                                        "mailtrap_inbox_id",
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            <InputError
                                                message={
                                                    errors.mailtrap_inbox_id
                                                }
                                                className="mt-2"
                                            />
                                        </div>

                                        <div className="mb-4">
                                            <InputLabel
                                                htmlFor="mailtrap_api_token"
                                                value="Mailtrap API Token"
                                            />
                                            <TextInput
                                                id="mailtrap_api_token"
                                                type="text"
                                                name="mailtrap_api_token"
                                                value={data.mailtrap_api_token}
                                                className="mt-1 block w-full"
                                                onChange={(e) =>
                                                    setData(
                                                        "mailtrap_api_token",
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            <InputError
                                                message={
                                                    errors.mailtrap_api_token
                                                }
                                                className="mt-2"
                                            />
                                        </div>

                                        <div className="mb-4">
                                            <form
                                                onSubmit={(e) => {
                                                    e.preventDefault();
                                                    postTest(
                                                        route(
                                                            "admin.email-settings.test-mailtrap"
                                                        )
                                                    );
                                                }}
                                            >
                                                <PrimaryButton
                                                    type="submit"
                                                    className="mt-2"
                                                    disabled={testProcessing}
                                                >
                                                    {testProcessing
                                                        ? "Test Ediliyor..."
                                                        : "Mailtrap Bağlantısını Test Et"}
                                                </PrimaryButton>
                                                <p className="text-xs text-gray-500 mt-1">
                                                    Bu test, Mailtrap
                                                    bağlantınızı kontrol edecek
                                                    ve inbox bilgilerini
                                                    alacaktır.
                                                </p>
                                            </form>
                                        </div>
                                    </>
                                )}
                            </div>

                            <h3 className="text-lg font-medium mb-4">
                                Bildirim Ayarları
                            </h3>
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="notification_welcome"
                                            name="notification_welcome"
                                            checked={
                                                data.notification_settings
                                                    .welcome
                                            }
                                            onChange={(e) =>
                                                handleNotificationSettingChange(
                                                    "welcome",
                                                    e.target.checked
                                                )
                                            }
                                        />
                                        <InputLabel
                                            htmlFor="notification_welcome"
                                            value="Hoş Geldiniz E-postası"
                                            className="ml-2"
                                        />
                                    </div>
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="notification_order_confirmation"
                                            name="notification_order_confirmation"
                                            checked={
                                                data.notification_settings
                                                    .order_confirmation
                                            }
                                            onChange={(e) =>
                                                handleNotificationSettingChange(
                                                    "order_confirmation",
                                                    e.target.checked
                                                )
                                            }
                                        />
                                        <InputLabel
                                            htmlFor="notification_order_confirmation"
                                            value="Sipariş Onayı E-postası"
                                            className="ml-2"
                                        />
                                    </div>
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="notification_order_status"
                                            name="notification_order_status"
                                            checked={
                                                data.notification_settings
                                                    .order_status
                                            }
                                            onChange={(e) =>
                                                handleNotificationSettingChange(
                                                    "order_status",
                                                    e.target.checked
                                                )
                                            }
                                        />
                                        <InputLabel
                                            htmlFor="notification_order_status"
                                            value="Sipariş Durumu E-postası"
                                            className="ml-2"
                                        />
                                    </div>
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="notification_password_reset"
                                            name="notification_password_reset"
                                            checked={
                                                data.notification_settings
                                                    .password_reset
                                            }
                                            onChange={(e) =>
                                                handleNotificationSettingChange(
                                                    "password_reset",
                                                    e.target.checked
                                                )
                                            }
                                        />
                                        <InputLabel
                                            htmlFor="notification_password_reset"
                                            value="Şifre Sıfırlama E-postası"
                                            className="ml-2"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center justify-end mt-8">
                            <PrimaryButton
                                className="ml-4"
                                disabled={processing}
                            >
                                {processing ? "Kaydediliyor..." : "Kaydet"}
                            </PrimaryButton>
                        </div>
                    </form>
                </div>
            </div>
        </AdminLayout>
    );
}
