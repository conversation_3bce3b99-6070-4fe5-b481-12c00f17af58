import React from "react";
import { Head } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";

export default function Dashboard({
    auth,
    stats,
    latestProducts,
    latestOrders,
}) {
    return (
        <AdminLayout title="Yönetim Paneli">
            <Head title="Yönetim Paneli" />

            <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div className="p-6 bg-white border-b border-gray-200">
                    <h3 className="text-lg font-medium mb-4">
                        Ho<PERSON> Geldiniz, {auth.user.name}!
                    </h3>

                    {/* İstatistikler */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div className="bg-blue-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-blue-700 mb-2">
                                <PERSON><PERSON><PERSON><PERSON><PERSON>
                            </h4>
                            <p className="text-3xl font-bold text-gray-800">
                                {stats.productCount}
                            </p>
                            <a
                                href="/admin/products"
                                className="mt-3 inline-block text-sm text-blue-600 hover:underline"
                            >
                                Tüm ürünleri görüntüle &rarr;
                            </a>
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-green-700 mb-2">
                                Kategoriler
                            </h4>
                            <p className="text-3xl font-bold text-gray-800">
                                {stats.categoryCount}
                            </p>
                            <a
                                href="/admin/categories"
                                className="mt-3 inline-block text-sm text-green-600 hover:underline"
                            >
                                Tüm kategorileri görüntüle &rarr;
                            </a>
                        </div>

                        <div className="bg-purple-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-purple-700 mb-2">
                                Kullanıcılar
                            </h4>
                            <p className="text-3xl font-bold text-gray-800">
                                {stats.userCount}
                            </p>
                            <a
                                href="/admin/users"
                                className="mt-3 inline-block text-sm text-purple-600 hover:underline"
                            >
                                Tüm kullanıcıları görüntüle &rarr;
                            </a>
                        </div>

                        <div className="bg-amber-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-amber-700 mb-2">
                                Siparişler
                            </h4>
                            <p className="text-3xl font-bold text-gray-800">
                                {stats.orderCount}
                            </p>
                            <a
                                href="/admin/orders"
                                className="mt-3 inline-block text-sm text-amber-600 hover:underline"
                            >
                                Tüm siparişleri görüntüle &rarr;
                            </a>
                        </div>
                    </div>

                    {/* Hızlı Erişim */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div className="bg-blue-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-blue-700 mb-2">
                                Yeni Ürün Ekle
                            </h4>
                            <p className="text-gray-600">
                                Sisteme yeni ürün eklemek için tıklayın.
                            </p>
                            <a
                                href="/admin/products"
                                className="mt-3 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                            >
                                Ürün Ekle
                            </a>
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-green-700 mb-2">
                                Yeni Kategori Ekle
                            </h4>
                            <p className="text-gray-600">
                                Sisteme yeni kategori eklemek için tıklayın.
                            </p>
                            <a
                                href="/admin/categories"
                                className="mt-3 inline-block px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                            >
                                Kategori Ekle
                            </a>
                        </div>

                        <div className="bg-amber-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-amber-700 mb-2">
                                Modül Yönetimi
                            </h4>
                            <p className="text-gray-600">
                                Sistem modüllerini yönetin ve özellikleri
                                etkinleştirin.
                            </p>
                            <a
                                href="/admin/modules"
                                className="mt-3 inline-block px-4 py-2 bg-amber-500 text-white rounded hover:bg-amber-600"
                            >
                                Modülleri Yönet
                            </a>
                        </div>

                        <div className="bg-purple-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-purple-700 mb-2">
                                Kullanıcı Profili
                            </h4>
                            <p className="text-gray-600">
                                Profil bilgilerinizi güncelleyin.
                            </p>
                            <a
                                href="/profile"
                                className="mt-3 inline-block px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
                            >
                                Profile Git
                            </a>
                        </div>
                    </div>

                    {/* Son Eklenen Ürünler */}
                    <div className="mt-8">
                        <h3 className="text-lg font-medium mb-4">
                            Son Eklenen Ürünler
                        </h3>
                        <div className="bg-white overflow-hidden shadow-md rounded-lg">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Ürün Adı
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Kategori
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Fiyat
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Stok
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Durum
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {latestProducts.map((product) => (
                                        <tr key={product.id}>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {product.name}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-500">
                                                    {product.category?.name ||
                                                        "Kategori Yok"}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    {product.price} TL
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    {product.stock}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span
                                                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                        product.status
                                                            ? "bg-green-100 text-green-800"
                                                            : "bg-red-100 text-red-800"
                                                    }`}
                                                >
                                                    {product.status
                                                        ? "Aktif"
                                                        : "Pasif"}
                                                </span>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    {/* Son Siparişler */}
                    <div className="mt-8">
                        <h3 className="text-lg font-medium mb-4">
                            Son Siparişler
                        </h3>
                        <div className="bg-white overflow-hidden shadow-md rounded-lg">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Sipariş No
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Müşteri
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Tutar
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Durum
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        >
                                            Tarih
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {latestOrders && latestOrders.length > 0 ? (
                                        latestOrders.map((order) => (
                                            <tr key={order.id}>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {order.order_number}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">
                                                        {order.billing_name}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">
                                                        {order.total_amount} TL
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span
                                                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                            order.status ===
                                                            "completed"
                                                                ? "bg-green-100 text-green-800"
                                                                : order.status ===
                                                                  "processing"
                                                                ? "bg-blue-100 text-blue-800"
                                                                : order.status ===
                                                                  "cancelled"
                                                                ? "bg-red-100 text-red-800"
                                                                : "bg-yellow-100 text-yellow-800"
                                                        }`}
                                                    >
                                                        {order.status ===
                                                        "pending"
                                                            ? "Beklemede"
                                                            : order.status ===
                                                              "processing"
                                                            ? "İşlemde"
                                                            : order.status ===
                                                              "completed"
                                                            ? "Tamamlandı"
                                                            : order.status ===
                                                              "cancelled"
                                                            ? "İptal Edildi"
                                                            : order.status}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-500">
                                                        {new Date(
                                                            order.created_at
                                                        ).toLocaleDateString(
                                                            "tr-TR"
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))
                                    ) : (
                                        <tr>
                                            <td
                                                colSpan="5"
                                                className="px-6 py-4 text-center text-gray-500"
                                            >
                                                Henüz sipariş bulunmuyor
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    {/* Kullanıcı Bilgileri */}
                    <div className="mt-8">
                        <h3 className="text-lg font-medium mb-4">
                            Kullanıcı Bilgileri
                        </h3>
                        <div className="bg-gray-50 p-4 rounded-lg shadow">
                            <div className="mb-2">
                                <span className="font-semibold">Ad Soyad:</span>{" "}
                                {auth.user.name}
                            </div>
                            <div className="mb-2">
                                <span className="font-semibold">E-posta:</span>{" "}
                                {auth.user.email}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
