import React, { useState } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";

export default function Attributes({
    category,
    attributes,
    categoryAttributeIds,
    attributeTypes,
}) {
    // Hata ayıklama için kategori özelliklerini konsola yazdır
    console.log("Kategori özellikleri:", category.attributes);

    const { data, setData, put, processing, errors } = useForm({
        attributes: attributes
            .filter((attr) => categoryAttributeIds.includes(attr.id))
            .map((attr) => {
                // Kategori özellikleri içinde eşleşen özelliği bul
                const categoryAttr = category.attributes.find(
                    (ca) => ca.id === attr.id
                );

                return {
                    id: attr.id,
                    is_required: categoryAttr
                        ? categoryAttr.pivot.is_required
                        : false,
                    is_filterable: categoryAttr
                        ? categoryAttr.pivot.is_filterable
                        : false,
                    position: categoryAttr ? categoryAttr.pivot.position : 0,
                };
            }),
    });

    const [selectedAttributes, setSelectedAttributes] =
        useState(categoryAttributeIds);

    const handleAttributeToggle = (attributeId) => {
        const newSelectedAttributes = [...selectedAttributes];
        const index = newSelectedAttributes.indexOf(attributeId);

        if (index === -1) {
            // Özellik seçilmemiş, ekle
            newSelectedAttributes.push(attributeId);

            // Form verilerine de ekle
            const newAttributes = [...data.attributes];
            newAttributes.push({
                id: attributeId,
                is_required: false,
                is_filterable: false,
                position: 0,
            });

            setData("attributes", newAttributes);
        } else {
            // Özellik zaten seçili, kaldır
            newSelectedAttributes.splice(index, 1);

            // Form verilerinden de kaldır
            const newAttributes = data.attributes.filter(
                (attr) => attr.id !== attributeId
            );
            setData("attributes", newAttributes);
        }

        setSelectedAttributes(newSelectedAttributes);
    };

    const handleAttributeChange = (attributeId, field, value) => {
        const newAttributes = [...data.attributes];
        const index = newAttributes.findIndex(
            (attr) => attr.id === attributeId
        );

        if (index !== -1) {
            newAttributes[index][field] = value;
            setData("attributes", newAttributes);
        }
    };

    const handleSubmit = (e) => {
        e.preventDefault();

        // Form verilerini konsola yazdır (hata ayıklama için)
        console.log("Gönderilecek veriler:", data);

        put(route("admin.categories.attributes.update", category.id), data, {
            preserveScroll: true,
            preserveState: true,
            replace: true,
            onBefore: () => {
                console.log("Form gönderilmeden önce");
            },
            onStart: () => {
                console.log("Form gönderimi başladı");
            },
            onProgress: (progress) => {
                console.log("Form gönderimi ilerliyor:", progress);
            },
            onSuccess: (page) => {
                console.log("Form gönderimi başarılı", page);
                if (page.props.success) {
                    alert(page.props.success);
                } else {
                    alert("Kategori özellikleri başarıyla güncellendi.");
                }
            },
            onError: (errors) => {
                console.error("Form gönderiminde hata:", errors);
                alert("Kategori özellikleri güncellenirken bir hata oluştu.");
            },
            onFinish: () => {
                console.log("Form gönderimi tamamlandı");
            },
        });
    };

    return (
        <AdminLayout title={`Kategori Özellikleri: ${category.name}`}>
            <Head title={`Kategori Özellikleri: ${category.name}`} />

            <div className="mb-6">
                <Link
                    href={route("admin.categories.index")}
                    className="text-blue-600 hover:text-blue-800"
                >
                    &larr; Kategorilere Dön
                </Link>
            </div>

            <div className="bg-white shadow-md rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">
                    {category.name} - Özellik Yönetimi
                </h2>

                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                    <p className="text-gray-700">
                        Bu sayfada kategoriye özel özellikleri yönetebilirsiniz.
                        Seçilen özellikler, bu kategorideki ürünlerde
                        kullanılabilir olacaktır.
                    </p>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="mb-6">
                        <h3 className="text-lg font-medium mb-2">
                            Mevcut Özellikler
                        </h3>

                        <div className="grid grid-cols-1 gap-4 mb-4">
                            {attributes.length === 0 ? (
                                <p className="text-gray-500">
                                    Henüz özellik bulunmuyor.
                                </p>
                            ) : (
                                attributes.map((attribute) => (
                                    <div
                                        key={attribute.id}
                                        className={`border rounded-lg p-4 ${
                                            selectedAttributes.includes(
                                                attribute.id
                                            )
                                                ? "border-blue-500 bg-blue-50"
                                                : "border-gray-200"
                                        }`}
                                    >
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-center">
                                                <input
                                                    type="checkbox"
                                                    id={`attribute-${attribute.id}`}
                                                    checked={selectedAttributes.includes(
                                                        attribute.id
                                                    )}
                                                    onChange={() =>
                                                        handleAttributeToggle(
                                                            attribute.id
                                                        )
                                                    }
                                                    className="h-5 w-5 text-blue-600 rounded"
                                                />
                                                <label
                                                    htmlFor={`attribute-${attribute.id}`}
                                                    className="ml-2 text-lg font-medium"
                                                >
                                                    {attribute.name}
                                                </label>
                                            </div>
                                            <div className="text-sm text-gray-500">
                                                {attributeTypes[attribute.type]}
                                            </div>
                                        </div>

                                        {selectedAttributes.includes(
                                            attribute.id
                                        ) && (
                                            <div className="mt-4 pl-7 grid grid-cols-1 md:grid-cols-3 gap-4">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Zorunlu mu?
                                                    </label>
                                                    <input
                                                        type="checkbox"
                                                        checked={
                                                            data.attributes.find(
                                                                (attr) =>
                                                                    attr.id ===
                                                                    attribute.id
                                                            )?.is_required ||
                                                            false
                                                        }
                                                        onChange={(e) =>
                                                            handleAttributeChange(
                                                                attribute.id,
                                                                "is_required",
                                                                e.target.checked
                                                            )
                                                        }
                                                        className="h-5 w-5 text-blue-600 rounded"
                                                    />
                                                </div>

                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Filtrelenebilir mi?
                                                    </label>
                                                    <input
                                                        type="checkbox"
                                                        checked={
                                                            data.attributes.find(
                                                                (attr) =>
                                                                    attr.id ===
                                                                    attribute.id
                                                            )?.is_filterable ||
                                                            false
                                                        }
                                                        onChange={(e) =>
                                                            handleAttributeChange(
                                                                attribute.id,
                                                                "is_filterable",
                                                                e.target.checked
                                                            )
                                                        }
                                                        className="h-5 w-5 text-blue-600 rounded"
                                                    />
                                                </div>

                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Sıralama
                                                    </label>
                                                    <input
                                                        type="number"
                                                        min="0"
                                                        value={
                                                            data.attributes.find(
                                                                (attr) =>
                                                                    attr.id ===
                                                                    attribute.id
                                                            )?.position || 0
                                                        }
                                                        onChange={(e) =>
                                                            handleAttributeChange(
                                                                attribute.id,
                                                                "position",
                                                                parseInt(
                                                                    e.target
                                                                        .value
                                                                )
                                                            )
                                                        }
                                                        className="w-full px-3 py-2 border rounded-md"
                                                    />
                                                </div>
                                            </div>
                                        )}

                                        {attribute.values &&
                                            attribute.values.length > 0 &&
                                            selectedAttributes.includes(
                                                attribute.id
                                            ) && (
                                                <div className="mt-4 pl-7">
                                                    <h4 className="text-sm font-medium text-gray-700 mb-2">
                                                        Değerler:
                                                    </h4>
                                                    <div className="flex flex-wrap gap-2">
                                                        {attribute.values.map(
                                                            (value) => (
                                                                <span
                                                                    key={
                                                                        value.id
                                                                    }
                                                                    className="inline-block bg-gray-100 px-2 py-1 rounded text-sm"
                                                                >
                                                                    {value.label ||
                                                                        value.value}
                                                                </span>
                                                            )
                                                        )}
                                                    </div>
                                                </div>
                                            )}
                                    </div>
                                ))
                            )}
                        </div>
                    </div>

                    <div className="flex justify-between">
                        <Link
                            href={route("admin.categories.edit", category.id)}
                            className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
                        >
                            İptal
                        </Link>

                        <button
                            type="submit"
                            disabled={processing}
                            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                        >
                            {processing
                                ? "Kaydediliyor..."
                                : "Değişiklikleri Kaydet"}
                        </button>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
