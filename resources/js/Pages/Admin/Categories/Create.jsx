import React, { useState, useEffect, useRef } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import CategoryTree from "@/Components/CategoryTree";
import CategoryService from "@/Services/CategoryService";
import AdminLayout from "@/Layouts/AdminLayout";

export default function Create({ categories: initialCategories }) {
    const { data, setData, post, processing, errors } = useForm({
        name: "",
        slug: "",
        description: "",
        parent_id: "",
        status: true,
        featured: false,
        show_in_menu: true,
        position: 0,
        image: "",
        icon: "",
    });

    const [categoryTree, setCategoryTree] = useState([]);
    const [showCategoryTree, setShowCategoryTree] = useState(false);
    const [isLoadingCategories, setIsLoadingCategories] = useState(false);
    const [categoryPath, setCategoryPath] = useState([]);
    const categoryDropdownRef = useRef(null);

    // Dışarıda bir yere tıklandığında kategori ağacını kapat
    useEffect(() => {
        function handleClickOutside(event) {
            if (
                categoryDropdownRef.current &&
                !categoryDropdownRef.current.contains(event.target)
            ) {
                setShowCategoryTree(false);
            }
        }

        // Event listener ekle
        document.addEventListener("mousedown", handleClickOutside);

        // Cleanup
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [categoryDropdownRef]);

    // Kategori ağacını yükle
    useEffect(() => {
        const loadCategoryTree = async () => {
            try {
                setIsLoadingCategories(true);
                const treeData = await CategoryService.getCategoryTree();
                setCategoryTree(treeData);
            } catch (error) {
                console.error("Kategori ağacı yüklenirken hata oluştu:", error);
                // Hata durumunda düz listeyi kullan
                setCategoryTree([]);
            } finally {
                setIsLoadingCategories(false);
            }
        };

        loadCategoryTree();
    }, []);

    // Kategori seçimi
    const handleCategorySelect = (categoryId) => {
        setData("parent_id", categoryId);
        setShowCategoryTree(false);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route("admin.categories.store"));
    };

    return (
        <AdminLayout title="Yeni Kategori Ekle">
            <Head title="Yeni Kategori Ekle" />

            <div className="mb-6">
                <Link
                    href={route("admin.categories.index")}
                    className="text-blue-600 hover:text-blue-800"
                >
                    &larr; Kategorilere Dön
                </Link>
            </div>

            <div className="bg-white shadow-md rounded-lg p-6">
                <form onSubmit={handleSubmit}>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label
                                htmlFor="name"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Kategori Adı
                            </label>
                            <input
                                type="text"
                                id="name"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.name}
                                onChange={(e) =>
                                    setData("name", e.target.value)
                                }
                                required
                            />
                            {errors.name && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.name}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="slug"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Slug (Boş bırakılırsa otomatik oluşturulur)
                            </label>
                            <input
                                type="text"
                                id="slug"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.slug}
                                onChange={(e) =>
                                    setData("slug", e.target.value)
                                }
                            />
                            {errors.slug && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.slug}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="parent_id"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Üst Kategori
                            </label>

                            <div className="relative" ref={categoryDropdownRef}>
                                {/* Seçili kategori gösterimi */}
                                <div
                                    className="flex items-center justify-between w-full px-3 py-2 border rounded-md cursor-pointer hover:border-gray-400"
                                    onClick={() =>
                                        setShowCategoryTree(!showCategoryTree)
                                    }
                                >
                                    <span
                                        className={
                                            data.parent_id
                                                ? "text-gray-900"
                                                : "text-gray-500"
                                        }
                                    >
                                        {data.parent_id
                                            ? categoryPath.length > 0
                                                ? categoryPath
                                                      .map((cat) => cat.name)
                                                      .join(" > ")
                                                : initialCategories.find(
                                                      (c) =>
                                                          c.id ===
                                                          parseInt(
                                                              data.parent_id
                                                          )
                                                  )?.name || "Ana Kategori"
                                            : "Ana Kategori"}
                                    </span>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-5 w-5 text-gray-400"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                    >
                                        <path
                                            fillRule="evenodd"
                                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                            clipRule="evenodd"
                                        />
                                    </svg>
                                </div>

                                {/* Kategori ağacı */}
                                {showCategoryTree && (
                                    <div className="absolute z-10 w-full mt-1">
                                        {isLoadingCategories ? (
                                            <div className="bg-white border rounded-md p-4 text-center">
                                                <svg
                                                    className="animate-spin h-5 w-5 text-blue-500 mx-auto"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <circle
                                                        className="opacity-25"
                                                        cx="12"
                                                        cy="12"
                                                        r="10"
                                                        stroke="currentColor"
                                                        strokeWidth="4"
                                                    ></circle>
                                                    <path
                                                        className="opacity-75"
                                                        fill="currentColor"
                                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                    ></path>
                                                </svg>
                                                <p className="mt-2 text-sm text-gray-600">
                                                    Kategoriler yükleniyor...
                                                </p>
                                            </div>
                                        ) : (
                                            <div className="bg-white border rounded-md">
                                                <div className="p-2 border-b">
                                                    <button
                                                        type="button"
                                                        className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                                                        onClick={() =>
                                                            handleCategorySelect(
                                                                ""
                                                            )
                                                        }
                                                    >
                                                        Ana Kategori (Üst
                                                        kategori yok)
                                                    </button>
                                                </div>
                                                <CategoryTree
                                                    categories={categoryTree}
                                                    selectedCategoryId={
                                                        data.parent_id
                                                            ? parseInt(
                                                                  data.parent_id
                                                              )
                                                            : null
                                                    }
                                                    onSelect={
                                                        handleCategorySelect
                                                    }
                                                    onCategoryPathChange={
                                                        setCategoryPath
                                                    }
                                                    showCheckbox={true}
                                                />
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>

                            {errors.parent_id && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.parent_id}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="position"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Sıralama Pozisyonu
                            </label>
                            <input
                                type="number"
                                id="position"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.position}
                                onChange={(e) =>
                                    setData(
                                        "position",
                                        parseInt(e.target.value) || 0
                                    )
                                }
                                min="0"
                            />
                            {errors.position && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.position}
                                </p>
                            )}
                        </div>

                        <div className="md:col-span-2">
                            <label
                                htmlFor="description"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Açıklama
                            </label>
                            <textarea
                                id="description"
                                className="w-full px-4 py-2 border rounded-lg"
                                rows="4"
                                value={data.description}
                                onChange={(e) =>
                                    setData("description", e.target.value)
                                }
                            ></textarea>
                            {errors.description && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.description}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="image"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Resim URL
                            </label>
                            <input
                                type="text"
                                id="image"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.image}
                                onChange={(e) =>
                                    setData("image", e.target.value)
                                }
                            />
                            {errors.image && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.image}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="icon"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                İkon URL
                            </label>
                            <input
                                type="text"
                                id="icon"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.icon}
                                onChange={(e) =>
                                    setData("icon", e.target.value)
                                }
                            />
                            {errors.icon && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.icon}
                                </p>
                            )}
                        </div>

                        <div className="md:col-span-2">
                            <div className="flex flex-wrap gap-4">
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="status"
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        checked={data.status}
                                        onChange={(e) =>
                                            setData("status", e.target.checked)
                                        }
                                    />
                                    <label
                                        htmlFor="status"
                                        className="ml-2 block text-sm text-gray-700"
                                    >
                                        Aktif
                                    </label>
                                </div>

                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="featured"
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        checked={data.featured}
                                        onChange={(e) =>
                                            setData(
                                                "featured",
                                                e.target.checked
                                            )
                                        }
                                    />
                                    <label
                                        htmlFor="featured"
                                        className="ml-2 block text-sm text-gray-700"
                                    >
                                        Öne Çıkan
                                    </label>
                                </div>

                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="show_in_menu"
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        checked={data.show_in_menu}
                                        onChange={(e) =>
                                            setData(
                                                "show_in_menu",
                                                e.target.checked
                                            )
                                        }
                                    />
                                    <label
                                        htmlFor="show_in_menu"
                                        className="ml-2 block text-sm text-gray-700"
                                    >
                                        Menüde Göster
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end">
                        <button
                            type="submit"
                            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                            disabled={processing}
                        >
                            {processing ? "Kaydediliyor..." : "Kaydet"}
                        </button>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
