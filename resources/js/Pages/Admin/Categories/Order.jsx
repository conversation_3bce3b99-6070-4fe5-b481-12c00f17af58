import React, { useState, useEffect } from "react";
import { Head, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from "@dnd-kit/core";
import {
    SortableContext,
    arrayMove,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import SortableCategory from "@/Components/SortableCategory";

const Order = ({ categories }) => {
    const [items, setItems] = useState([]);
    const [expandedCategories, setExpandedCategories] = useState({});

    const { data, setData, post, processing, errors, reset } = useForm({
        categories: [],
    });

    useEffect(() => {
        // Kategorileri düz bir liste haline getir
        const flattenCategories = (cats, parentId = null, level = 0) => {
            let result = [];

            cats.forEach((category) => {
                // Ana kategoriyi ekle
                result.push({
                    ...category,
                    level,
                    parent_id: parentId,
                });

                // Alt kategorileri ekle (eğer varsa ve genişletilmişse)
                if (
                    category.children &&
                    category.children.length > 0 &&
                    expandedCategories[category.id]
                ) {
                    result = [
                        ...result,
                        ...flattenCategories(
                            category.children,
                            category.id,
                            level + 1
                        ),
                    ];
                }
            });

            return result;
        };

        setItems(flattenCategories(categories));
    }, [categories, expandedCategories]);

    useEffect(() => {
        // Form verilerini güncelle
        const formattedCategories = items.map((item, index) => ({
            id: item.id,
            order: index,
            parent_id: item.parent_id,
        }));

        setData("categories", formattedCategories);
    }, [items]);

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragEnd = (event) => {
        const { active, over } = event;

        if (active.id !== over.id) {
            setItems((items) => {
                const oldIndex = items.findIndex(
                    (item) => item.id === active.id
                );
                const newIndex = items.findIndex((item) => item.id === over.id);

                return arrayMove(items, oldIndex, newIndex);
            });
        }
    };

    const toggleExpand = (categoryId) => {
        setExpandedCategories((prev) => ({
            ...prev,
            [categoryId]: !prev[categoryId],
        }));
    };

    const handleSave = () => {
        post(route("admin.categories.order.update"), {
            preserveScroll: true,
            onSuccess: () => {
                alert("Kategori sıralaması başarıyla güncellendi.");
            },
            onError: (errors) => {
                console.error("Hata:", errors);
                alert("Kategori sıralaması güncellenirken bir hata oluştu.");
            },
        });
    };

    const expandAll = () => {
        const newExpandedState = {};
        categories.forEach((category) => {
            if (category.children && category.children.length > 0) {
                newExpandedState[category.id] = true;

                // Alt kategorileri de genişlet
                const expandChildren = (children) => {
                    children.forEach((child) => {
                        if (child.children && child.children.length > 0) {
                            newExpandedState[child.id] = true;
                            expandChildren(child.children);
                        }
                    });
                };

                expandChildren(category.children);
            }
        });

        setExpandedCategories(newExpandedState);
    };

    const collapseAll = () => {
        setExpandedCategories({});
    };

    return (
        <AdminLayout>
            <Head title="Kategori Sıralama" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h1 className="text-2xl font-semibold text-gray-800">
                                    Kategori Sıralama
                                </h1>
                                <div className="flex space-x-2">
                                    <button
                                        type="button"
                                        onClick={expandAll}
                                        className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                                    >
                                        Tümünü Genişlet
                                    </button>
                                    <button
                                        type="button"
                                        onClick={collapseAll}
                                        className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                                    >
                                        Tümünü Daralt
                                    </button>
                                    <button
                                        type="button"
                                        onClick={handleSave}
                                        disabled={processing}
                                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                                    >
                                        {processing
                                            ? "Kaydediliyor..."
                                            : "Değişiklikleri Kaydet"}
                                    </button>
                                </div>
                            </div>

                            <div className="bg-gray-50 p-4 mb-6 rounded">
                                <p className="text-gray-700">
                                    Kategorileri sürükleyip bırakarak
                                    sıralayabilirsiniz. Değişiklikleri kaydetmek
                                    için "Değişiklikleri Kaydet" düğmesine
                                    tıklayın.
                                </p>
                            </div>

                            <div className="border rounded-lg overflow-hidden">
                                <div className="bg-gray-100 px-4 py-3 border-b">
                                    <div className="grid grid-cols-12 gap-4">
                                        <div className="col-span-8 font-medium">
                                            Kategori Adı
                                        </div>
                                        <div className="col-span-2 font-medium">
                                            Durum
                                        </div>
                                        <div className="col-span-2 font-medium">
                                            İşlemler
                                        </div>
                                    </div>
                                </div>

                                <div className="divide-y">
                                    <DndContext
                                        sensors={sensors}
                                        collisionDetection={closestCenter}
                                        onDragEnd={handleDragEnd}
                                        modifiers={[restrictToVerticalAxis]}
                                    >
                                        <SortableContext
                                            items={items.map((item) => item.id)}
                                            strategy={
                                                verticalListSortingStrategy
                                            }
                                        >
                                            {items.map((category) => (
                                                <SortableCategory
                                                    key={category.id}
                                                    category={category}
                                                    isExpanded={
                                                        expandedCategories[
                                                            category.id
                                                        ] || false
                                                    }
                                                    hasChildren={
                                                        category.children &&
                                                        category.children
                                                            .length > 0
                                                    }
                                                    onToggleExpand={() =>
                                                        toggleExpand(
                                                            category.id
                                                        )
                                                    }
                                                />
                                            ))}
                                        </SortableContext>
                                    </DndContext>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
};

export default Order;
