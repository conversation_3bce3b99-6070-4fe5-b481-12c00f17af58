import React, { useState } from "react";
import { Head, Link, router } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import { toast } from "react-hot-toast";

export default function Tree({ categories }) {
    const [expandedCategories, setExpandedCategories] = useState({});
    const [draggedCategory, setDraggedCategory] = useState(null);
    const [dragOverCategory, setDragOverCategory] = useState(null);
    const [dragPosition, setDragPosition] = useState(null); // 'before', 'after', 'inside'
    const [updatedCategories, setUpdatedCategories] = useState(categories);
    const [hasChanges, setHasChanges] = useState(false);

    const toggleExpand = (categoryId) => {
        setExpandedCategories({
            ...expandedCategories,
            [categoryId]: !expandedCategories[categoryId],
        });
    };

    const handleDragStart = (e, category) => {
        setDraggedCategory(category);
        e.dataTransfer.setData("text/plain", category.id);
        e.dataTransfer.effectAllowed = "move";
    };

    const handleDragOver = (e, category) => {
        e.preventDefault();
        if (!draggedCategory || draggedCategory.id === category.id) return;

        // Prevent circular references
        if (isDescendantOf(category, draggedCategory)) return;

        setDragOverCategory(category);

        // Determine drop position
        const rect = e.currentTarget.getBoundingClientRect();
        const y = e.clientY - rect.top;
        const height = rect.height;

        if (y < height * 0.25) {
            setDragPosition("before");
        } else if (y > height * 0.75) {
            setDragPosition("after");
        } else {
            setDragPosition("inside");
        }
    };

    const handleDragLeave = () => {
        setDragOverCategory(null);
        setDragPosition(null);
    };

    const handleDrop = (e, targetCategory) => {
        e.preventDefault();
        if (!draggedCategory || draggedCategory.id === targetCategory.id) return;

        // Prevent circular references
        if (isDescendantOf(targetCategory, draggedCategory)) {
            toast.error("Bir kategori kendi alt kategorisinin içine taşınamaz");
            setDraggedCategory(null);
            setDragOverCategory(null);
            setDragPosition(null);
            return;
        }

        const newCategories = [...updatedCategories];
        const draggedIndex = findCategoryIndex(newCategories, draggedCategory.id);
        
        if (draggedIndex === -1) return;
        
        const draggedItem = { ...newCategories[draggedIndex] };
        newCategories.splice(draggedIndex, 1);
        
        if (dragPosition === "inside") {
            // Move as a child
            draggedItem.parent_id = targetCategory.id;
            draggedItem.position = targetCategory.children ? targetCategory.children.length : 0;
            
            // Add to target's children
            const targetIndex = findCategoryIndex(newCategories, targetCategory.id);
            if (targetIndex !== -1) {
                if (!newCategories[targetIndex].children) {
                    newCategories[targetIndex].children = [];
                }
                newCategories[targetIndex].children.push(draggedItem);
                
                // Expand the target category
                setExpandedCategories({
                    ...expandedCategories,
                    [targetCategory.id]: true,
                });
            }
        } else {
            // Move before or after
            const targetIndex = findCategoryIndex(newCategories, targetCategory.id);
            if (targetIndex !== -1) {
                draggedItem.parent_id = newCategories[targetIndex].parent_id;
                
                const insertIndex = dragPosition === "before" ? targetIndex : targetIndex + 1;
                newCategories.splice(insertIndex, 0, draggedItem);
                
                // Update positions
                updatePositions(newCategories);
            }
        }
        
        setUpdatedCategories(newCategories);
        setHasChanges(true);
        setDraggedCategory(null);
        setDragOverCategory(null);
        setDragPosition(null);
    };

    const isDescendantOf = (category, potentialAncestor) => {
        if (!category || !potentialAncestor) return false;
        if (category.parent_id === potentialAncestor.id) return true;
        
        if (potentialAncestor.children) {
            for (const child of potentialAncestor.children) {
                if (isDescendantOf(category, child)) return true;
            }
        }
        
        return false;
    };

    const findCategoryIndex = (categories, id) => {
        for (let i = 0; i < categories.length; i++) {
            if (categories[i].id === id) return i;
            
            if (categories[i].children) {
                const index = findCategoryIndex(categories[i].children, id);
                if (index !== -1) return index;
            }
        }
        
        return -1;
    };

    const updatePositions = (categories) => {
        for (let i = 0; i < categories.length; i++) {
            categories[i].position = i;
            
            if (categories[i].children) {
                updatePositions(categories[i].children);
            }
        }
    };

    const saveChanges = () => {
        // Flatten the category tree to get all categories with their new positions and parent_ids
        const flattenedCategories = flattenCategories(updatedCategories);
        
        router.put(route("admin.categories.positions"), {
            categories: flattenedCategories
        }, {
            onSuccess: () => {
                toast.success("Kategori sıralaması başarıyla güncellendi");
                setHasChanges(false);
            },
            onError: () => {
                toast.error("Kategori sıralaması güncellenirken bir hata oluştu");
            }
        });
    };

    const flattenCategories = (categories, result = []) => {
        for (const category of categories) {
            result.push({
                id: category.id,
                position: category.position,
                parent_id: category.parent_id
            });
            
            if (category.children && category.children.length > 0) {
                flattenCategories(category.children, result);
            }
        }
        
        return result;
    };

    const toggleFeatured = (category) => {
        router.put(route("admin.categories.featured", category.id), {
            featured: !category.featured
        }, {
            onSuccess: () => {
                toast.success(`Kategori ${category.featured ? 'öne çıkarılmadı' : 'öne çıkarıldı'}`);
                
                // Update local state
                const newCategories = [...updatedCategories];
                updateCategoryProperty(newCategories, category.id, 'featured', !category.featured);
                setUpdatedCategories(newCategories);
            }
        });
    };

    const toggleMenuVisibility = (category) => {
        router.put(route("admin.categories.menu-visibility", category.id), {
            show_in_menu: !category.show_in_menu
        }, {
            onSuccess: () => {
                toast.success(`Kategori menüde ${category.show_in_menu ? 'gizlendi' : 'gösterildi'}`);
                
                // Update local state
                const newCategories = [...updatedCategories];
                updateCategoryProperty(newCategories, category.id, 'show_in_menu', !category.show_in_menu);
                setUpdatedCategories(newCategories);
            }
        });
    };

    const updateCategoryProperty = (categories, id, property, value) => {
        for (let i = 0; i < categories.length; i++) {
            if (categories[i].id === id) {
                categories[i][property] = value;
                return true;
            }
            
            if (categories[i].children && categories[i].children.length > 0) {
                if (updateCategoryProperty(categories[i].children, id, property, value)) {
                    return true;
                }
            }
        }
        
        return false;
    };

    const renderCategory = (category, level = 0) => {
        const isExpanded = expandedCategories[category.id];
        const hasChildren = category.children && category.children.length > 0;
        const isDragOver = dragOverCategory && dragOverCategory.id === category.id;
        
        return (
            <div key={category.id}>
                <div
                    className={`flex items-center p-2 border-b ${
                        isDragOver
                            ? dragPosition === "before"
                                ? "border-t-2 border-t-blue-500"
                                : dragPosition === "after"
                                ? "border-b-2 border-b-blue-500"
                                : "bg-blue-50"
                            : ""
                    } ${!category.status ? "opacity-50" : ""}`}
                    style={{ paddingLeft: `${level * 20 + 8}px` }}
                    draggable
                    onDragStart={(e) => handleDragStart(e, category)}
                    onDragOver={(e) => handleDragOver(e, category)}
                    onDragLeave={handleDragLeave}
                    onDrop={(e) => handleDrop(e, category)}
                >
                    <div className="flex items-center flex-1">
                        {hasChildren && (
                            <button
                                onClick={() => toggleExpand(category.id)}
                                className="mr-2 text-gray-500 hover:text-gray-700"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className={`h-4 w-4 transition-transform ${
                                        isExpanded ? "transform rotate-90" : ""
                                    }`}
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 5l7 7-7 7"
                                    />
                                </svg>
                            </button>
                        )}
                        {!hasChildren && (
                            <span className="mr-2 w-4"></span>
                        )}
                        <span className="font-medium">{category.name}</span>
                        {category.featured && (
                            <span className="ml-2 px-2 py-0.5 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                                Öne Çıkan
                            </span>
                        )}
                        {!category.show_in_menu && (
                            <span className="ml-2 px-2 py-0.5 bg-gray-100 text-gray-800 text-xs rounded-full">
                                Menüde Gizli
                            </span>
                        )}
                    </div>
                    <div className="flex items-center space-x-2">
                        <button
                            onClick={() => toggleFeatured(category)}
                            className={`p-1 rounded ${
                                category.featured
                                    ? "text-yellow-500 hover:text-yellow-700"
                                    : "text-gray-400 hover:text-gray-600"
                            }`}
                            title={category.featured ? "Öne çıkarmayı kaldır" : "Öne çıkar"}
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5"
                                fill={category.featured ? "currentColor" : "none"}
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                                />
                            </svg>
                        </button>
                        <button
                            onClick={() => toggleMenuVisibility(category)}
                            className={`p-1 rounded ${
                                category.show_in_menu
                                    ? "text-blue-500 hover:text-blue-700"
                                    : "text-gray-400 hover:text-gray-600"
                            }`}
                            title={category.show_in_menu ? "Menüde gizle" : "Menüde göster"}
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d={
                                        category.show_in_menu
                                            ? "M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                            : "M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                                    }
                                />
                            </svg>
                        </button>
                        <Link
                            href={route("admin.categories.edit", category.id)}
                            className="p-1 text-indigo-600 hover:text-indigo-900"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                />
                            </svg>
                        </Link>
                    </div>
                </div>
                {hasChildren && isExpanded && (
                    <div>
                        {category.children.map((child) => renderCategory(child, level + 1))}
                    </div>
                )}
            </div>
        );
    };

    return (
        <AdminLayout title="Kategori Ağacı">
            <Head title="Kategori Ağacı" />

            <div className="mb-6 flex justify-between items-center">
                <div>
                    <Link
                        href={route("admin.categories.index")}
                        className="text-blue-600 hover:text-blue-800"
                    >
                        &larr; Kategorilere Dön
                    </Link>
                </div>
                <div className="flex space-x-2">
                    {hasChanges && (
                        <button
                            onClick={saveChanges}
                            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
                        >
                            Değişiklikleri Kaydet
                        </button>
                    )}
                    <Link
                        href={route("admin.categories.create")}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                    >
                        Yeni Kategori
                    </Link>
                </div>
            </div>

            <div className="bg-white shadow-md rounded-lg overflow-hidden">
                <div className="p-4 bg-gray-50 border-b">
                    <h3 className="font-medium">Kategori Ağacı</h3>
                    <p className="text-sm text-gray-500">
                        Kategorileri sürükleyip bırakarak sıralayabilir veya iç içe yerleştirebilirsiniz.
                    </p>
                </div>

                <div className="divide-y">
                    {updatedCategories.length === 0 ? (
                        <div className="p-4 text-center text-gray-500">
                            Henüz kategori bulunmuyor.
                        </div>
                    ) : (
                        updatedCategories.map((category) => renderCategory(category))
                    )}
                </div>
            </div>
        </AdminLayout>
    );
}
