import React from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import InputError from "@/Components/InputError";

export default function Edit({ coupon }) {
    // Tarihleri input için uygun formata dönüştür
    const formatDateForInput = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toISOString().slice(0, 16); // "YYYY-MM-DDThh:mm" formatı
    };

    const { data, setData, put, processing, errors } = useForm({
        code: coupon.code || "",
        description: coupon.description || "",
        type: coupon.type || "percentage",
        value: coupon.value || "",
        min_order_amount: coupon.min_order_amount || "",
        max_uses: coupon.max_uses || "",
        is_active: coupon.is_active,
        starts_at: formatDateForInput(coupon.starts_at),
        expires_at: formatDateForInput(coupon.expires_at),
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route("admin.coupons.update", coupon.id));
    };

    return (
        <AdminLayout title={`Kupon Düzenle: ${coupon.code}`}>
            <Head title={`Kupon Düzenle: ${coupon.code}`} />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6">
                            <div className="flex justify-between items-center mb-6">
                                <h3 className="text-lg font-medium">
                                    Kupon Düzenle
                                </h3>
                                <Link
                                    href={route("admin.coupons.index")}
                                    className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded"
                                >
                                    Geri Dön
                                </Link>
                            </div>

                            <form onSubmit={handleSubmit}>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {/* Kupon Kodu */}
                                    <div className="mb-6">
                                        <label
                                            htmlFor="code"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Kupon Kodu
                                        </label>
                                        <input
                                            id="code"
                                            type="text"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.code}
                                            onChange={(e) =>
                                                setData("code", e.target.value.toUpperCase())
                                            }
                                            required
                                        />
                                        <InputError
                                            message={errors.code}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Açıklama */}
                                    <div className="mb-6">
                                        <label
                                            htmlFor="description"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Açıklama
                                        </label>
                                        <input
                                            id="description"
                                            type="text"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.description}
                                            onChange={(e) =>
                                                setData("description", e.target.value)
                                            }
                                        />
                                        <InputError
                                            message={errors.description}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* İndirim Tipi */}
                                    <div className="mb-6">
                                        <label
                                            htmlFor="type"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            İndirim Tipi
                                        </label>
                                        <select
                                            id="type"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.type}
                                            onChange={(e) =>
                                                setData("type", e.target.value)
                                            }
                                            required
                                        >
                                            <option value="percentage">Yüzde İndirim (%)</option>
                                            <option value="fixed">Sabit Tutar İndirimi (TL)</option>
                                        </select>
                                        <InputError
                                            message={errors.type}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* İndirim Değeri */}
                                    <div className="mb-6">
                                        <label
                                            htmlFor="value"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            İndirim Değeri {data.type === 'percentage' ? '(%)' : '(TL)'}
                                        </label>
                                        <input
                                            id="value"
                                            type="number"
                                            min="0"
                                            step={data.type === 'percentage' ? '1' : '0.01'}
                                            max={data.type === 'percentage' ? '100' : null}
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.value}
                                            onChange={(e) =>
                                                setData("value", e.target.value)
                                            }
                                            required
                                        />
                                        <InputError
                                            message={errors.value}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Minimum Sipariş Tutarı */}
                                    <div className="mb-6">
                                        <label
                                            htmlFor="min_order_amount"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Minimum Sipariş Tutarı (TL)
                                        </label>
                                        <input
                                            id="min_order_amount"
                                            type="number"
                                            min="0"
                                            step="0.01"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.min_order_amount}
                                            onChange={(e) =>
                                                setData("min_order_amount", e.target.value)
                                            }
                                        />
                                        <InputError
                                            message={errors.min_order_amount}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Maksimum Kullanım Sayısı */}
                                    <div className="mb-6">
                                        <label
                                            htmlFor="max_uses"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Maksimum Kullanım Sayısı
                                        </label>
                                        <input
                                            id="max_uses"
                                            type="number"
                                            min="0"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.max_uses}
                                            onChange={(e) =>
                                                setData("max_uses", e.target.value)
                                            }
                                        />
                                        <InputError
                                            message={errors.max_uses}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Başlangıç Tarihi */}
                                    <div className="mb-6">
                                        <label
                                            htmlFor="starts_at"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Başlangıç Tarihi
                                        </label>
                                        <input
                                            id="starts_at"
                                            type="datetime-local"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.starts_at}
                                            onChange={(e) =>
                                                setData("starts_at", e.target.value)
                                            }
                                        />
                                        <InputError
                                            message={errors.starts_at}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Bitiş Tarihi */}
                                    <div className="mb-6">
                                        <label
                                            htmlFor="expires_at"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Bitiş Tarihi
                                        </label>
                                        <input
                                            id="expires_at"
                                            type="datetime-local"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.expires_at}
                                            onChange={(e) =>
                                                setData("expires_at", e.target.value)
                                            }
                                        />
                                        <InputError
                                            message={errors.expires_at}
                                            className="mt-2"
                                        />
                                    </div>
                                </div>

                                {/* Kullanım Bilgisi */}
                                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                                    <h4 className="font-medium text-gray-700 mb-2">Kullanım Bilgisi</h4>
                                    <p className="text-gray-600">Bu kupon şu ana kadar <strong>{coupon.used_count}</strong> kez kullanılmıştır.</p>
                                </div>

                                {/* Aktif/Pasif */}
                                <div className="mb-6">
                                    <div className="flex items-center">
                                        <input
                                            id="is_active"
                                            type="checkbox"
                                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                            checked={data.is_active}
                                            onChange={(e) =>
                                                setData("is_active", e.target.checked)
                                            }
                                        />
                                        <label
                                            htmlFor="is_active"
                                            className="ml-2 block text-sm text-gray-900"
                                        >
                                            Kupon Aktif
                                        </label>
                                    </div>
                                    <InputError
                                        message={errors.is_active}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="flex items-center justify-end">
                                    <Link
                                        href={route("admin.coupons.index")}
                                        className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded mr-2"
                                    >
                                        İptal
                                    </Link>
                                    <button
                                        type="submit"
                                        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                        disabled={processing}
                                    >
                                        {processing ? "Kaydediliyor..." : "Güncelle"}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
