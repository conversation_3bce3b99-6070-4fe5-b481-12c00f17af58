import React from "react";
import { <PERSON>, <PERSON> } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import { PlusIcon } from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";
import { axiosDelete } from "@/Utils/inertiaHelper";

export default function ShippingCompanyZones({ company, zones }) {
    const handleDeleteZone = (zoneId) => {
        if (confirm("Bu kargo bölgesini silmek istediğinize emin misiniz?")) {
            const url = route("admin.shipping.zones.destroy", zoneId);

            axiosDelete(url)
                .then((response) => {
                    if (response.data && response.data.success) {
                        toast.success(
                            response.data.message || "Kargo bölgesi silindi"
                        );
                        window.location.reload();
                    } else {
                        toast.error("Kargo bölgesi silinirken bir hata oluştu");
                    }
                })
                .catch((error) => {
                    console.error(error);
                    let errorMessage = "Kargo bölgesi silinirken bir hata o<PERSON>";

                    if (
                        error.response &&
                        error.response.data &&
                        error.response.data.message
                    ) {
                        errorMessage = error.response.data.message;
                    }

                    toast.error(errorMessage);
                });
        }
    };

    return (
        <AdminLayout>
            <Head title={`${company.name} - Kargo Bölgeleri`} />

            <div className="container mx-auto py-6">
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h1 className="text-2xl font-semibold text-gray-900">
                            {company.name} - Kargo Bölgeleri
                        </h1>
                        <p className="text-sm text-gray-500 mt-1">
                            Bu kargo şirketinin metodlarının uygulandığı bölgeleri görüntüleyin
                        </p>
                    </div>
                    <div className="flex space-x-2">
                        <Link
                            href={route("admin.shipping-companies.index")}
                            className="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                        >
                            Geri Dön
                        </Link>
                        <Link
                            href={route("admin.shipping.zones.create")}
                            className="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                        >
                            <PlusIcon className="w-5 h-5 mr-2" />
                            Yeni Bölge Ekle
                        </Link>
                    </div>
                </div>

                <div className="bg-white shadow-md rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    ID
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Bölge Adı
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Açıklama
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Lokasyon Sayısı
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Metod Sayısı
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Durum
                                </th>
                                <th
                                    scope="col"
                                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    İşlemler
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {zones && zones.length > 0 ? (
                                zones.map((zone) => (
                                    <tr key={zone.id}>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {zone.id}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {zone.name}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {zone.description || "-"}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {zone.locations_count || 0}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {zone.methods_count || 0}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <span
                                                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                    zone.is_active
                                                        ? "bg-green-100 text-green-800"
                                                        : "bg-red-100 text-red-800"
                                                }`}
                                            >
                                                {zone.is_active
                                                    ? "Aktif"
                                                    : "Pasif"}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end space-x-2">
                                                <Link
                                                    href={route(
                                                        "admin.shipping.zones.locations",
                                                        zone.id
                                                    )}
                                                    className="text-blue-600 hover:text-blue-900"
                                                >
                                                    Lokasyonlar
                                                </Link>
                                                <Link
                                                    href={route(
                                                        "admin.shipping.zones.methods",
                                                        zone.id
                                                    )}
                                                    className="text-indigo-600 hover:text-indigo-900"
                                                >
                                                    Metodlar
                                                </Link>
                                                <Link
                                                    href={route(
                                                        "admin.shipping.zones.edit",
                                                        zone.id
                                                    )}
                                                    className="text-yellow-600 hover:text-yellow-900"
                                                >
                                                    Düzenle
                                                </Link>
                                                <button
                                                    onClick={() => handleDeleteZone(zone.id)}
                                                    className="text-red-600 hover:text-red-900"
                                                >
                                                    Sil
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td
                                        colSpan="7"
                                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center"
                                    >
                                        Bu kargo şirketinin metodlarının uygulandığı bölge bulunmuyor.
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </AdminLayout>
    );
}
