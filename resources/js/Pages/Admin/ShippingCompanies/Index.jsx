import React, { useState } from "react";
import { Head, <PERSON>, router } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import {
    PencilIcon,
    TrashIcon,
    PlusIcon,
    CheckIcon,
    XMarkIcon,
} from "@heroicons/react/24/outline";
import Pagination from "@/Components/Pagination";
import DeleteConfirmationModal from "@/Components/DeleteConfirmationModal";

export default function Index({ companies }) {
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [companyToDelete, setCompanyToDelete] = useState(null);

    const confirmDelete = (company) => {
        setCompanyToDelete(company);
        setDeleteModalOpen(true);
    };

    const deleteCompany = () => {
        router.delete(
            route("admin.shipping-companies.destroy", companyToDelete.id),
            {
                onSuccess: () => {
                    setDeleteModalOpen(false);
                    setCompanyToDelete(null);
                },
            }
        );
    };

    const toggleStatus = (company) => {
        router.post(
            route("admin.shipping-companies.toggle-status", company.id)
        );
    };

    return (
        <AdminLayout>
            <Head title="Kargo Şirketleri" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-semibold text-gray-900">
                                    Kargo Şirketleri
                                </h2>
                                <Link
                                    href={route(
                                        "admin.shipping-companies.create"
                                    )}
                                    className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition"
                                >
                                    <PlusIcon className="w-4 h-4 mr-2" />
                                    Yeni Kargo Şirketi
                                </Link>
                            </div>

                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Logo
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Şirket Adı
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Kod
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Durum
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                API Entegrasyonu
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                İşlemler
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {companies.length > 0 ? (
                                            companies.map((company) => (
                                                <tr key={company.id}>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        {company.logo ? (
                                                            <img
                                                                src={`/storage/${company.logo}`}
                                                                alt={
                                                                    company.name
                                                                }
                                                                className="h-10 w-auto"
                                                            />
                                                        ) : (
                                                            <div className="h-10 w-10 bg-gray-200 rounded flex items-center justify-center text-gray-500">
                                                                {company.name.charAt(
                                                                    0
                                                                )}
                                                            </div>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {company.name}
                                                        </div>
                                                        {company.description && (
                                                            <div className="text-sm text-gray-500">
                                                                {
                                                                    company.description
                                                                }
                                                            </div>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            {company.code}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <button
                                                            onClick={() =>
                                                                toggleStatus(
                                                                    company
                                                                )
                                                            }
                                                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                                company.is_active
                                                                    ? "bg-green-100 text-green-800"
                                                                    : "bg-red-100 text-red-800"
                                                            }`}
                                                        >
                                                            {company.is_active ? (
                                                                <>
                                                                    <CheckIcon className="w-4 h-4 mr-1" />
                                                                    Aktif
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <XMarkIcon className="w-4 h-4 mr-1" />
                                                                    Pasif
                                                                </>
                                                            )}
                                                        </button>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span
                                                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                                company.api_key &&
                                                                company.api_endpoint
                                                                    ? "bg-blue-100 text-blue-800"
                                                                    : "bg-gray-100 text-gray-800"
                                                            }`}
                                                        >
                                                            {company.api_key &&
                                                            company.api_endpoint
                                                                ? "Entegre"
                                                                : "Entegre Değil"}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                        <div className="flex space-x-2">
                                                            <Link
                                                                href={route(
                                                                    "admin.shipping-companies.methods",
                                                                    company.id
                                                                )}
                                                                className="text-blue-600 hover:text-blue-900 bg-blue-100 px-2 py-1 rounded"
                                                                title="Kargo Metodları"
                                                            >
                                                                Metodlar{" "}
                                                                {company.methods_count >
                                                                    0 &&
                                                                    `(${company.methods_count})`}
                                                            </Link>
                                                            <Link
                                                                href={route(
                                                                    "admin.shipping-companies.zones",
                                                                    company.id
                                                                )}
                                                                className="text-green-600 hover:text-green-900 bg-green-100 px-2 py-1 rounded"
                                                                title="Kargo Bölgeleri"
                                                            >
                                                                Bölgeler{" "}
                                                                {company.zones_count >
                                                                    0 &&
                                                                    `(${company.zones_count})`}
                                                            </Link>
                                                            <Link
                                                                href={route(
                                                                    "admin.shipping-companies.edit",
                                                                    company.id
                                                                )}
                                                                className="text-indigo-600 hover:text-indigo-900"
                                                            >
                                                                <PencilIcon className="w-5 h-5" />
                                                            </Link>
                                                            <button
                                                                onClick={() =>
                                                                    confirmDelete(
                                                                        company
                                                                    )
                                                                }
                                                                className="text-red-600 hover:text-red-900"
                                                            >
                                                                <TrashIcon className="w-5 h-5" />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))
                                        ) : (
                                            <tr>
                                                <td
                                                    colSpan="6"
                                                    className="px-6 py-4 text-center text-sm text-gray-500"
                                                >
                                                    Henüz kargo şirketi
                                                    eklenmemiş.
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <DeleteConfirmationModal
                isOpen={deleteModalOpen}
                onClose={() => setDeleteModalOpen(false)}
                onConfirm={deleteCompany}
                title="Kargo Şirketini Sil"
                message={`"${companyToDelete?.name}" kargo şirketini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`}
            />
        </AdminLayout>
    );
}
