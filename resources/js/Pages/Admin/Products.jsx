import React, { useState } from "react";
import { Head, Link, router } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import Pagination from "@/Components/Pagination";
import { toast } from "react-hot-toast";
import { safeDelete } from "@/Utils/inertiaHelper";

export default function Products({ products, categories, filters, flash }) {
    const [searchTerm, setSearchTerm] = useState(filters.search || "");
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [productToDelete, setProductToDelete] = useState(null);

    // Flash mesajı varsa göster
    React.useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
    }, [flash.success]);

    function handleSearch(e) {
        e.preventDefault();
        router.get(
            route("admin.products.index"),
            { search: searchTerm, per_page: filters.per_page },
            { preserveState: true }
        );
    }

    function handlePerPageChange(e) {
        router.get(
            route("admin.products.index"),
            { search: searchTerm, per_page: e.target.value },
            { preserveState: true }
        );
    }

    function confirmDelete(product) {
        setProductToDelete(product);
        setDeleteModalOpen(true);
    }

    function handleDelete() {
        if (!productToDelete) return;

        // router.delete yerine safeDelete kullan
        safeDelete(route("admin.products.destroy", productToDelete.id), {
            onSuccess: () => {
                setDeleteModalOpen(false);
                setProductToDelete(null);
                toast.success("Ürün başarıyla silindi");
            },
            onError: () => {
                toast.error("Ürün silinirken bir hata oluştu");
            },
        });
    }
    return (
        <AdminLayout title="Ürünler">
            <Head title="Ürünler" />

            <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div className="p-6 bg-white border-b border-gray-200">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-medium">Ürün Listesi</h3>
                        <a
                            href={route("admin.products.create")}
                            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                        >
                            Yeni Ürün Ekle
                        </a>
                    </div>

                    {/* Arama ve Filtreleme */}
                    <div className="mb-6">
                        <form onSubmit={handleSearch} className="flex gap-2">
                            <div className="flex-1">
                                <input
                                    type="text"
                                    placeholder="Ürün ara..."
                                    className="w-full px-4 py-2 border rounded-lg"
                                    value={searchTerm}
                                    onChange={(e) =>
                                        setSearchTerm(e.target.value)
                                    }
                                />
                            </div>
                            <button
                                type="submit"
                                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg"
                            >
                                Ara
                            </button>
                            <select
                                className="px-4 py-2 border rounded-lg"
                                value={filters.per_page}
                                onChange={handlePerPageChange}
                            >
                                <option value="5">5 / sayfa</option>
                                <option value="10">10 / sayfa</option>
                                <option value="25">25 / sayfa</option>
                                <option value="50">50 / sayfa</option>
                            </select>
                        </form>
                    </div>

                    {/* Ürün Tablosu */}
                    <div className="overflow-x-auto">
                        <table className="min-w-full bg-white">
                            <thead className="bg-gray-100">
                                <tr>
                                    <th className="py-2 px-4 border-b text-left">
                                        ID
                                    </th>
                                    <th className="py-2 px-4 border-b text-left">
                                        Ürün Adı
                                    </th>
                                    <th className="py-2 px-4 border-b text-left">
                                        Kategori
                                    </th>
                                    <th className="py-2 px-4 border-b text-left">
                                        Fiyat
                                    </th>
                                    <th className="py-2 px-4 border-b text-left">
                                        Stok
                                    </th>
                                    <th className="py-2 px-4 border-b text-left">
                                        Durum
                                    </th>
                                    <th className="py-2 px-4 border-b text-left">
                                        İşlemler
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {products.data.map((product) => (
                                    <tr key={product.id}>
                                        <td className="py-2 px-4 border-b">
                                            {product.id}
                                        </td>
                                        <td className="py-2 px-4 border-b">
                                            {product.name}
                                        </td>
                                        <td className="py-2 px-4 border-b">
                                            {product.category
                                                ? product.category.name
                                                : "-"}
                                        </td>
                                        <td className="py-2 px-4 border-b">
                                            {product.price} TL
                                        </td>
                                        <td className="py-2 px-4 border-b">
                                            {product.stock}
                                        </td>
                                        <td className="py-2 px-4 border-b">
                                            <span
                                                className={`px-2 py-1 rounded text-xs ${
                                                    product.status
                                                        ? "bg-green-100 text-green-800"
                                                        : "bg-red-100 text-red-800"
                                                }`}
                                            >
                                                {product.status
                                                    ? "Aktif"
                                                    : "Pasif"}
                                            </span>
                                        </td>
                                        <td className="py-2 px-4 border-b">
                                            <a
                                                href={route(
                                                    "admin.products.edit",
                                                    product.id
                                                )}
                                                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mr-2"
                                            >
                                                Düzenle
                                            </a>
                                            <button
                                                onClick={() =>
                                                    confirmDelete(product)
                                                }
                                                className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                                            >
                                                Sil
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Sayfalama */}
                    <div className="mt-6">
                        <Pagination links={products.links} />
                    </div>
                </div>
            </div>

            {/* Silme Onay Modalı */}
            {deleteModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Ürünü Sil
                        </h3>
                        <p className="mb-4 text-gray-600">
                            <strong>{productToDelete?.name}</strong> adlı ürünü
                            silmek istediğinize emin misiniz? Bu işlem geri
                            alınamaz.
                        </p>
                        <div className="flex justify-end">
                            <button
                                type="button"
                                className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded mr-2"
                                onClick={() => setDeleteModalOpen(false)}
                            >
                                İptal
                            </button>
                            <button
                                type="button"
                                className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                onClick={handleDelete}
                            >
                                Sil
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </AdminLayout>
    );
}
