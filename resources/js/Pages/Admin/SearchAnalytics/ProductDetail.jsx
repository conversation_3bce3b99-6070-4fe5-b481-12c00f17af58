import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/Components/ui/tabs';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

export default function ProductDetail({ product, stats, productViewTrends, searchTerms }) {
    const [timeRange, setTimeRange] = useState(stats.days.toString());
    
    // Renk paleti
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658', '#8dd1e1', '#a4de6c', '#d0ed57'];
    
    // <PERSON><PERSON>h a<PERSON>ığını değiştir
    const handleTimeRangeChange = (value) => {
        setTimeRange(value);
        window.location.href = `/admin/search-analytics/product/${product.id}?days=${value}`;
    };
    
    // Arama terimi detay sayfasına git
    const goToSearchTermDetail = (query) => {
        window.location.href = `/admin/search-analytics/term/${encodeURIComponent(query)}?days=${timeRange}`;
    };
    
    // Tarih formatla
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('tr-TR');
    };
    
    return (
        <AdminLayout>
            <Head title={`Ürün: ${product.name}`} />
            
            <div className="container mx-auto py-6">
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h1 className="text-2xl font-bold">Ürün: {product.name}</h1>
                        <p className="text-gray-500">Stok Kodu: {product.stock_code}</p>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">Zaman Aralığı:</span>
                        <select 
                            value={timeRange} 
                            onChange={(e) => handleTimeRangeChange(e.target.value)}
                            className="border border-gray-300 rounded px-2 py-1 text-sm"
                        >
                            <option value="7">Son 7 Gün</option>
                            <option value="30">Son 30 Gün</option>
                            <option value="90">Son 90 Gün</option>
                            <option value="365">Son 1 Yıl</option>
                        </select>
                    </div>
                </div>
                
                {/* Özet İstatistikler */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">Görüntülenme Sayısı</div>
                            <div className="text-3xl font-bold mt-2">{stats.viewCount}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">Benzersiz Ziyaretçi</div>
                            <div className="text-3xl font-bold mt-2">{stats.uniqueViews}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">Dönüşüm Sayısı</div>
                            <div className="text-3xl font-bold mt-2">{stats.conversionCount}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">Dönüşüm Oranı</div>
                            <div className="text-3xl font-bold mt-2">{stats.conversionRate}%</div>
                        </CardContent>
                    </Card>
                </div>
                
                <Tabs defaultValue="overview" className="mb-6">
                    <TabsList className="mb-4">
                        <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
                        <TabsTrigger value="search-terms">Arama Terimleri</TabsTrigger>
                    </TabsList>
                    
                    {/* Genel Bakış Sekmesi */}
                    <TabsContent value="overview">
                        <div className="grid grid-cols-1 gap-6">
                            {/* Görüntülenme Trendi Grafiği */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Görüntülenme Trendi</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-80">
                                        <ResponsiveContainer width="100%" height="100%">
                                            <LineChart
                                                data={productViewTrends}
                                                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis 
                                                    dataKey="date" 
                                                    tickFormatter={formatDate}
                                                />
                                                <YAxis />
                                                <Tooltip 
                                                    formatter={(value) => [value, 'Görüntülenme Sayısı']}
                                                    labelFormatter={formatDate}
                                                />
                                                <Legend />
                                                <Line 
                                                    type="monotone" 
                                                    dataKey="view_count" 
                                                    name="Görüntülenme Sayısı" 
                                                    stroke="#0088FE" 
                                                    activeDot={{ r: 8 }} 
                                                />
                                            </LineChart>
                                        </ResponsiveContainer>
                                    </div>
                                </CardContent>
                            </Card>
                            
                            {/* Ürün Bilgileri */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Ürün Bilgileri</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <h3 className="text-lg font-medium mb-2">Temel Bilgiler</h3>
                                            <table className="w-full text-sm">
                                                <tbody>
                                                    <tr>
                                                        <td className="py-2 font-medium">ID:</td>
                                                        <td className="py-2">{product.id}</td>
                                                    </tr>
                                                    <tr>
                                                        <td className="py-2 font-medium">Ürün Adı:</td>
                                                        <td className="py-2">{product.name}</td>
                                                    </tr>
                                                    <tr>
                                                        <td className="py-2 font-medium">Stok Kodu:</td>
                                                        <td className="py-2">{product.stock_code}</td>
                                                    </tr>
                                                    {product.category && (
                                                        <tr>
                                                            <td className="py-2 font-medium">Kategori:</td>
                                                            <td className="py-2">{product.category.name}</td>
                                                        </tr>
                                                    )}
                                                    <tr>
                                                        <td className="py-2 font-medium">Fiyat:</td>
                                                        <td className="py-2">
                                                            {new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' }).format(product.price)}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                        <div>
                                            <h3 className="text-lg font-medium mb-2">Arama İstatistikleri</h3>
                                            <table className="w-full text-sm">
                                                <tbody>
                                                    <tr>
                                                        <td className="py-2 font-medium">Toplam Görüntülenme:</td>
                                                        <td className="py-2">{stats.viewCount}</td>
                                                    </tr>
                                                    <tr>
                                                        <td className="py-2 font-medium">Benzersiz Ziyaretçi:</td>
                                                        <td className="py-2">{stats.uniqueViews}</td>
                                                    </tr>
                                                    <tr>
                                                        <td className="py-2 font-medium">Dönüşüm Sayısı:</td>
                                                        <td className="py-2">{stats.conversionCount}</td>
                                                    </tr>
                                                    <tr>
                                                        <td className="py-2 font-medium">Dönüşüm Oranı:</td>
                                                        <td className="py-2">{stats.conversionRate}%</td>
                                                    </tr>
                                                    <tr>
                                                        <td className="py-2 font-medium">Arama Terimi Sayısı:</td>
                                                        <td className="py-2">{stats.searchTermsCount}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>
                    
                    {/* Arama Terimleri Sekmesi */}
                    <TabsContent value="search-terms">
                        <div className="grid grid-cols-1 gap-6">
                            {/* Arama Terimleri Grafiği */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Arama Terimleri</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-80">
                                        <ResponsiveContainer width="100%" height="100%">
                                            <BarChart
                                                data={searchTerms}
                                                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                                layout="vertical"
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis type="number" />
                                                <YAxis 
                                                    dataKey="search_query" 
                                                    type="category" 
                                                    width={150}
                                                    tick={{ fontSize: 12 }}
                                                />
                                                <Tooltip 
                                                    formatter={(value) => [value, 'Görüntülenme Sayısı']}
                                                />
                                                <Legend />
                                                <Bar 
                                                    dataKey="view_count" 
                                                    name="Görüntülenme Sayısı" 
                                                    fill="#8884d8" 
                                                    onClick={(data) => goToSearchTermDetail(data.search_query)}
                                                    cursor="pointer"
                                                />
                                            </BarChart>
                                        </ResponsiveContainer>
                                    </div>
                                </CardContent>
                            </Card>
                            
                            {/* Arama Terimleri Tablosu */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Arama Terimleri Detayı</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="overflow-x-auto">
                                        <table className="w-full text-sm">
                                            <thead>
                                                <tr className="border-b">
                                                    <th className="text-left py-3 px-4">Arama Terimi</th>
                                                    <th className="text-right py-3 px-4">Görüntülenme</th>
                                                    <th className="text-right py-3 px-4">Dönüşüm</th>
                                                    <th className="text-right py-3 px-4">Dönüşüm Oranı</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {searchTerms.map((term, index) => {
                                                    const conversionRate = term.view_count > 0 
                                                        ? ((term.conversion_count / term.view_count) * 100).toFixed(1) 
                                                        : 0;
                                                        
                                                    return (
                                                        <tr 
                                                            key={index} 
                                                            className="border-b hover:bg-gray-50 cursor-pointer"
                                                            onClick={() => goToSearchTermDetail(term.search_query)}
                                                        >
                                                            <td className="py-3 px-4">{term.search_query}</td>
                                                            <td className="text-right py-3 px-4">{term.view_count}</td>
                                                            <td className="text-right py-3 px-4">{term.conversion_count}</td>
                                                            <td className="text-right py-3 px-4">{conversionRate}%</td>
                                                        </tr>
                                                    );
                                                })}
                                            </tbody>
                                        </table>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>
                </Tabs>
            </div>
        </AdminLayout>
    );
}
