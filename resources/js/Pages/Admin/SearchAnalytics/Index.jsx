import React, { useState } from "react";
import { Head } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/Components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/Components/ui/tabs";
import {
    BarChart,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
    LineChart,
    Line,
    PieChart,
    Pie,
    Cell,
} from "recharts";

export default function Index({
    stats,
    popularSearchTerms,
    zeroResultSearchTerms,
    highestConversionSearchTerms,
    searchTrends,
    mostViewedProducts,
    mostPurchasedProducts,
}) {
    const [timeRange, setTimeRange] = useState(stats.days.toString());

    // Renk paleti
    const COLORS = [
        "#0088FE",
        "#00C49F",
        "#FFBB28",
        "#FF8042",
        "#8884d8",
        "#82ca9d",
        "#ffc658",
        "#8dd1e1",
        "#a4de6c",
        "#d0ed57",
    ];

    // Tarih aralığını değiştir
    const handleTimeRangeChange = (value) => {
        setTimeRange(value);
        window.location.href = `/admin/search-analytics?days=${value}`;
    };

    // Arama terimi detay sayfasına git
    const goToSearchTermDetail = (query) => {
        window.location.href = `/admin/search-analytics/term/${encodeURIComponent(
            query
        )}?days=${timeRange}`;
    };

    // Ürün detay sayfasına git
    const goToProductDetail = (id) => {
        window.location.href = `/admin/search-analytics/product/${id}?days=${timeRange}`;
    };

    // Tarih formatla
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString("tr-TR");
    };

    // Yüzde formatla
    const formatPercent = (value) => {
        return `${value}%`;
    };

    return (
        <AdminLayout>
            <Head title="Arama Analitiği" />

            <div className="container mx-auto py-6">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Arama Analitiği</h1>

                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">
                            Zaman Aralığı:
                        </span>
                        <select
                            value={timeRange}
                            onChange={(e) =>
                                handleTimeRangeChange(e.target.value)
                            }
                            className="border border-gray-300 rounded px-2 py-1 text-sm"
                        >
                            <option value="7">Son 7 Gün</option>
                            <option value="30">Son 30 Gün</option>
                            <option value="90">Son 90 Gün</option>
                            <option value="365">Son 1 Yıl</option>
                        </select>
                    </div>
                </div>

                {/* Özet İstatistikler */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">
                                Toplam Arama
                            </div>
                            <div className="text-3xl font-bold mt-2">
                                {stats.totalSearches}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">
                                Benzersiz Arama Terimi
                            </div>
                            <div className="text-3xl font-bold mt-2">
                                {stats.uniqueSearchTerms}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">
                                Sonuç Dönmeyen Arama
                            </div>
                            <div className="text-3xl font-bold mt-2">
                                {stats.zeroResultSearches}
                            </div>
                            <div className="text-sm text-gray-500 mt-1">
                                (
                                {stats.totalSearches > 0
                                    ? (
                                          (stats.zeroResultSearches /
                                              stats.totalSearches) *
                                          100
                                      ).toFixed(1)
                                    : 0}
                                %)
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">
                                Dönüşüm Oranı
                            </div>
                            <div className="text-3xl font-bold mt-2">
                                {stats.conversionRate}%
                            </div>
                            <div className="text-sm text-gray-500 mt-1">
                                {stats.totalConversions} / {stats.totalClicks}{" "}
                                tıklama
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <Tabs defaultValue="overview" className="mb-6">
                    <TabsList className="mb-4">
                        <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
                        <TabsTrigger value="search-terms">
                            Arama Terimleri
                        </TabsTrigger>
                        <TabsTrigger value="products">Ürünler</TabsTrigger>
                    </TabsList>

                    {/* Genel Bakış Sekmesi */}
                    <TabsContent value="overview">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Arama Trendi Grafiği */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Arama Trendi</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-80">
                                        <ResponsiveContainer
                                            width="100%"
                                            height="100%"
                                        >
                                            <LineChart
                                                data={searchTrends}
                                                margin={{
                                                    top: 5,
                                                    right: 30,
                                                    left: 20,
                                                    bottom: 5,
                                                }}
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis
                                                    dataKey="date"
                                                    tickFormatter={formatDate}
                                                />
                                                <YAxis />
                                                <Tooltip
                                                    formatter={(value) => [
                                                        value,
                                                        "Arama Sayısı",
                                                    ]}
                                                    labelFormatter={formatDate}
                                                />
                                                <Legend />
                                                <Line
                                                    type="monotone"
                                                    dataKey="search_count"
                                                    name="Arama Sayısı"
                                                    stroke="#8884d8"
                                                    activeDot={{ r: 8 }}
                                                />
                                            </LineChart>
                                        </ResponsiveContainer>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Popüler Arama Terimleri Grafiği */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>
                                        Popüler Arama Terimleri
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-80">
                                        <ResponsiveContainer
                                            width="100%"
                                            height="100%"
                                        >
                                            <BarChart
                                                data={popularSearchTerms}
                                                margin={{
                                                    top: 5,
                                                    right: 30,
                                                    left: 20,
                                                    bottom: 5,
                                                }}
                                                layout="vertical"
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis type="number" />
                                                <YAxis
                                                    dataKey="query"
                                                    type="category"
                                                    width={100}
                                                    tick={{ fontSize: 12 }}
                                                />
                                                <Tooltip
                                                    formatter={(value) => [
                                                        value,
                                                        "Arama Sayısı",
                                                    ]}
                                                />
                                                <Legend />
                                                <Bar
                                                    dataKey="search_count"
                                                    name="Arama Sayısı"
                                                    fill="#8884d8"
                                                    onClick={(data) =>
                                                        goToSearchTermDetail(
                                                            data.query
                                                        )
                                                    }
                                                    cursor="pointer"
                                                />
                                            </BarChart>
                                        </ResponsiveContainer>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    {/* Arama Terimleri Sekmesi */}
                    <TabsContent value="search-terms">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* En Yüksek Dönüşüm Oranına Sahip Arama Terimleri */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>
                                        En Yüksek Dönüşüm Oranına Sahip Arama
                                        Terimleri
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-80">
                                        <ResponsiveContainer
                                            width="100%"
                                            height="100%"
                                        >
                                            <BarChart
                                                data={
                                                    highestConversionSearchTerms
                                                }
                                                margin={{
                                                    top: 5,
                                                    right: 30,
                                                    left: 20,
                                                    bottom: 5,
                                                }}
                                                layout="vertical"
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis
                                                    type="number"
                                                    tickFormatter={
                                                        formatPercent
                                                    }
                                                />
                                                <YAxis
                                                    dataKey="query"
                                                    type="category"
                                                    width={100}
                                                    tick={{ fontSize: 12 }}
                                                />
                                                <Tooltip
                                                    formatter={(value) => [
                                                        `${value.toFixed(1)}%`,
                                                        "Dönüşüm Oranı",
                                                    ]}
                                                />
                                                <Legend />
                                                <Bar
                                                    dataKey="conversion_rate"
                                                    name="Dönüşüm Oranı"
                                                    fill="#00C49F"
                                                    onClick={(data) =>
                                                        goToSearchTermDetail(
                                                            data.query
                                                        )
                                                    }
                                                    cursor="pointer"
                                                />
                                            </BarChart>
                                        </ResponsiveContainer>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Sonuç Dönmeyen Arama Terimleri */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>
                                        Sonuç Dönmeyen Arama Terimleri
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-80">
                                        <ResponsiveContainer
                                            width="100%"
                                            height="100%"
                                        >
                                            <BarChart
                                                data={zeroResultSearchTerms}
                                                margin={{
                                                    top: 5,
                                                    right: 30,
                                                    left: 20,
                                                    bottom: 5,
                                                }}
                                                layout="vertical"
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis type="number" />
                                                <YAxis
                                                    dataKey="query"
                                                    type="category"
                                                    width={100}
                                                    tick={{ fontSize: 12 }}
                                                />
                                                <Tooltip
                                                    formatter={(value) => [
                                                        value,
                                                        "Arama Sayısı",
                                                    ]}
                                                />
                                                <Legend />
                                                <Bar
                                                    dataKey="search_count"
                                                    name="Arama Sayısı"
                                                    fill="#FF8042"
                                                    onClick={(data) =>
                                                        goToSearchTermDetail(
                                                            data.query
                                                        )
                                                    }
                                                    cursor="pointer"
                                                />
                                            </BarChart>
                                        </ResponsiveContainer>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Tüm Arama Terimleri Tablosu */}
                            <Card className="lg:col-span-2">
                                <CardHeader>
                                    <CardTitle>Tüm Arama Terimleri</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="overflow-x-auto">
                                        <table className="w-full text-sm">
                                            <thead>
                                                <tr className="border-b">
                                                    <th className="text-left py-3 px-4">
                                                        Arama Terimi
                                                    </th>
                                                    <th className="text-right py-3 px-4">
                                                        Arama Sayısı
                                                    </th>
                                                    <th className="text-right py-3 px-4">
                                                        Tıklama
                                                    </th>
                                                    <th className="text-right py-3 px-4">
                                                        Dönüşüm
                                                    </th>
                                                    <th className="text-right py-3 px-4">
                                                        Dönüşüm Oranı
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {popularSearchTerms.map(
                                                    (term, index) => (
                                                        <tr
                                                            key={index}
                                                            className="border-b hover:bg-gray-50 cursor-pointer"
                                                            onClick={() =>
                                                                goToSearchTermDetail(
                                                                    term.query
                                                                )
                                                            }
                                                        >
                                                            <td className="py-3 px-4">
                                                                {term.query}
                                                            </td>
                                                            <td className="text-right py-3 px-4">
                                                                {
                                                                    term.search_count
                                                                }
                                                            </td>
                                                            <td className="text-right py-3 px-4">
                                                                {term.total_clicks ||
                                                                    0}
                                                            </td>
                                                            <td className="text-right py-3 px-4">
                                                                {term.total_conversions ||
                                                                    0}
                                                            </td>
                                                            <td className="text-right py-3 px-4">
                                                                {term.total_clicks &&
                                                                term.total_clicks >
                                                                    0
                                                                    ? (
                                                                          (term.total_conversions /
                                                                              term.total_clicks) *
                                                                          100
                                                                      ).toFixed(
                                                                          1
                                                                      )
                                                                    : 0}
                                                                %
                                                            </td>
                                                        </tr>
                                                    )
                                                )}
                                            </tbody>
                                        </table>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    {/* Ürünler Sekmesi */}
                    <TabsContent value="products">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* En Çok Görüntülenen Ürünler */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>
                                        En Çok Görüntülenen Ürünler
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-80">
                                        <ResponsiveContainer
                                            width="100%"
                                            height="100%"
                                        >
                                            <BarChart
                                                data={mostViewedProducts}
                                                margin={{
                                                    top: 5,
                                                    right: 30,
                                                    left: 20,
                                                    bottom: 5,
                                                }}
                                                layout="vertical"
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis type="number" />
                                                <YAxis
                                                    dataKey="name"
                                                    type="category"
                                                    width={100}
                                                    tick={{ fontSize: 12 }}
                                                />
                                                <Tooltip
                                                    formatter={(value) => [
                                                        value,
                                                        "Görüntülenme Sayısı",
                                                    ]}
                                                />
                                                <Legend />
                                                <Bar
                                                    dataKey="view_count"
                                                    name="Görüntülenme Sayısı"
                                                    fill="#0088FE"
                                                    onClick={(data) =>
                                                        goToProductDetail(
                                                            data.id
                                                        )
                                                    }
                                                    cursor="pointer"
                                                />
                                            </BarChart>
                                        </ResponsiveContainer>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* En Çok Satın Alınan Ürünler */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>
                                        En Çok Satın Alınan Ürünler
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-80">
                                        <ResponsiveContainer
                                            width="100%"
                                            height="100%"
                                        >
                                            <BarChart
                                                data={mostPurchasedProducts}
                                                margin={{
                                                    top: 5,
                                                    right: 30,
                                                    left: 20,
                                                    bottom: 5,
                                                }}
                                                layout="vertical"
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis type="number" />
                                                <YAxis
                                                    dataKey="name"
                                                    type="category"
                                                    width={100}
                                                    tick={{ fontSize: 12 }}
                                                />
                                                <Tooltip
                                                    formatter={(value) => [
                                                        value,
                                                        "Satın Alma Sayısı",
                                                    ]}
                                                />
                                                <Legend />
                                                <Bar
                                                    dataKey="purchase_count"
                                                    name="Satın Alma Sayısı"
                                                    fill="#00C49F"
                                                    onClick={(data) =>
                                                        goToProductDetail(
                                                            data.id
                                                        )
                                                    }
                                                    cursor="pointer"
                                                />
                                            </BarChart>
                                        </ResponsiveContainer>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Tüm Ürünler Tablosu */}
                            <Card className="lg:col-span-2">
                                <CardHeader>
                                    <CardTitle>
                                        Arama Sonuçlarından Görüntülenen Ürünler
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="overflow-x-auto">
                                        <table className="w-full text-sm">
                                            <thead>
                                                <tr className="border-b">
                                                    <th className="text-left py-3 px-4">
                                                        Ürün Adı
                                                    </th>
                                                    <th className="text-left py-3 px-4">
                                                        Stok Kodu
                                                    </th>
                                                    <th className="text-right py-3 px-4">
                                                        Görüntülenme
                                                    </th>
                                                    <th className="text-right py-3 px-4">
                                                        Satın Alma
                                                    </th>
                                                    <th className="text-right py-3 px-4">
                                                        Dönüşüm Oranı
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {mostViewedProducts.map(
                                                    (product, index) => {
                                                        const purchaseData =
                                                            mostPurchasedProducts.find(
                                                                (p) =>
                                                                    p.id ===
                                                                    product.id
                                                            );
                                                        const purchaseCount =
                                                            purchaseData
                                                                ? purchaseData.purchase_count
                                                                : 0;
                                                        const conversionRate =
                                                            product.view_count &&
                                                            product.view_count >
                                                                0
                                                                ? (
                                                                      (purchaseCount /
                                                                          product.view_count) *
                                                                      100
                                                                  ).toFixed(1)
                                                                : 0;

                                                        return (
                                                            <tr
                                                                key={index}
                                                                className="border-b hover:bg-gray-50 cursor-pointer"
                                                                onClick={() =>
                                                                    goToProductDetail(
                                                                        product.id
                                                                    )
                                                                }
                                                            >
                                                                <td className="py-3 px-4">
                                                                    {
                                                                        product.name
                                                                    }
                                                                </td>
                                                                <td className="py-3 px-4">
                                                                    {
                                                                        product.stock_code
                                                                    }
                                                                </td>
                                                                <td className="text-right py-3 px-4">
                                                                    {
                                                                        product.view_count
                                                                    }
                                                                </td>
                                                                <td className="text-right py-3 px-4">
                                                                    {
                                                                        purchaseCount
                                                                    }
                                                                </td>
                                                                <td className="text-right py-3 px-4">
                                                                    {
                                                                        conversionRate
                                                                    }
                                                                    %
                                                                </td>
                                                            </tr>
                                                        );
                                                    }
                                                )}
                                            </tbody>
                                        </table>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>
                </Tabs>
            </div>
        </AdminLayout>
    );
}
