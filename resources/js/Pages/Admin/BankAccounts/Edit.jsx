import React from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import InputError from "@/Components/InputError";
import InputLabel from "@/Components/InputLabel";
import TextInput from "@/Components/TextInput";
import Checkbox from "@/Components/Checkbox";
import TextArea from "@/Components/TextArea";

export default function Edit({ bankAccount }) {
    const { data, setData, put, processing, errors } = useForm({
        bank_name: bankAccount.bank_name || "",
        account_name: bankAccount.account_name || "",
        account_number: bankAccount.account_number || "",
        iban: bankAccount.iban || "",
        branch_code: bankAccount.branch_code || "",
        branch_name: bankAccount.branch_name || "",
        swift_code: bankAccount.swift_code || "",
        description: bankAccount.description || "",
        is_active: bankAccount.is_active,
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route("admin.bank-accounts.update", bankAccount.id));
    };

    return (
        <AdminLayout>
            <Head title={`${bankAccount.bank_name} Düzenle`} />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-semibold text-gray-900">
                                    {bankAccount.bank_name} Düzenle
                                </h2>
                                <Link
                                    href={route("admin.bank-accounts.index")}
                                    className="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-400 active:bg-gray-500 focus:outline-none focus:border-gray-500 focus:ring ring-gray-300 disabled:opacity-25 transition"
                                >
                                    Geri Dön
                                </Link>
                            </div>

                            <form onSubmit={handleSubmit}>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <InputLabel htmlFor="bank_name" value="Banka Adı" />
                                        <TextInput
                                            id="bank_name"
                                            type="text"
                                            className="mt-1 block w-full"
                                            value={data.bank_name}
                                            onChange={(e) => setData("bank_name", e.target.value)}
                                            required
                                        />
                                        <InputError message={errors.bank_name} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="account_name" value="Hesap Adı" />
                                        <TextInput
                                            id="account_name"
                                            type="text"
                                            className="mt-1 block w-full"
                                            value={data.account_name}
                                            onChange={(e) => setData("account_name", e.target.value)}
                                            required
                                        />
                                        <InputError message={errors.account_name} className="mt-2" />
                                        <p className="text-sm text-gray-500 mt-1">
                                            Hesap sahibinin adı
                                        </p>
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="iban" value="IBAN" />
                                        <TextInput
                                            id="iban"
                                            type="text"
                                            className="mt-1 block w-full"
                                            value={data.iban}
                                            onChange={(e) => setData("iban", e.target.value)}
                                            required
                                        />
                                        <InputError message={errors.iban} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="account_number" value="Hesap Numarası" />
                                        <TextInput
                                            id="account_number"
                                            type="text"
                                            className="mt-1 block w-full"
                                            value={data.account_number}
                                            onChange={(e) => setData("account_number", e.target.value)}
                                        />
                                        <InputError message={errors.account_number} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="branch_name" value="Şube Adı" />
                                        <TextInput
                                            id="branch_name"
                                            type="text"
                                            className="mt-1 block w-full"
                                            value={data.branch_name}
                                            onChange={(e) => setData("branch_name", e.target.value)}
                                        />
                                        <InputError message={errors.branch_name} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="branch_code" value="Şube Kodu" />
                                        <TextInput
                                            id="branch_code"
                                            type="text"
                                            className="mt-1 block w-full"
                                            value={data.branch_code}
                                            onChange={(e) => setData("branch_code", e.target.value)}
                                        />
                                        <InputError message={errors.branch_code} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="swift_code" value="SWIFT Kodu" />
                                        <TextInput
                                            id="swift_code"
                                            type="text"
                                            className="mt-1 block w-full"
                                            value={data.swift_code}
                                            onChange={(e) => setData("swift_code", e.target.value)}
                                        />
                                        <InputError message={errors.swift_code} className="mt-2" />
                                    </div>

                                    <div className="md:col-span-2">
                                        <InputLabel htmlFor="description" value="Açıklama" />
                                        <TextArea
                                            id="description"
                                            className="mt-1 block w-full"
                                            value={data.description}
                                            onChange={(e) => setData("description", e.target.value)}
                                        />
                                        <InputError message={errors.description} className="mt-2" />
                                        <p className="text-sm text-gray-500 mt-1">
                                            Müşterilere gösterilecek ek bilgiler (opsiyonel)
                                        </p>
                                    </div>

                                    <div className="md:col-span-2">
                                        <label className="flex items-center">
                                            <Checkbox
                                                name="is_active"
                                                checked={data.is_active}
                                                onChange={(e) =>
                                                    setData("is_active", e.target.checked)
                                                }
                                            />
                                            <span className="ml-2 text-sm text-gray-600">
                                                Aktif
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div className="flex items-center justify-end mt-6">
                                    <button
                                        type="submit"
                                        className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition"
                                        disabled={processing}
                                    >
                                        Güncelle
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
