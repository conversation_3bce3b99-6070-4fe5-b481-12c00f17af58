import React, { useState } from "react";
import { Head, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";

export default function QueueMonitor({ stats }) {
    const [count, setCount] = useState(10);
    const { post, processing } = useForm();

    const handleSendTestJobs = () => {
        post(route("admin.queue-monitor.send-test-jobs"), {
            count: count,
        });
    };

    const handleClearFailedJobs = () => {
        post(route("admin.queue-monitor.clear-failed-jobs"));
    };

    return (
        <AdminLayout title="Queue Monitör">
            <Head title="Queue Monitör" />

            <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div className="p-6 bg-white border-b border-gray-200">
                    <h3 className="text-lg font-medium mb-4">
                        Queue Durumu
                    </h3>

                    {!stats.redis_available && (
                        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
                            <p className="font-bold">Uyarı</p>
                            <p>
                                Redis bağlantısı kurulamadı. Queue işlemleri
                                düzgün çalışmayabilir.
                            </p>
                        </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        {stats.redis_available &&
                            Object.entries(stats.queues).map(
                                ([queueName, queueStats]) => (
                                    <div
                                        key={queueName}
                                        className="bg-white p-4 rounded-lg shadow"
                                    >
                                        <h4 className="font-semibold text-gray-700 mb-2 capitalize">
                                            {queueName} Kuyruğu
                                        </h4>
                                        <div className="text-gray-600">
                                            <p>
                                                Bekleyen İşler:{" "}
                                                <span className="font-medium">
                                                    {queueStats.count}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                )
                            )}

                        <div className="bg-white p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-gray-700 mb-2">
                                Başarısız İşler
                            </h4>
                            <div className="text-gray-600">
                                <p>
                                    Toplam:{" "}
                                    <span className="font-medium">
                                        {stats.failed_count}
                                    </span>
                                </p>
                                {stats.failed_count > 0 && (
                                    <button
                                        onClick={handleClearFailedJobs}
                                        className="mt-2 px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
                                    >
                                        Temizle
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg shadow mb-6">
                        <h4 className="font-semibold text-gray-700 mb-2">
                            Test İşleri Gönder
                        </h4>
                        <div className="flex items-center">
                            <input
                                type="number"
                                min="1"
                                max="100"
                                value={count}
                                onChange={(e) => setCount(e.target.value)}
                                className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm mr-2"
                            />
                            <button
                                onClick={handleSendTestJobs}
                                disabled={processing}
                                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                            >
                                {processing
                                    ? "İşleniyor..."
                                    : "Test İşleri Gönder"}
                            </button>
                        </div>
                        <p className="text-sm text-gray-500 mt-2">
                            Bu işlem, belirtilen sayıda test sipariş ve e-posta
                            işi oluşturur ve kuyruğa ekler.
                        </p>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg shadow">
                        <h4 className="font-semibold text-gray-700 mb-2">
                            Queue Worker Çalıştırma
                        </h4>
                        <p className="text-gray-600 mb-2">
                            Queue worker'ı çalıştırmak için aşağıdaki komutu
                            kullanabilirsiniz:
                        </p>
                        <div className="bg-gray-800 text-white p-3 rounded font-mono text-sm">
                            php artisan queue:work redis --queue=default,emails,orders
                        </div>
                        <p className="text-sm text-gray-500 mt-2">
                            Not: Bu komutu ayrı bir terminal penceresinde
                            çalıştırmanız gerekir.
                        </p>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
