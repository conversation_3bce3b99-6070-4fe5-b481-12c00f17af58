import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import InputLabel from '@/Components/InputLabel';
import TextInput from '@/Components/TextInput';
import InputError from '@/Components/InputError';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import Checkbox from '@/Components/Checkbox';

export default function Edit({ template }) {
    const [showTestEmailForm, setShowTestEmailForm] = useState(false);
    const [testEmail, setTestEmail] = useState('');
    const [testEmailSending, setTestEmailSending] = useState(false);

    const { data, setData, post, put, processing, errors, reset } = useForm({
        subject: template.subject || '',
        body_html: template.body_html || '',
        body_text: template.body_text || '',
        is_active: template.is_active || false,
        description: template.description || '',
    });

    const { data: testData, setData: setTestData, post: postTest, processing: testProcessing, errors: testErrors, reset: resetTest } = useForm({
        email: '',
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route('admin.email-templates.update', template.id));
    };

    const handleTestSubmit = (e) => {
        e.preventDefault();
        setTestEmailSending(true);
        postTest(route('admin.email-templates.send-test', template.id), {
            onSuccess: () => {
                setTestEmailSending(false);
                setShowTestEmailForm(false);
                resetTest();
            },
            onError: () => {
                setTestEmailSending(false);
            }
        });
    };

    return (
        <AdminLayout title={`E-posta Şablonu Düzenle: ${template.name}`}>
            <Head title={`E-posta Şablonu Düzenle: ${template.name}`} />

            <div className="bg-white shadow-md rounded-lg overflow-hidden">
                <div className="p-6">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-xl font-semibold text-gray-800">E-posta Şablonu Düzenle</h2>
                        <div>
                            <SecondaryButton
                                onClick={() => setShowTestEmailForm(!showTestEmailForm)}
                                className="mr-2"
                            >
                                Test E-postası Gönder
                            </SecondaryButton>
                            <SecondaryButton
                                href={route('admin.email-templates.index')}
                                className="bg-gray-300 hover:bg-gray-400"
                            >
                                Geri Dön
                            </SecondaryButton>
                        </div>
                    </div>

                    {showTestEmailForm && (
                        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                            <h3 className="text-lg font-medium mb-2">Test E-postası Gönder</h3>
                            <form onSubmit={handleTestSubmit} className="flex items-end">
                                <div className="flex-1 mr-2">
                                    <InputLabel htmlFor="test_email" value="E-posta Adresi" />
                                    <TextInput
                                        id="test_email"
                                        type="email"
                                        name="email"
                                        value={testData.email}
                                        className="mt-1 block w-full"
                                        onChange={(e) => setTestData('email', e.target.value)}
                                        required
                                    />
                                    <InputError message={testErrors.email} className="mt-2" />
                                </div>
                                <PrimaryButton
                                    className="ml-4"
                                    disabled={testProcessing}
                                >
                                    {testProcessing ? 'Gönderiliyor...' : 'Gönder'}
                                </PrimaryButton>
                            </form>
                        </div>
                    )}

                    <form onSubmit={handleSubmit}>
                        <div className="mb-4">
                            <InputLabel htmlFor="name" value="Şablon Adı" />
                            <TextInput
                                id="name"
                                type="text"
                                name="name"
                                value={template.name}
                                className="mt-1 block w-full"
                                disabled
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel htmlFor="type" value="Şablon Türü" />
                            <TextInput
                                id="type"
                                type="text"
                                name="type"
                                value={template.type}
                                className="mt-1 block w-full"
                                disabled
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel htmlFor="subject" value="Konu" />
                            <TextInput
                                id="subject"
                                type="text"
                                name="subject"
                                value={data.subject}
                                className="mt-1 block w-full"
                                onChange={(e) => setData('subject', e.target.value)}
                                required
                            />
                            <InputError message={errors.subject} className="mt-2" />
                        </div>

                        <div className="mb-4">
                            <InputLabel htmlFor="body_html" value="HTML İçerik" />
                            <textarea
                                id="body_html"
                                name="body_html"
                                value={data.body_html}
                                className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                onChange={(e) => setData('body_html', e.target.value)}
                                rows={15}
                                required
                            />
                            <InputError message={errors.body_html} className="mt-2" />
                        </div>

                        <div className="mb-4">
                            <InputLabel htmlFor="body_text" value="Düz Metin İçerik" />
                            <textarea
                                id="body_text"
                                name="body_text"
                                value={data.body_text}
                                className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                onChange={(e) => setData('body_text', e.target.value)}
                                rows={10}
                            />
                            <InputError message={errors.body_text} className="mt-2" />
                        </div>

                        <div className="mb-4">
                            <InputLabel htmlFor="description" value="Açıklama" />
                            <textarea
                                id="description"
                                name="description"
                                value={data.description}
                                className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                onChange={(e) => setData('description', e.target.value)}
                                rows={3}
                            />
                            <InputError message={errors.description} className="mt-2" />
                        </div>

                        <div className="mb-4">
                            <div className="flex items-center">
                                <Checkbox
                                    id="is_active"
                                    name="is_active"
                                    checked={data.is_active}
                                    onChange={(e) => setData('is_active', e.target.checked)}
                                />
                                <InputLabel htmlFor="is_active" value="Aktif" className="ml-2" />
                            </div>
                            <InputError message={errors.is_active} className="mt-2" />
                        </div>

                        <div className="mb-4">
                            <h3 className="text-lg font-medium mb-2">Kullanılabilir Değişkenler</h3>
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <ul className="list-disc list-inside">
                                    {template.variables && template.variables.map((variable, index) => (
                                        <li key={index} className="text-sm text-gray-700">
                                            <code className="bg-gray-200 px-1 py-0.5 rounded">{'{{' + variable + '}}'}</code>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>

                        <div className="flex items-center justify-end mt-4">
                            <PrimaryButton
                                className="ml-4"
                                disabled={processing}
                            >
                                {processing ? 'Kaydediliyor...' : 'Kaydet'}
                            </PrimaryButton>
                        </div>
                    </form>
                </div>
            </div>
        </AdminLayout>
    );
}
