import React, { useState } from "react";
import { Head, Link, router } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import Pagination from "@/Components/Pagination";
import { toast } from "react-hot-toast";

export default function Index({ attributes, filters }) {
    const [search, setSearch] = useState(filters?.search || "");
    const [perPage, setPerPage] = useState(filters?.per_page || 10);
    const [expandedAttribute, setExpandedAttribute] = useState(null);

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route("admin.attributes.index"),
            { search, per_page: perPage },
            { preserveState: true }
        );
    };

    const handlePerPageChange = (e) => {
        const value = e.target.value;
        setPerPage(value);
        router.get(
            route("admin.attributes.index"),
            { search, per_page: value },
            { preserveState: true }
        );
    };

    const toggleAttributeDetails = (attributeId) => {
        if (expandedAttribute === attributeId) {
            setExpandedAttribute(null);
        } else {
            setExpandedAttribute(attributeId);
        }
    };

    const getAttributeTypeLabel = (type) => {
        switch (type) {
            case "select":
                return "Tek Seçim";
            case "multiple":
                return "Çoklu Seçim";
            case "text":
                return "Metin";
            case "color":
                return "Renk";
            case "size":
                return "Boyut";
            case "boolean":
                return "Evet/Hayır";
            default:
                return type;
        }
    };

    const handleDelete = (attribute) => {
        if (
            confirm(
                `${attribute.name} özelliğini silmek istediğinize emin misiniz?`
            )
        ) {
            router.delete(route("admin.attributes.destroy", attribute.id), {
                onSuccess: () => {
                    toast.success("Özellik başarıyla silindi");
                },
                onError: () => {
                    toast.error("Özellik silinirken bir hata oluştu");
                },
            });
        }
    };

    return (
        <AdminLayout title="Ürün Özellikleri">
            <Head title="Ürün Özellikleri" />

            <div className="mb-6 flex justify-between items-center">
                <div>
                    <h2 className="text-xl font-semibold">Ürün Özellikleri</h2>
                    <p className="text-gray-600">
                        Ürünlerde kullanılacak özellikleri yönetin
                    </p>
                </div>
                <Link
                    href={route("admin.attributes.create")}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                >
                    Yeni Özellik Ekle
                </Link>
            </div>

            <div className="bg-white shadow-md rounded-lg overflow-hidden">
                <div className="p-4 border-b">
                    <form onSubmit={handleSearch} className="flex gap-4">
                        <div className="flex-1">
                            <input
                                type="text"
                                placeholder="Özellik adı veya kodu ile ara..."
                                className="w-full px-4 py-2 border rounded-lg"
                                value={search}
                                onChange={(e) => setSearch(e.target.value)}
                            />
                        </div>
                        <button
                            type="submit"
                            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
                        >
                            Ara
                        </button>
                    </form>
                </div>

                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                        <tr>
                            <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                                Özellik Adı
                            </th>
                            <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                                Kod
                            </th>
                            <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                                Tür
                            </th>
                            <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                                Özellikler
                            </th>
                            <th
                                scope="col"
                                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                                İşlemler
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {attributes.data.map((attribute) => (
                            <React.Fragment key={attribute.id}>
                                <tr
                                    className={
                                        expandedAttribute === attribute.id
                                            ? "bg-blue-50"
                                            : "hover:bg-gray-50"
                                    }
                                >
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                            <button
                                                onClick={() =>
                                                    toggleAttributeDetails(
                                                        attribute.id
                                                    )
                                                }
                                                className="mr-2 text-gray-500 hover:text-gray-700"
                                            >
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className={`h-4 w-4 transition-transform ${
                                                        expandedAttribute ===
                                                        attribute.id
                                                            ? "transform rotate-90"
                                                            : ""
                                                    }`}
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M9 5l7 7-7 7"
                                                    />
                                                </svg>
                                            </button>
                                            <div className="text-sm font-medium text-gray-900">
                                                {attribute.name}
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-500">
                                            {attribute.code}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            {getAttributeTypeLabel(
                                                attribute.type
                                            )}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex space-x-2">
                                            {attribute.is_required && (
                                                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    Zorunlu
                                                </span>
                                            )}
                                            {attribute.is_filterable && (
                                                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    Filtrelenebilir
                                                </span>
                                            )}
                                            {attribute.is_variant && (
                                                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                                                    Varyant
                                                </span>
                                            )}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <Link
                                            href={route(
                                                "admin.attributes.edit",
                                                attribute.id
                                            )}
                                            className="text-indigo-600 hover:text-indigo-900 mr-3"
                                        >
                                            Düzenle
                                        </Link>
                                        <button
                                            onClick={() =>
                                                handleDelete(attribute)
                                            }
                                            className="text-red-600 hover:text-red-900"
                                        >
                                            Sil
                                        </button>
                                    </td>
                                </tr>
                                {expandedAttribute === attribute.id && (
                                    <tr>
                                        <td
                                            colSpan="5"
                                            className="px-6 py-4 bg-gray-50"
                                        >
                                            <div className="text-sm text-gray-900">
                                                <h4 className="font-medium mb-2">
                                                    Değerler
                                                </h4>
                                                {attribute.values &&
                                                attribute.values.length > 0 ? (
                                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                                                        {[...attribute.values]
                                                            .sort(
                                                                (a, b) =>
                                                                    a.position -
                                                                    b.position
                                                            )
                                                            .map((value) => (
                                                                <div
                                                                    key={
                                                                        value.id
                                                                    }
                                                                    className="bg-white p-2 rounded border"
                                                                >
                                                                    <div className="font-medium">
                                                                        {value.label ||
                                                                            value.value}
                                                                    </div>
                                                                    {value.label !==
                                                                        value.value && (
                                                                        <div className="text-xs text-gray-500">
                                                                            {
                                                                                value.value
                                                                            }
                                                                        </div>
                                                                    )}
                                                                    <div className="text-xs text-gray-400 mt-1">
                                                                        Sıra:{" "}
                                                                        {
                                                                            value.position
                                                                        }
                                                                    </div>
                                                                </div>
                                                            ))}
                                                    </div>
                                                ) : (
                                                    <p className="text-gray-500 italic">
                                                        Bu özellik için
                                                        tanımlanmış değer
                                                        bulunmuyor.
                                                    </p>
                                                )}
                                            </div>
                                        </td>
                                    </tr>
                                )}
                            </React.Fragment>
                        ))}

                        {attributes.data.length === 0 && (
                            <tr>
                                <td
                                    colSpan="5"
                                    className="px-6 py-4 text-center text-gray-500"
                                >
                                    Özellik bulunamadı
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>

                <div className="px-6 py-4 border-t">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center">
                            <span className="mr-2 text-sm text-gray-600">
                                Sayfa başına:
                            </span>
                            <select
                                className="border rounded px-2 py-1"
                                value={perPage}
                                onChange={handlePerPageChange}
                            >
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <Pagination links={attributes.links} />
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
