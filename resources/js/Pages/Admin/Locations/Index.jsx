import React, { useState, useEffect } from 'react';
import { Head, useForm, usePage } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import { toast } from 'react-toastify';

export default function LocationsIndex({ stats, isDataImported }) {
    const [loading, setLoading] = useState(false);
    const [importing, setImporting] = useState(false);

    const { post, processing } = useForm();
    const { flash } = usePage().props;

    // Flash mesajlarını göster
    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
        if (flash.error) {
            toast.error(flash.error);
        }
    }, [flash]);

    const refreshCache = () => {
        if (confirm('Konum verilerini önbelleğe almak istediğinize emin misiniz?')) {
            setLoading(true);
            post(route('admin.locations.refresh-cache'), {
                preserveScroll: true,
                onFinish: () => {
                    setLoading(false);
                }
            });
        }
    };

    const importData = () => {
        if (confirm('Konum verilerini import etmek istediğinize emin misiniz? Bu işlem birkaç dakika sürebilir.')) {
            setImporting(true);
            post(route('admin.locations.import-data'), {
                preserveScroll: true,
                onFinish: () => {
                    setImporting(false);
                }
            });
        }
    };

    return (
        <AdminLayout>
            <Head title="Konum Yönetimi" />

            <div className="container mx-auto py-6">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-semibold text-gray-900">Konum Yönetimi</h1>
                </div>

                <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 className="text-lg font-semibold mb-4">Konum Verileri İstatistikleri</h2>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <h3 className="text-sm font-medium text-blue-800">Toplam Ülke</h3>
                            <p className="text-2xl font-bold text-blue-600">{stats.countries}</p>
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                            <h3 className="text-sm font-medium text-green-800">Türkiye İlleri</h3>
                            <p className="text-2xl font-bold text-green-600">{stats.turkey_states}</p>
                        </div>
                    </div>

                    {!isDataImported && (
                        <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-6">
                            <h3 className="text-sm font-medium text-red-800 mb-2">Uyarı</h3>
                            <p className="text-sm text-red-700">
                                Konum verileri henüz sisteme aktarılmamış. Lütfen aşağıdaki butona tıklayarak konum verilerini import edin.
                                Bu işlem ülkeler ve Türkiye'nin il/ilçe bilgilerini sisteme aktaracaktır.
                            </p>
                        </div>
                    )}

                    {isDataImported && (
                        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-6">
                            <h3 className="text-sm font-medium text-yellow-800 mb-2">Bilgi</h3>
                            <p className="text-sm text-yellow-700">
                                Konum verileri, ülke, il ve ilçe bilgilerini içerir. Bu veriler, adres formlarında ve diğer konum tabanlı işlemlerde kullanılır.
                                Veriler önbelleğe alınarak performans artırılır. Verilerde bir değişiklik olduğunda veya önbellek temizlendiğinde, verileri yeniden önbelleğe almanız gerekir.
                            </p>
                        </div>
                    )}

                    <div className="flex justify-end space-x-3">
                        {!isDataImported ? (
                            <button
                                type="button"
                                onClick={importData}
                                disabled={processing || importing}
                                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {processing || importing ? (
                                    <span>Import Ediliyor...</span>
                                ) : (
                                    <span>Konum Verilerini Import Et</span>
                                )}
                            </button>
                        ) : (
                            <button
                                type="button"
                                onClick={refreshCache}
                                disabled={processing || loading}
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {processing || loading ? (
                                    <span>İşleniyor...</span>
                                ) : (
                                    <span>Konum Verilerini Önbelleğe Al</span>
                                )}
                            </button>
                        )}
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-md p-6">
                    <h2 className="text-lg font-semibold mb-4">Konum Verileri Hakkında</h2>

                    <div className="prose max-w-none">
                        <p>
                            Bu sistem, <a href="https://github.com/dr5hn/countries-states-cities-database" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">dr5hn/countries-states-cities-database</a> projesinden alınan verileri kullanmaktadır.
                            Veriler, dünya genelindeki ülkeler, eyaletler/iller ve şehirler/ilçeler için kapsamlı bilgiler içerir.
                        </p>

                        <h3 className="text-md font-semibold mt-4">Veri Kaynağı</h3>
                        <ul className="list-disc pl-5">
                            <li>Dünya genelinde 250+ ülke</li>
                            <li>5.000+ eyalet/il</li>
                            <li>100.000+ şehir/ilçe</li>
                            <li>Ülke bayrakları, telefon kodları, para birimleri</li>
                            <li>Çoklu dil desteği</li>
                        </ul>

                        <h3 className="text-md font-semibold mt-4">Önbellek Kullanımı</h3>
                        <p>
                            Konum verileri, performansı artırmak için önbelleğe alınır. Veriler, uygulama başlatıldığında otomatik olarak kontrol edilir ve gerekirse önbelleğe alınır.
                            Ayrıca, deploy sırasında veya manuel olarak bu sayfadan da önbelleğe alabilirsiniz.
                        </p>

                        <h3 className="text-md font-semibold mt-4">Veri Güncelleme</h3>
                        <p>
                            Konum verileri çok sık değişmediği için, veriler veritabanında saklanır ve önbelleğe alınır.
                            Verileri güncellemek için, yeni verileri veritabanına aktarmanız ve ardından önbelleği yenilemeniz gerekir.
                        </p>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
