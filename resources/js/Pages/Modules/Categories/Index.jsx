import React, { useState, useEffect } from "react";
import { Head } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import categoryApi from "../../Services/modules/categoryApi";
import CategoryForm from "./Components/CategoryForm";
import { toast } from "react-hot-toast";

export default function Index() {
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showForm, setShowForm] = useState(false);
    const [currentCategory, setCurrentCategory] = useState(null);
    const [confirmDelete, setConfirmDelete] = useState(null);

    // Kategorileri yükle
    const fetchCategories = async () => {
        setLoading(true);
        try {
            const response = await categoryApi.getAll();
            setCategories(response.data.data);
        } catch (error) {
            console.error("Kategoriler yüklenirken hata oluştu:", error);
            toast.error("Kategoriler yüklenirken bir hata oluştu.");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchCategories();
    }, []);

    // Kategori oluştur veya güncelle
    const handleSubmit = async (formData) => {
        try {
            if (currentCategory) {
                // Güncelleme
                await categoryApi.update(currentCategory.id, formData);
                toast.success("Kategori başarıyla güncellendi.");
            } else {
                // Oluşturma
                await categoryApi.create(formData);
                toast.success("Kategori başarıyla oluşturuldu.");
            }
            setShowForm(false);
            setCurrentCategory(null);
            fetchCategories();
        } catch (error) {
            console.error("Kategori kaydedilirken hata oluştu:", error);
            toast.error("Kategori kaydedilirken bir hata oluştu.");
            throw error; // Hata formda yakalanabilsin
        }
    };

    // Kategori düzenleme formunu aç
    const handleEdit = (category) => {
        setCurrentCategory(category);
        setShowForm(true);
    };

    // Kategori silme işlemi
    const handleDelete = async (id) => {
        try {
            await categoryApi.delete(id);
            toast.success("Kategori başarıyla silindi.");
            fetchCategories();
            setConfirmDelete(null);
        } catch (error) {
            console.error("Kategori silinirken hata oluştu:", error);
            if (error.response && error.response.data && error.response.data.errors) {
                const errorMessage = Object.values(error.response.data.errors)[0][0];
                toast.error(errorMessage);
            } else {
                toast.error("Kategori silinirken bir hata oluştu.");
            }
        }
    };

    return (
        <AdminLayout>
            <Head title="Kategoriler" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h1 className="text-2xl font-semibold text-gray-900">
                                    Kategoriler
                                </h1>
                                <button
                                    onClick={() => {
                                        setCurrentCategory(null);
                                        setShowForm(true);
                                    }}
                                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                                >
                                    Yeni Kategori
                                </button>
                            </div>

                            {/* Kategori Formu */}
                            {showForm && (
                                <div className="mb-6 p-4 border rounded-md bg-gray-50">
                                    <h2 className="text-lg font-medium mb-4">
                                        {currentCategory
                                            ? "Kategori Düzenle"
                                            : "Yeni Kategori"}
                                    </h2>
                                    <CategoryForm
                                        category={currentCategory}
                                        onSubmit={handleSubmit}
                                        onCancel={() => {
                                            setShowForm(false);
                                            setCurrentCategory(null);
                                        }}
                                    />
                                </div>
                            )}

                            {/* Kategori Listesi */}
                            {loading ? (
                                <div className="text-center py-4">
                                    <p>Yükleniyor...</p>
                                </div>
                            ) : categories.length === 0 ? (
                                <div className="text-center py-4">
                                    <p>Henüz kategori bulunmuyor.</p>
                                </div>
                            ) : (
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    ID
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Ad
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Üst Kategori
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Durum
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    İşlemler
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {categories.map((category) => (
                                                <tr key={category.id}>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {category.id}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                        {category.name}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {category.parent
                                                            ? category.parent.name
                                                            : "-"}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        <span
                                                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                                category.status
                                                                    ? "bg-green-100 text-green-800"
                                                                    : "bg-red-100 text-red-800"
                                                            }`}
                                                        >
                                                            {category.status
                                                                ? "Aktif"
                                                                : "Pasif"}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                        <button
                                                            onClick={() =>
                                                                handleEdit(
                                                                    category
                                                                )
                                                            }
                                                            className="text-indigo-600 hover:text-indigo-900 mr-4"
                                                        >
                                                            Düzenle
                                                        </button>
                                                        <button
                                                            onClick={() =>
                                                                setConfirmDelete(
                                                                    category.id
                                                                )
                                                            }
                                                            className="text-red-600 hover:text-red-900"
                                                        >
                                                            Sil
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}

                            {/* Silme Onay Modalı */}
                            {confirmDelete && (
                                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4">
                                    <div className="bg-white rounded-lg p-6 max-w-md mx-auto">
                                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                                            Kategoriyi Sil
                                        </h3>
                                        <p className="mb-4">
                                            Bu kategoriyi silmek istediğinizden
                                            emin misiniz? Bu işlem geri alınamaz.
                                        </p>
                                        <div className="flex justify-end space-x-3">
                                            <button
                                                onClick={() =>
                                                    setConfirmDelete(null)
                                                }
                                                className="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400"
                                            >
                                                İptal
                                            </button>
                                            <button
                                                onClick={() =>
                                                    handleDelete(confirmDelete)
                                                }
                                                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                                            >
                                                Sil
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
