import React, { useState, useEffect } from "react";
import { Head } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import productApi from "../../Services/modules/productApi";
import ProductForm from "./Components/ProductForm";
import { toast } from "react-hot-toast";

export default function Index() {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showForm, setShowForm] = useState(false);
    const [currentProduct, setCurrentProduct] = useState(null);
    const [confirmDelete, setConfirmDelete] = useState(null);

    // Ürünleri yükle
    const fetchProducts = async () => {
        setLoading(true);
        try {
            const response = await productApi.getAll();
            setProducts(response.data.data);
        } catch (error) {
            console.error("Ürünler yüklenirken hata oluştu:", error);
            toast.error("Ürünler yüklenirken bir hata oluştu.");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchProducts();
    }, []);

    // Ürün oluştur veya güncelle
    const handleSubmit = async (formData) => {
        try {
            if (currentProduct) {
                // Güncelleme
                await productApi.update(currentProduct.id, formData);
                toast.success("Ürün başarıyla güncellendi.");
            } else {
                // Oluşturma
                await productApi.create(formData);
                toast.success("Ürün başarıyla oluşturuldu.");
            }
            setShowForm(false);
            setCurrentProduct(null);
            fetchProducts();
        } catch (error) {
            console.error("Ürün kaydedilirken hata oluştu:", error);
            toast.error("Ürün kaydedilirken bir hata oluştu.");
            throw error; // Hata formda yakalanabilsin
        }
    };

    // Ürün düzenleme formunu aç
    const handleEdit = (product) => {
        setCurrentProduct(product);
        setShowForm(true);
    };

    // Ürün silme işlemi
    const handleDelete = async (id) => {
        try {
            await productApi.delete(id);
            toast.success("Ürün başarıyla silindi.");
            fetchProducts();
            setConfirmDelete(null);
        } catch (error) {
            console.error("Ürün silinirken hata oluştu:", error);
            toast.error("Ürün silinirken bir hata oluştu.");
        }
    };

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    return (
        <AdminLayout>
            <Head title="Ürünler" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h1 className="text-2xl font-semibold text-gray-900">
                                    Ürünler
                                </h1>
                                <button
                                    onClick={() => {
                                        setCurrentProduct(null);
                                        setShowForm(true);
                                    }}
                                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                                >
                                    Yeni Ürün
                                </button>
                            </div>

                            {/* Ürün Formu */}
                            {showForm && (
                                <div className="mb-6 p-4 border rounded-md bg-gray-50">
                                    <h2 className="text-lg font-medium mb-4">
                                        {currentProduct
                                            ? "Ürün Düzenle"
                                            : "Yeni Ürün"}
                                    </h2>
                                    <ProductForm
                                        product={currentProduct}
                                        onSubmit={handleSubmit}
                                        onCancel={() => {
                                            setShowForm(false);
                                            setCurrentProduct(null);
                                        }}
                                    />
                                </div>
                            )}

                            {/* Ürün Listesi */}
                            {loading ? (
                                <div className="text-center py-4">
                                    <p>Yükleniyor...</p>
                                </div>
                            ) : products.length === 0 ? (
                                <div className="text-center py-4">
                                    <p>Henüz ürün bulunmuyor.</p>
                                </div>
                            ) : (
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    ID
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Ad
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Kategori
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Fiyat
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Stok
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Durum
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    İşlemler
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {products.map((product) => (
                                                <tr key={product.id}>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {product.id}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                        {product.name}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {product.category
                                                            ? product.category
                                                                  .name
                                                            : "-"}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {formatPrice(
                                                            product.price
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {product.stock}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        <span
                                                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                                product.status
                                                                    ? "bg-green-100 text-green-800"
                                                                    : "bg-red-100 text-red-800"
                                                            }`}
                                                        >
                                                            {product.status
                                                                ? "Aktif"
                                                                : "Pasif"}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                        <button
                                                            onClick={() =>
                                                                handleEdit(
                                                                    product
                                                                )
                                                            }
                                                            className="text-indigo-600 hover:text-indigo-900 mr-4"
                                                        >
                                                            Düzenle
                                                        </button>
                                                        <button
                                                            onClick={() =>
                                                                setConfirmDelete(
                                                                    product.id
                                                                )
                                                            }
                                                            className="text-red-600 hover:text-red-900"
                                                        >
                                                            Sil
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}

                            {/* Silme Onay Modalı */}
                            {confirmDelete && (
                                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4">
                                    <div className="bg-white rounded-lg p-6 max-w-md mx-auto">
                                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                                            Ürünü Sil
                                        </h3>
                                        <p className="mb-4">
                                            Bu ürünü silmek istediğinizden emin
                                            misiniz? Bu işlem geri alınamaz.
                                        </p>
                                        <div className="flex justify-end space-x-3">
                                            <button
                                                onClick={() =>
                                                    setConfirmDelete(null)
                                                }
                                                className="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400"
                                            >
                                                İptal
                                            </button>
                                            <button
                                                onClick={() =>
                                                    handleDelete(confirmDelete)
                                                }
                                                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                                            >
                                                Sil
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
