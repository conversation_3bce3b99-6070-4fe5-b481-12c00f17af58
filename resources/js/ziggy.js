const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"debugbar.openhandler":{"uri":"_debugbar\/open","methods":["GET","HEAD"]},"debugbar.clockwork":{"uri":"_debugbar\/clockwork\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"debugbar.assets.css":{"uri":"_debugbar\/assets\/stylesheets","methods":["GET","HEAD"]},"debugbar.assets.js":{"uri":"_debugbar\/assets\/javascript","methods":["GET","HEAD"]},"debugbar.cache.delete":{"uri":"_debugbar\/cache\/{key}\/{tags?}","methods":["DELETE"],"parameters":["key","tags"]},"debugbar.queries.explain":{"uri":"_debugbar\/queries\/explain","methods":["POST"]},"categories.index":{"uri":"categories","methods":["GET","HEAD"]},"categories.store":{"uri":"api\/categories","methods":["POST"]},"categories.show":{"uri":"api\/categories\/{category}","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"id"}},"categories.update":{"uri":"api\/categories\/{category}","methods":["PUT","PATCH"],"parameters":["category"],"bindings":{"category":"id"}},"categories.destroy":{"uri":"api\/categories\/{category}","methods":["DELETE"],"parameters":["category"],"bindings":{"category":"id"}},"products.index":{"uri":"api\/v1\/products","methods":["GET","HEAD"]},"products.store":{"uri":"api\/products","methods":["POST"]},"products.show":{"uri":"api\/products\/{product}","methods":["GET","HEAD"],"parameters":["product"],"bindings":{"product":"id"}},"products.update":{"uri":"api\/products\/{product}","methods":["PUT","PATCH"],"parameters":["product"],"bindings":{"product":"id"}},"products.destroy":{"uri":"api\/products\/{product}","methods":["DELETE"],"parameters":["product"],"bindings":{"product":"id"}},"home":{"uri":"\/","methods":["GET","HEAD"]},"frontend.products.index":{"uri":"products","methods":["GET","HEAD"]},"frontend.products.filters":{"uri":"products\/filtreler\/{filters}","methods":["GET","HEAD"],"parameters":["filters"]},"frontend.products.show":{"uri":"products\/{id}","methods":["GET","HEAD"],"wheres":{"id":"[0-9]+"},"parameters":["id"]},"categories.products":{"uri":"categories\/{category}\/products","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"id"}},"search.index":{"uri":"search","methods":["GET","HEAD"]},"search.autocomplete":{"uri":"api\/search\/autocomplete","methods":["GET","HEAD"]},"login":{"uri":"admin\/login","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"profile":{"uri":"profile","methods":["GET","HEAD"]},"profile.update":{"uri":"profile","methods":["PUT"]},"customer.login":{"uri":"login","methods":["GET","HEAD"]},"customer.register":{"uri":"register","methods":["GET","HEAD"]},"customer.dashboard":{"uri":"customer\/dashboard","methods":["GET","HEAD"]},"customer.profile":{"uri":"customer\/profile","methods":["GET","HEAD"]},"customer.profile.update":{"uri":"customer\/profile","methods":["PUT"]},"customer.orders":{"uri":"customer\/orders","methods":["GET","HEAD"]},"customer.orders.show":{"uri":"customer\/orders\/{orderNumber}","methods":["GET","HEAD"],"parameters":["orderNumber"]},"customer.addresses":{"uri":"customer\/addresses","methods":["GET","HEAD"]},"customer.addresses.create":{"uri":"customer\/addresses\/create","methods":["GET","HEAD"]},"customer.addresses.store":{"uri":"customer\/addresses","methods":["POST"]},"customer.addresses.edit":{"uri":"customer\/addresses\/{address}\/edit","methods":["GET","HEAD"],"parameters":["address"],"bindings":{"address":"id"}},"customer.addresses.update":{"uri":"customer\/addresses\/{address}","methods":["PUT"],"parameters":["address"],"bindings":{"address":"id"}},"customer.addresses.destroy":{"uri":"customer\/addresses\/{address}","methods":["DELETE"],"parameters":["address"],"bindings":{"address":"id"}},"customer.favorites":{"uri":"customer\/favorites","methods":["GET","HEAD"]},"customer.favorites.destroy":{"uri":"customer\/favorites\/{favorite}","methods":["DELETE"],"parameters":["favorite"],"bindings":{"favorite":"id"}},"customer.logout":{"uri":"customer\/logout","methods":["POST"]},"admin.shipping.zones.index":{"uri":"admin\/shipping\/zones","methods":["GET","HEAD"]},"admin.shipping.zones.create":{"uri":"admin\/shipping\/zones\/create","methods":["GET","HEAD"]},"admin.shipping.zones.store":{"uri":"admin\/shipping\/zones","methods":["POST"]},"admin.shipping.zones.edit":{"uri":"admin\/shipping\/zones\/{zone}\/edit","methods":["GET","HEAD"],"parameters":["zone"],"bindings":{"zone":"id"}},"admin.shipping.zones.update":{"uri":"admin\/shipping\/zones\/{zone}","methods":["PUT"],"parameters":["zone"],"bindings":{"zone":"id"}},"admin.shipping.zones.destroy":{"uri":"admin\/shipping\/zones\/{zone}","methods":["DELETE"],"parameters":["zone"],"bindings":{"zone":"id"}},"admin.shipping.zones.locations":{"uri":"admin\/shipping\/zones\/{zone}\/locations","methods":["GET","HEAD"],"parameters":["zone"],"bindings":{"zone":"id"}},"admin.shipping.zones.locations.add":{"uri":"admin\/shipping\/zones\/{zone}\/locations","methods":["POST"],"parameters":["zone"],"bindings":{"zone":"id"}},"admin.shipping.zones.locations.remove":{"uri":"admin\/shipping\/zones\/{zone}\/locations\/{location}","methods":["DELETE"],"parameters":["zone","location"],"bindings":{"zone":"id","location":"id"}},"admin.shipping.zones.methods":{"uri":"admin\/shipping\/zones\/{zone}\/methods","methods":["GET","HEAD"],"parameters":["zone"],"bindings":{"zone":"id"}},"admin.shipping.zones.methods.add":{"uri":"admin\/shipping\/zones\/{zone}\/methods","methods":["POST"],"parameters":["zone"],"bindings":{"zone":"id"}},"admin.shipping.zones.methods.edit":{"uri":"admin\/shipping\/zones\/{zone}\/methods\/{method}\/edit","methods":["GET","HEAD"],"parameters":["zone","method"],"bindings":{"zone":"id","method":"id"}},"admin.shipping.zones.methods.update":{"uri":"admin\/shipping\/zones\/{zone}\/methods\/{method}","methods":["PUT"],"parameters":["zone","method"],"bindings":{"zone":"id","method":"id"}},"admin.shipping.zones.methods.remove":{"uri":"admin\/shipping\/zones\/{zone}\/methods\/{method}","methods":["DELETE"],"parameters":["zone","method"],"bindings":{"zone":"id","method":"id"}},"admin.shipping.methods.index":{"uri":"admin\/shipping\/methods","methods":["GET","HEAD"]},"admin.shipping.methods.create":{"uri":"admin\/shipping\/methods\/create","methods":["GET","HEAD"]},"admin.shipping.methods.store":{"uri":"admin\/shipping\/methods","methods":["POST"]},"admin.shipping.methods.edit":{"uri":"admin\/shipping\/methods\/{method}\/edit","methods":["GET","HEAD"],"parameters":["method"],"bindings":{"method":"id"}},"admin.shipping.methods.update":{"uri":"admin\/shipping\/methods\/{method}","methods":["PUT"],"parameters":["method"],"bindings":{"method":"id"}},"admin.shipping.methods.destroy":{"uri":"admin\/shipping\/methods\/{method}","methods":["DELETE"],"parameters":["method"],"bindings":{"method":"id"}},"admin.shipping.companies.index":{"uri":"admin\/shipping\/companies","methods":["GET","HEAD"]},"admin.shipping.companies.create":{"uri":"admin\/shipping\/companies\/create","methods":["GET","HEAD"]},"admin.shipping.companies.store":{"uri":"admin\/shipping\/companies","methods":["POST"]},"admin.shipping.companies.edit":{"uri":"admin\/shipping\/companies\/{company}\/edit","methods":["GET","HEAD"],"parameters":["company"]},"admin.shipping.companies.update":{"uri":"admin\/shipping\/companies\/{company}","methods":["PUT"],"parameters":["company"]},"admin.shipping.companies.destroy":{"uri":"admin\/shipping\/companies\/{company}","methods":["DELETE"],"parameters":["company"]},"admin.dashboard":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"admin.products.index":{"uri":"admin\/products","methods":["GET","HEAD"]},"admin.products.create":{"uri":"admin\/products\/create","methods":["GET","HEAD"]},"admin.products.store":{"uri":"admin\/products","methods":["POST"]},"admin.products.edit":{"uri":"admin\/products\/{product}\/edit","methods":["GET","HEAD"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.update":{"uri":"admin\/products\/{product}","methods":["PUT"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.destroy":{"uri":"admin\/products\/{product}","methods":["DELETE"],"parameters":["product"],"bindings":{"product":"id"}},"admin.categories.index":{"uri":"admin\/categories","methods":["GET","HEAD"]},"admin.orders.index":{"uri":"admin\/orders","methods":["GET","HEAD"]},"admin.orders.show":{"uri":"admin\/orders\/{order}","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.edit":{"uri":"admin\/orders\/{order}\/edit","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.update":{"uri":"admin\/orders\/{order}","methods":["PUT"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.destroy":{"uri":"admin\/orders\/{order}","methods":["DELETE"],"parameters":["order"],"bindings":{"order":"id"}},"admin.users.index":{"uri":"admin\/users","methods":["GET","HEAD"]},"admin.users.create":{"uri":"admin\/users\/create","methods":["GET","HEAD"]},"admin.users.store":{"uri":"admin\/users","methods":["POST"]},"admin.users.show":{"uri":"admin\/users\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.edit":{"uri":"admin\/users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.update":{"uri":"admin\/users\/{user}","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.destroy":{"uri":"admin\/users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"admin.roles.index":{"uri":"admin\/roles","methods":["GET","HEAD"]},"admin.roles.create":{"uri":"admin\/roles\/create","methods":["GET","HEAD"]},"admin.roles.store":{"uri":"admin\/roles","methods":["POST"]},"admin.roles.show":{"uri":"admin\/roles\/{role}","methods":["GET","HEAD"],"parameters":["role"],"bindings":{"role":"id"}},"admin.roles.edit":{"uri":"admin\/roles\/{role}\/edit","methods":["GET","HEAD"],"parameters":["role"],"bindings":{"role":"id"}},"admin.roles.update":{"uri":"admin\/roles\/{role}","methods":["PUT"],"parameters":["role"],"bindings":{"role":"id"}},"admin.roles.destroy":{"uri":"admin\/roles\/{role}","methods":["DELETE"],"parameters":["role"],"bindings":{"role":"id"}},"admin.coupons.index":{"uri":"admin\/coupons","methods":["GET","HEAD"]},"admin.coupons.create":{"uri":"admin\/coupons\/create","methods":["GET","HEAD"]},"admin.coupons.store":{"uri":"admin\/coupons","methods":["POST"]},"admin.coupons.show":{"uri":"admin\/coupons\/{coupon}","methods":["GET","HEAD"],"parameters":["coupon"],"bindings":{"coupon":"id"}},"admin.coupons.edit":{"uri":"admin\/coupons\/{coupon}\/edit","methods":["GET","HEAD"],"parameters":["coupon"],"bindings":{"coupon":"id"}},"admin.coupons.update":{"uri":"admin\/coupons\/{coupon}","methods":["PUT"],"parameters":["coupon"],"bindings":{"coupon":"id"}},"admin.coupons.destroy":{"uri":"admin\/coupons\/{coupon}","methods":["DELETE"],"parameters":["coupon"],"bindings":{"coupon":"id"}},"admin.modules.index":{"uri":"admin\/modules","methods":["GET","HEAD"]},"admin.modules.update":{"uri":"admin\/modules","methods":["POST"]},"admin.attributes.index":{"uri":"admin\/attributes","methods":["GET","HEAD"]},"admin.attributes.create":{"uri":"admin\/attributes\/create","methods":["GET","HEAD"]},"admin.attributes.store":{"uri":"admin\/attributes","methods":["POST"]},"admin.attributes.show":{"uri":"admin\/attributes\/{attribute}","methods":["GET","HEAD"],"parameters":["attribute"],"bindings":{"attribute":"id"}},"admin.attributes.edit":{"uri":"admin\/attributes\/{attribute}\/edit","methods":["GET","HEAD"],"parameters":["attribute"],"bindings":{"attribute":"id"}},"admin.attributes.update":{"uri":"admin\/attributes\/{attribute}","methods":["PUT"],"parameters":["attribute"],"bindings":{"attribute":"id"}},"admin.attributes.destroy":{"uri":"admin\/attributes\/{attribute}","methods":["DELETE"],"parameters":["attribute"],"bindings":{"attribute":"id"}},"admin.products.variants.index":{"uri":"admin\/products\/{product}\/variants","methods":["GET","HEAD"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.variants.create":{"uri":"admin\/products\/{product}\/variants\/create","methods":["GET","HEAD"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.variants.store":{"uri":"admin\/products\/{product}\/variants","methods":["POST"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.variants.edit":{"uri":"admin\/products\/{product}\/variants\/{variant}\/edit","methods":["GET","HEAD"],"parameters":["product","variant"],"bindings":{"product":"id","variant":"id"}},"admin.products.variants.update":{"uri":"admin\/products\/{product}\/variants\/{variant}","methods":["PUT"],"parameters":["product","variant"],"bindings":{"product":"id","variant":"id"}},"admin.products.variants.stock":{"uri":"admin\/products\/{product}\/variants\/{variant}\/stock","methods":["PUT"],"parameters":["product","variant"],"bindings":{"product":"id","variant":"id"}},"admin.products.variants.set-default":{"uri":"admin\/products\/{product}\/variants\/{variant}\/set-default","methods":["PUT"],"parameters":["product","variant"],"bindings":{"product":"id","variant":"id"}},"admin.products.variants.bulk-stock":{"uri":"admin\/products\/{product}\/variants-bulk-stock","methods":["PUT"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.variants.bulk-status":{"uri":"admin\/products\/{product}\/variants-bulk-status","methods":["PUT"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.variants.bulk-price":{"uri":"admin\/products\/{product}\/variants-bulk-price","methods":["PUT"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.variants.bulk-delete":{"uri":"admin\/products\/{product}\/variants-bulk-delete","methods":["DELETE"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.variants.destroy":{"uri":"admin\/products\/{product}\/variants\/{variant}","methods":["DELETE"],"parameters":["product","variant"],"bindings":{"product":"id","variant":"id"}},"admin.products.bulk-edit":{"uri":"admin\/products\/bulk-edit","methods":["GET","HEAD"]},"admin.products.bulk-status":{"uri":"admin\/products\/bulk-status","methods":["PUT"]},"admin.products.bulk-category":{"uri":"admin\/products\/bulk-category","methods":["PUT"]},"admin.products.bulk-prices":{"uri":"admin\/products\/bulk-prices","methods":["PUT"]},"admin.products.bulk-stock":{"uri":"admin\/products\/bulk-stock","methods":["PUT"]},"admin.products.bulk-default-variant":{"uri":"admin\/products\/bulk-default-variant","methods":["PUT"]},"admin.products.bulk-delete":{"uri":"admin\/products\/bulk-delete","methods":["DELETE"]},"admin.products.import":{"uri":"admin\/products\/import","methods":["GET","HEAD"]},"admin.products.import.process":{"uri":"admin\/products\/import","methods":["POST"]},"admin.products.export":{"uri":"admin\/products\/export","methods":["GET","HEAD"]},"admin.categories.tree":{"uri":"admin\/categories\/tree","methods":["GET","HEAD"]},"admin.categories.positions":{"uri":"admin\/categories\/positions","methods":["PUT"]},"admin.categories.featured":{"uri":"admin\/categories\/{category}\/featured","methods":["PUT"],"parameters":["category"],"bindings":{"category":"id"}},"admin.categories.menu-visibility":{"uri":"admin\/categories\/{category}\/menu-visibility","methods":["PUT"],"parameters":["category"],"bindings":{"category":"id"}},"admin.categories.dropdown":{"uri":"admin\/categories\/dropdown","methods":["GET","HEAD"]},"admin.categories.order":{"uri":"admin\/categories\/order","methods":["GET","HEAD"]},"admin.categories.order.update":{"uri":"admin\/categories\/order","methods":["POST"]},"admin.categories.create":{"uri":"admin\/categories\/create","methods":["GET","HEAD"]},"admin.categories.store":{"uri":"admin\/categories","methods":["POST"]},"admin.categories.show":{"uri":"admin\/categories\/{category}","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"id"}},"admin.categories.edit":{"uri":"admin\/categories\/{category}\/edit","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"id"}},"admin.categories.update":{"uri":"admin\/categories\/{category}","methods":["PUT"],"parameters":["category"],"bindings":{"category":"id"}},"admin.categories.destroy":{"uri":"admin\/categories\/{category}","methods":["DELETE"],"parameters":["category"],"bindings":{"category":"id"}},"admin.categories.attributes.edit":{"uri":"admin\/categories\/{category}\/attributes","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"id"}},"admin.categories.attributes.update":{"uri":"admin\/categories\/{category}\/attributes","methods":["PUT"],"parameters":["category"],"bindings":{"category":"id"}},"admin.orders.notes.add":{"uri":"admin\/orders\/{order}\/notes","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.notes.add-with-type":{"uri":"admin\/orders\/{order}\/notes-with-type","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.notes.delete":{"uri":"admin\/orders\/{order}\/notes\/{note}","methods":["DELETE"],"parameters":["order","note"],"bindings":{"order":"id","note":"id"}},"admin.orders.update-status":{"uri":"admin\/orders\/{order}\/status","methods":["PUT"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.payment-status":{"uri":"admin\/orders\/{order}\/payment-status","methods":["PUT"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.shipping":{"uri":"admin\/orders\/{order}\/shipping","methods":["PUT"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.shipping-info":{"uri":"admin\/orders\/{order}\/shipping-info","methods":["PUT"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.mark-delivered":{"uri":"admin\/orders\/{order}\/mark-delivered","methods":["PUT"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.invoice-info":{"uri":"admin\/orders\/{order}\/invoice-info","methods":["PUT"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.invoice":{"uri":"admin\/orders\/{order}\/invoice","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.send-confirmation":{"uri":"admin\/orders\/{order}\/send-confirmation","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"admin.shipping-companies.index":{"uri":"admin\/shipping-companies","methods":["GET","HEAD"]},"admin.shipping-companies.create":{"uri":"admin\/shipping-companies\/create","methods":["GET","HEAD"]},"admin.shipping-companies.store":{"uri":"admin\/shipping-companies","methods":["POST"]},"admin.shipping-companies.edit":{"uri":"admin\/shipping-companies\/{shippingCompany}\/edit","methods":["GET","HEAD"],"parameters":["shippingCompany"],"bindings":{"shippingCompany":"id"}},"admin.shipping-companies.update":{"uri":"admin\/shipping-companies\/{shippingCompany}","methods":["PUT"],"parameters":["shippingCompany"],"bindings":{"shippingCompany":"id"}},"admin.shipping-companies.destroy":{"uri":"admin\/shipping-companies\/{shippingCompany}","methods":["DELETE"],"parameters":["shippingCompany"],"bindings":{"shippingCompany":"id"}},"admin.shipping-companies.toggle-status":{"uri":"admin\/shipping-companies\/{shippingCompany}\/toggle-status","methods":["POST"],"parameters":["shippingCompany"],"bindings":{"shippingCompany":"id"}},"admin.bank-accounts.index":{"uri":"admin\/bank-accounts","methods":["GET","HEAD"]},"admin.bank-accounts.create":{"uri":"admin\/bank-accounts\/create","methods":["GET","HEAD"]},"admin.bank-accounts.store":{"uri":"admin\/bank-accounts","methods":["POST"]},"admin.bank-accounts.edit":{"uri":"admin\/bank-accounts\/{bankAccount}\/edit","methods":["GET","HEAD"],"parameters":["bankAccount"],"bindings":{"bankAccount":"id"}},"admin.bank-accounts.update":{"uri":"admin\/bank-accounts\/{bankAccount}","methods":["PUT"],"parameters":["bankAccount"],"bindings":{"bankAccount":"id"}},"admin.bank-accounts.destroy":{"uri":"admin\/bank-accounts\/{bankAccount}","methods":["DELETE"],"parameters":["bankAccount"],"bindings":{"bankAccount":"id"}},"admin.bank-accounts.toggle-status":{"uri":"admin\/bank-accounts\/{bankAccount}\/toggle-status","methods":["POST"],"parameters":["bankAccount"],"bindings":{"bankAccount":"id"}},"admin.cache.index":{"uri":"admin\/cache","methods":["GET","HEAD"]},"admin.cache.clear":{"uri":"admin\/cache\/clear","methods":["POST"]},"admin.cache.warm":{"uri":"admin\/cache\/warm","methods":["POST"]},"admin.locations.index":{"uri":"admin\/locations","methods":["GET","HEAD"]},"admin.locations.refresh-cache":{"uri":"admin\/locations\/refresh-cache","methods":["POST"]},"admin.email-templates.index":{"uri":"admin\/email-templates","methods":["GET","HEAD"]},"admin.email-templates.show":{"uri":"admin\/email-templates\/{emailTemplate}","methods":["GET","HEAD"],"parameters":["emailTemplate"],"bindings":{"emailTemplate":"id"}},"admin.email-templates.edit":{"uri":"admin\/email-templates\/{emailTemplate}\/edit","methods":["GET","HEAD"],"parameters":["emailTemplate"],"bindings":{"emailTemplate":"id"}},"admin.email-templates.update":{"uri":"admin\/email-templates\/{emailTemplate}","methods":["PUT"],"parameters":["emailTemplate"],"bindings":{"emailTemplate":"id"}},"admin.email-templates.send-test":{"uri":"admin\/email-templates\/{emailTemplate}\/send-test","methods":["POST"],"parameters":["emailTemplate"],"bindings":{"emailTemplate":"id"}},"admin.email-settings.index":{"uri":"admin\/email-settings","methods":["GET","HEAD"]},"admin.email-settings.update":{"uri":"admin\/email-settings","methods":["PUT"]},"admin.email-settings.send-test":{"uri":"admin\/email-settings\/send-test","methods":["POST"]},"admin.email-settings.test-mailchimp":{"uri":"admin\/email-settings\/test-mailchimp","methods":["POST"]},"admin.email-settings.test-mailtrap":{"uri":"admin\/email-settings\/test-mailtrap","methods":["POST"]},"admin.email-logs.index":{"uri":"admin\/email-logs","methods":["GET","HEAD"]},"admin.email-logs.show":{"uri":"admin\/email-logs\/{emailLog}","methods":["GET","HEAD"],"parameters":["emailLog"],"bindings":{"emailLog":"id"}},"admin.email-logs.clear":{"uri":"admin\/email-logs\/clear","methods":["POST"]},"admin.email-logs.resend":{"uri":"admin\/email-logs\/{emailLog}\/resend","methods":["POST"],"parameters":["emailLog"],"bindings":{"emailLog":"id"}},"admin.search-analytics.index":{"uri":"admin\/search-analytics","methods":["GET","HEAD"]},"admin.search-analytics.term":{"uri":"admin\/search-analytics\/term\/{query}","methods":["GET","HEAD"],"parameters":["query"]},"admin.search-analytics.product":{"uri":"admin\/search-analytics\/product\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"cart.index":{"uri":"cart","methods":["GET","HEAD"]},"cart.add":{"uri":"cart\/add","methods":["POST"]},"cart.update":{"uri":"cart\/update\/{cartItem}","methods":["PUT"],"parameters":["cartItem"],"bindings":{"cartItem":"id"}},"cart.remove":{"uri":"cart\/remove\/{cartItem}","methods":["DELETE"],"parameters":["cartItem"],"bindings":{"cartItem":"id"}},"cart.clear":{"uri":"cart\/clear","methods":["POST"]},"cart.coupon.apply":{"uri":"cart\/coupon","methods":["POST"]},"cart.coupon.remove":{"uri":"cart\/coupon","methods":["DELETE"]},"orders.track":{"uri":"orders\/track","methods":["GET","HEAD"]},"orders.track.submit":{"uri":"orders\/track","methods":["POST"]},"checkout":{"uri":"checkout","methods":["GET","HEAD"]},"checkout.success":{"uri":"checkout\/success","methods":["GET","HEAD"]},"checkout.payment-form":{"uri":"checkout\/payment-form","methods":["GET","HEAD"]},"checkout.bank-transfer":{"uri":"payment\/bank-transfer","methods":["POST"]},"checkout.iyzico":{"uri":"payment\/iyzico","methods":["POST"]},"checkout.paywithiyzico":{"uri":"payment\/paywithiyzico","methods":["POST"]},"checkout.callback":{"uri":"payment\/callback","methods":["POST"]},"checkout.paywithiyzico.callback":{"uri":"payment\/paywithiyzico\/callback","methods":["GET","POST","HEAD"]},"api.cart.count":{"uri":"api\/cart\/count","methods":["GET","HEAD"]},"api.check-order-status":{"uri":"api\/check-order-status","methods":["GET","HEAD"]},"api.resume-payment":{"uri":"api\/resume-payment","methods":["GET","HEAD"]},"api.cancel-order":{"uri":"api\/cancel-order","methods":["POST"]},"frontend.products.show.seo":{"uri":"{seo_url}","methods":["GET","HEAD"],"wheres":{"seo_url":"^(?!admin|api|cart|checkout|customer|login|register|profile|test|simple-test).*$"},"parameters":["seo_url"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
