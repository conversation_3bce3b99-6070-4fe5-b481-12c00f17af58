import React, { useState, useEffect } from "react";

export default function PriceRangeSlider({
    min,
    max,
    onChange,
    initialMin,
    initialMax,
    priceRanges = [],
}) {
    const [minValue, setMinValue] = useState(
        initialMin !== undefined ? initialMin : ""
    );
    const [maxValue, setMaxValue] = useState(
        initialMax !== undefined ? initialMax : ""
    );
    const [minInputValue, setMinInputValue] = useState(
        initialMin !== undefined ? initialMin : ""
    );
    const [maxInputValue, setMaxInputValue] = useState(
        initialMax !== undefined ? initialMax : ""
    );
    const [selectedRangeIndex, setSelectedRangeIndex] = useState(-1);

    // Fiyat formatla
    const formatPrice = (price) => {
        if (price === undefined || price === null) return "";
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    // Varsayılan fiyat aralıkları
    const defaultPriceRanges = [
        { label: "0 - 100 TL", min: 0, max: 100 },
        { label: "100 - 500 TL", min: 100, max: 500 },
        { label: "500 - 1000 TL", min: 500, max: 1000 },
        { label: "1000 - 5000 TL", min: 1000, max: 5000 },
        { label: "5000 TL ve üzeri", min: 5000, max: 1000000 },
    ];

    // Kullanılacak fiyat aralıkları
    const ranges = priceRanges.length > 0 ? priceRanges : defaultPriceRanges;

    useEffect(() => {
        // Başlangıç değerlerini güncelle
        const minVal = initialMin !== undefined ? initialMin : "";
        const maxVal = initialMax !== undefined ? initialMax : "";

        setMinValue(minVal);
        setMaxValue(maxVal);
        setMinInputValue(minVal);
        setMaxInputValue(maxVal);

        // Önceden belirlenmiş bir aralık varsa, onu seç
        if (initialMin !== undefined && initialMax !== undefined) {
            // Tüm aralıkları kontrol et
            for (let i = 0; i < ranges.length; i++) {
                const range = ranges[i];
                // Tam eşleşme veya yaklaşık eşleşme kontrolü
                if (
                    (range.min === Number(initialMin) &&
                        range.max === Number(initialMax)) ||
                    (Number(initialMin) >= range.min &&
                        Number(initialMax) <= range.max)
                ) {
                    setSelectedRangeIndex(i);
                    return;
                }
            }
            setSelectedRangeIndex(-1);
        } else {
            setSelectedRangeIndex(-1);
        }
    }, [min, max, initialMin, initialMax, ranges]);

    // Seçilen aralığı bul
    const findSelectedRange = (minVal, maxVal) => {
        // Tam eşleşme varsa onu döndür
        const exactMatch = ranges.findIndex(
            (range) => range.min === minVal && range.max === maxVal
        );

        if (exactMatch !== -1) {
            return exactMatch;
        }

        // Tam eşleşme yoksa, -1 döndür (hiçbiri seçili değil)
        return -1;
    };

    // Radio button değiştiğinde
    const handleRangeChange = (index) => {
        setSelectedRangeIndex(index);
        const selectedRange = ranges[index];
        setMinValue(selectedRange.min);
        setMaxValue(selectedRange.max);
        setMinInputValue(selectedRange.min);
        setMaxInputValue(selectedRange.max);
        onChange({ min: selectedRange.min, max: selectedRange.max });
    };

    // Fiyat formatla (ikinci tanım - kaldırılacak)
    // Bu fonksiyon yukarıda zaten tanımlandı

    // Fiyat formatını kaldır (input için)
    const unformatPrice = (formattedPrice) => {
        if (typeof formattedPrice === "number") return formattedPrice;
        if (!formattedPrice) return 0;
        return Number(
            formattedPrice.replace(/[^0-9,-]/g, "").replace(",", ".")
        );
    };

    // Min değer değiştiğinde
    const handleMinChange = (e) => {
        const value = e.target.value;
        setMinInputValue(value);
    };

    // Max değer değiştiğinde
    const handleMaxChange = (e) => {
        const value = e.target.value;
        setMaxInputValue(value);
    };

    // Min değer onBlur
    const handleMinBlur = () => {
        // Boş değer kontrolü
        if (minInputValue === "" || minInputValue === null) {
            onChange({ min: "", max: maxValue });
            return;
        }

        let value = unformatPrice(minInputValue);

        // Geçerli bir sayı değilse, boş bırak
        if (isNaN(value) || value === 0) {
            setMinInputValue("");
            setMinValue("");
            onChange({ min: "", max: maxValue });
            return;
        }

        // Min değerden küçükse, min değeri kullan
        if (min !== undefined && value < min) {
            value = min;
        }

        // Max değerden büyükse, max değerden 1 çıkar
        if (maxValue !== "" && value >= maxValue) {
            value = maxValue - 1;
        }

        setMinValue(value);
        setMinInputValue(value);

        // Önceden belirlenmiş aralıkları temizle
        setSelectedRangeIndex(-1);

        onChange({ min: value, max: maxValue });
    };

    // Max değer onBlur
    const handleMaxBlur = () => {
        // Boş değer kontrolü
        if (maxInputValue === "" || maxInputValue === null) {
            onChange({ min: minValue, max: "" });
            return;
        }

        let value = unformatPrice(maxInputValue);

        // Geçerli bir sayı değilse, boş bırak
        if (isNaN(value) || value === 0) {
            setMaxInputValue("");
            setMaxValue("");
            onChange({ min: minValue, max: "" });
            return;
        }

        // Max değerden büyükse, max değeri kullan
        if (max !== undefined && value > max) {
            value = max;
        }

        // Min değerden küçükse, min değere 1 ekle
        if (minValue !== "" && value <= minValue) {
            value = minValue + 1;
        }

        setMaxValue(value);
        setMaxInputValue(value);

        // Önceden belirlenmiş aralıkları temizle
        setSelectedRangeIndex(-1);

        onChange({ min: minValue, max: value });
    };

    // Enter tuşuna basıldığında
    const handleKeyDown = (e, type) => {
        if (e.key === "Enter") {
            e.preventDefault();
            if (type === "min") {
                handleMinBlur();
            } else {
                handleMaxBlur();
            }
        }
    };

    // Arama butonu tıklandığında
    const handleSearch = () => {
        // Min ve max değerlerini uygula
        if (minInputValue !== "" || maxInputValue !== "") {
            handleMinBlur();
            handleMaxBlur();
        }
    };

    return (
        <div className="mt-4 mb-6">
            {/* Fiyat aralığı girişi - Tek satırda */}
            <div className="mb-4">
                <div className="flex items-center space-x-2">
                    <div className="flex-1">
                        <input
                            type="text"
                            value={minInputValue}
                            onChange={handleMinChange}
                            onBlur={handleMinBlur}
                            onKeyDown={(e) => handleKeyDown(e, "min")}
                            className="w-full px-3 py-2 border border-gray-300 rounded-l-md text-sm"
                            placeholder="Min"
                        />
                    </div>
                    <div className="flex-1">
                        <input
                            type="text"
                            value={maxInputValue}
                            onChange={handleMaxChange}
                            onBlur={handleMaxBlur}
                            onKeyDown={(e) => handleKeyDown(e, "max")}
                            className="w-full px-3 py-2 border border-gray-300 text-sm"
                            placeholder="Max"
                        />
                    </div>
                    <button
                        type="button"
                        className="p-2 text-sm font-medium border border-gray-300 bg-gray-100 text-gray-700 hover:bg-gray-200 rounded-r-md"
                        onClick={handleSearch}
                        aria-label="Ara"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                            />
                        </svg>
                    </button>
                </div>
            </div>

            {/* Önceden belirlenmiş fiyat aralıkları */}
            <div className="space-y-2">
                {ranges.map((range, index) => (
                    <div key={index} className="flex items-center">
                        <input
                            type="radio"
                            id={`price_range_${index}`}
                            name="price_range"
                            className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            checked={selectedRangeIndex === index}
                            onChange={() => handleRangeChange(index)}
                        />
                        <label
                            htmlFor={`price_range_${index}`}
                            className="ml-2 block text-sm text-gray-700"
                        >
                            {range.label}
                        </label>
                    </div>
                ))}
            </div>
        </div>
    );
}
