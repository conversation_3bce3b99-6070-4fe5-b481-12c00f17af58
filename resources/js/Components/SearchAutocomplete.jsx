import React, { useState, useEffect, useRef } from "react";
import { debounce } from "lodash";

export default function SearchAutocomplete({ placeholder = "Ürün ara...", minLength = 2 }) {
    const [query, setQuery] = useState("");
    const [results, setResults] = useState({ products: [], categories: [] });
    const [isLoading, setIsLoading] = useState(false);
    const [showResults, setShowResults] = useState(false);
    const searchRef = useRef(null);

    // Dışarı tıklandığında sonuçları kapat
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (searchRef.current && !searchRef.current.contains(event.target)) {
                setShowResults(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    // <PERSON><PERSON> debounce ile geciktir
    const debouncedSearch = useRef(
        debounce(async (searchQuery) => {
            if (searchQuery.length < minLength) {
                setResults({ products: [], categories: [] });
                setIsLoading(false);
                return;
            }

            setIsLoading(true);
            try {
                const response = await fetch(`/api/search/autocomplete?q=${encodeURIComponent(searchQuery)}`);
                const data = await response.json();
                setResults(data);
            } catch (error) {
                console.error("Arama hatası:", error);
            } finally {
                setIsLoading(false);
            }
        }, 300)
    ).current;

    // Arama sorgusu değiştiğinde
    const handleInputChange = (e) => {
        const value = e.target.value;
        setQuery(value);
        
        if (value.length >= minLength) {
            debouncedSearch(value);
            setShowResults(true);
        } else {
            setResults({ products: [], categories: [] });
            setShowResults(false);
        }
    };

    // Arama formunu gönder
    const handleSubmit = (e) => {
        e.preventDefault();
        if (query.trim().length >= minLength) {
            window.location.href = `/search?q=${encodeURIComponent(query)}`;
        }
    };

    // Sonuç öğesine tıklandığında
    const handleResultClick = (url) => {
        window.location.href = url;
    };

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    return (
        <div className="relative" ref={searchRef}>
            <form onSubmit={handleSubmit} className="flex">
                <input
                    type="text"
                    value={query}
                    onChange={handleInputChange}
                    placeholder={placeholder}
                    className="w-full px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded-r-md hover:bg-blue-700"
                >
                    Ara
                </button>
            </form>

            {showResults && (query.length >= minLength) && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-y-auto">
                    {isLoading ? (
                        <div className="p-4 text-center text-gray-500">Aranıyor...</div>
                    ) : (
                        <>
                            {results.products.length === 0 && results.categories.length === 0 ? (
                                <div className="p-4 text-center text-gray-500">Sonuç bulunamadı</div>
                            ) : (
                                <>
                                    {results.categories.length > 0 && (
                                        <div className="p-2">
                                            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 py-1">
                                                Kategoriler
                                            </h3>
                                            <ul>
                                                {results.categories.map((category) => (
                                                    <li key={category.id}>
                                                        <button
                                                            onClick={() => handleResultClick(category.url)}
                                                            className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                                                        >
                                                            <span className="text-gray-800">{category.name}</span>
                                                        </button>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    )}

                                    {results.products.length > 0 && (
                                        <div className="p-2">
                                            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 py-1">
                                                Ürünler
                                            </h3>
                                            <ul>
                                                {results.products.map((product) => (
                                                    <li key={product.id}>
                                                        <button
                                                            onClick={() => handleResultClick(product.url)}
                                                            className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                                                        >
                                                            <span className="text-gray-800 flex-1">{product.name}</span>
                                                            <span className="text-gray-600 font-medium">
                                                                {formatPrice(product.price)}
                                                            </span>
                                                        </button>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    )}

                                    <div className="p-2 border-t border-gray-200">
                                        <button
                                            onClick={() => handleResultClick(`/search?q=${encodeURIComponent(query)}`)}
                                            className="w-full text-center text-blue-600 hover:text-blue-800 py-2"
                                        >
                                            "{query}" için tüm sonuçları göster
                                        </button>
                                    </div>
                                </>
                            )}
                        </>
                    )}
                </div>
            )}
        </div>
    );
}
