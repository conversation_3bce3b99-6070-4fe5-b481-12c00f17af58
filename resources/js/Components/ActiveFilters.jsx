import React from "react";

export default function ActiveFilters({ filters, onRemove, onClearAll }) {
    // Aktif filtre yoksa gösterme
    if (!filters || Object.keys(filters).length === 0) {
        return null;
    }

    // Güvenli bir filtre nesnesi oluştur
    // JavaScript'in yerleşik metodlarıyla çakışabilecek özellikleri kaldır
    const safeFilters = {};

    // Sadece güvenli özellikleri kopyala
    if (filters.q) safeFilters.q = filters.q;
    if (filters.price_min) safeFilters.price_min = filters.price_min;
    if (filters.price_max) safeFilters.price_max = filters.price_max;
    if (filters.in_stock) safeFilters.in_stock = filters.in_stock;
    if (filters.attributes) safeFilters.attributes = filters.attributes;
    if (filters.variants) safeFilters.variants = filters.variants;
    if (filters.sorting) safeFilters.sorting = filters.sorting;

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    // Filtre etiketlerini oluştur
    const renderFilterTags = () => {
        const tags = [];

        // Arama filtresi
        if (safeFilters.q) {
            tags.push(
                <div
                    key="q"
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 mr-2 mb-2"
                >
                    <span className="mr-1">Arama: {safeFilters.q}</span>
                    <button
                        onClick={() => onRemove("q")}
                        className="ml-1 text-blue-500 hover:text-blue-700"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                            />
                        </svg>
                    </button>
                </div>
            );
        }

        // Fiyat aralığı filtresi
        if (safeFilters.price_min || safeFilters.price_max) {
            const priceText =
                safeFilters.price_min && safeFilters.price_max
                    ? `${formatPrice(safeFilters.price_min)} - ${formatPrice(
                          safeFilters.price_max
                      )}`
                    : safeFilters.price_min
                    ? `Min: ${formatPrice(safeFilters.price_min)}`
                    : `Max: ${formatPrice(safeFilters.price_max)}`;

            tags.push(
                <div
                    key="price"
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 mr-2 mb-2"
                >
                    <span className="mr-1">Fiyat: {priceText}</span>
                    <button
                        onClick={() => {
                            onRemove("price_min");
                            onRemove("price_max");
                        }}
                        className="ml-1 text-blue-500 hover:text-blue-700"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                            />
                        </svg>
                    </button>
                </div>
            );
        }

        // Stok durumu filtresi
        if (safeFilters.in_stock) {
            tags.push(
                <div
                    key="in_stock"
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 mr-2 mb-2"
                >
                    <span className="mr-1">Stokta Var</span>
                    <button
                        onClick={() => onRemove("in_stock")}
                        className="ml-1 text-blue-500 hover:text-blue-700"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                            />
                        </svg>
                    </button>
                </div>
            );
        }

        // Özellik filtreleri
        if (safeFilters.attributes) {
            Object.entries(safeFilters.attributes).forEach(([code, values]) => {
                values.forEach((value) => {
                    tags.push(
                        <div
                            key={`attr_${code}_${value}`}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 mr-2 mb-2"
                        >
                            <span className="mr-1">
                                {code}: {value}
                            </span>
                            <button
                                onClick={() =>
                                    onRemove(`attributes.${code}`, value)
                                }
                                className="ml-1 text-blue-500 hover:text-blue-700"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </button>
                        </div>
                    );
                });
            });
        }

        // Varyant filtreleri
        if (safeFilters.variants) {
            Object.entries(safeFilters.variants).forEach(([code, values]) => {
                values.forEach((value) => {
                    tags.push(
                        <div
                            key={`var_${code}_${value}`}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 mr-2 mb-2"
                        >
                            <span className="mr-1">
                                {code}: {value}
                            </span>
                            <button
                                onClick={() =>
                                    onRemove(`variants.${code}`, value)
                                }
                                className="ml-1 text-blue-500 hover:text-blue-700"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </button>
                        </div>
                    );
                });
            });
        }

        return tags;
    };

    return (
        <div className="bg-white rounded-lg shadow-md p-4 mb-4">
            <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium text-gray-900">
                    Aktif Filtreler
                </h3>
                <button
                    onClick={onClearAll}
                    className="text-sm text-blue-600 hover:text-blue-800"
                >
                    Tümünü Temizle
                </button>
            </div>
            <div className="flex flex-wrap">{renderFilterTags()}</div>
        </div>
    );
}
