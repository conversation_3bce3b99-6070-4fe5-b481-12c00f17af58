import React, { useState, useEffect } from "react";

export default function FilterGroup({ title, children, initialOpen = false }) {
    // Her zaman açık tutuyoruz
    const [isOpen, setIsOpen] = useState(true);

    // initialOpen değiştiğinde isOpen durumunu güncelle
    useEffect(() => {
        setIsOpen(true); // Her zaman açık
    }, [initialOpen]);

    // Tıklama olayını ele al ve yayılmasını engelle
    const handleToggle = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsOpen(!isOpen);
    };

    return (
        <div className="border-b border-gray-200 py-4">
            <button
                type="button"
                className={`flex justify-between items-center w-full text-left p-2 rounded-md ${
                    initialOpen ? "bg-gray-100" : ""
                }`}
                onClick={handleToggle}
            >
                <h3
                    className={`text-sm font-medium ${
                        initialOpen ? "text-blue-600" : "text-gray-900"
                    }`}
                >
                    {title}
                    {initialOpen && initialOpen !== "empty" && (
                        <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                            Seçili
                        </span>
                    )}
                </h3>
                <span
                    className={`${
                        initialOpen ? "text-blue-600" : "text-gray-500"
                    }`}
                >
                    {isOpen ? (
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path
                                fillRule="evenodd"
                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                clipRule="evenodd"
                            />
                        </svg>
                    ) : (
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path
                                fillRule="evenodd"
                                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                clipRule="evenodd"
                            />
                        </svg>
                    )}
                </span>
            </button>

            {isOpen && <div className="mt-4 space-y-1">{children}</div>}
        </div>
    );
}
