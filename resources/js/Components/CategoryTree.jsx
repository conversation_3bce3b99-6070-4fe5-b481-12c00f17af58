import React, { useState, useEffect, useRef } from "react";
import { ChevronDownIcon, ChevronRightIcon } from "@heroicons/react/24/outline";

const CategoryTree = ({
    categories,
    selectedCategoryId,
    onSelect,
    showIndent = true,
    showCheckbox = false,
    onCategoryPathChange = null, // Kategori yolu değiştiğinde çağrılacak callback
}) => {
    const [expandedCategories, setExpandedCategories] = useState({});

    // Kategori yolunu bul (seçili kategorinin tüm üst kategorileri dahil)
    const findCategoryPath = (cats, targetId, path = []) => {
        for (const cat of cats) {
            if (cat.id === targetId) {
                return [...path, cat];
            }

            if (cat.children && cat.children.length > 0) {
                const newPath = [...path, cat];
                const result = findCategoryPath(
                    cat.children,
                    targetId,
                    newPath
                );
                if (result.length > 0) {
                    return result;
                }
            }
        }
        return [];
    };

    // İlk yüklemede seçili kategorinin tüm üst kategorilerini genişlet
    useEffect(() => {
        if (selectedCategoryId) {
            const findAndExpandParents = (cats, targetId, parentIds = {}) => {
                for (const cat of cats) {
                    if (cat.id === targetId) {
                        return parentIds;
                    }

                    if (cat.children && cat.children.length > 0) {
                        const newParentIds = { ...parentIds, [cat.id]: true };
                        const result = findAndExpandParents(
                            cat.children,
                            targetId,
                            newParentIds
                        );
                        if (Object.keys(result).length > 0) {
                            return result;
                        }
                    }
                }
                return {};
            };

            const expandedParents = findAndExpandParents(
                categories,
                selectedCategoryId
            );
            setExpandedCategories(expandedParents);

            // Kategori yolunu bul ve callback'i çağır
            if (onCategoryPathChange) {
                const categoryPath = findCategoryPath(
                    categories,
                    selectedCategoryId
                );
                onCategoryPathChange(categoryPath);
            }
        }
    }, [selectedCategoryId, categories, onCategoryPathChange]);

    const toggleCategory = (categoryId) => {
        setExpandedCategories((prev) => ({
            ...prev,
            [categoryId]: !prev[categoryId],
        }));
    };

    const renderCategory = (category, level = 0) => {
        const hasChildren = category.children && category.children.length > 0;
        const isExpanded = expandedCategories[category.id];
        const isSelected = category.id === selectedCategoryId;

        return (
            <div key={category.id} className="category-item">
                <div
                    className={`flex items-center py-2 ${
                        isSelected ? "bg-blue-50" : "hover:bg-gray-50"
                    } rounded cursor-pointer`}
                    style={{
                        paddingLeft: showIndent ? `${level * 20}px` : "0",
                    }}
                >
                    {hasChildren && (
                        <button
                            type="button"
                            onClick={(e) => {
                                e.stopPropagation();
                                toggleCategory(category.id);
                            }}
                            className="mr-1 p-1 rounded-full hover:bg-gray-200 focus:outline-none"
                        >
                            {isExpanded ? (
                                <ChevronDownIcon className="h-4 w-4 text-gray-500" />
                            ) : (
                                <ChevronRightIcon className="h-4 w-4 text-gray-500" />
                            )}
                        </button>
                    )}

                    {!hasChildren && <div className="w-6"></div>}

                    <div
                        className="flex items-center flex-1"
                        onClick={() => {
                            onSelect(category.id);
                            if (onCategoryPathChange) {
                                const categoryPath = findCategoryPath(
                                    categories,
                                    category.id
                                );
                                onCategoryPathChange(categoryPath);
                            }
                        }}
                    >
                        {showCheckbox && (
                            <input
                                type="radio"
                                name="category"
                                checked={isSelected}
                                onChange={() => onSelect(category.id)}
                                className="mr-2"
                            />
                        )}
                        <span
                            className={`${
                                isSelected
                                    ? "font-medium text-blue-600"
                                    : "text-gray-700"
                            }`}
                        >
                            {category.name}
                        </span>

                        {!category.status && (
                            <span className="ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full">
                                Pasif
                            </span>
                        )}
                    </div>
                </div>

                {hasChildren && isExpanded && (
                    <div className="ml-2">
                        {category.children.map((child) =>
                            renderCategory(child, level + 1)
                        )}
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="category-tree border rounded-lg overflow-hidden bg-white">
            <div className="p-3 border-b bg-gray-50">
                <h3 className="font-medium text-gray-700">Kategoriler</h3>
            </div>
            <div className="p-2 max-h-80 overflow-y-auto">
                {categories.length === 0 ? (
                    <div className="text-gray-500 text-center py-4">
                        Kategori bulunamadı
                    </div>
                ) : (
                    categories.map((category) => renderCategory(category))
                )}
            </div>
        </div>
    );
};

export default CategoryTree;
