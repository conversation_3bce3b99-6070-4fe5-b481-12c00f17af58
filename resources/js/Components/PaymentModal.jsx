import React, { useEffect, useRef, useState } from "react";

export default function PaymentModal({
    isOpen,
    onClose,
    htmlContent,
    onPaymentComplete,
}) {
    const modalRef = useRef(null);
    const contentRef = useRef(null);
    const [loading, setLoading] = useState(false);
    const [status, setStatus] = useState(null); // 'success', 'error', null

    // Modal açıldığında veya kapandığında status ve loading durumlarını sıfırla
    useEffect(() => {
        if (isOpen) {
            setStatus(null);
            setLoading(false);
        }
    }, [isOpen]);

    // HTML içeriğini modal içine yerleştir
    useEffect(() => {
        if (isOpen && htmlContent && contentRef.current) {
            console.log("HTML içeriği modal içine yerleştiriliyor...");

            try {
                // HTML içeriğini yerleştir
                contentRef.current.innerHTML = htmlContent;

                // Form varsa işle
                setTimeout(() => {
                    const form = contentRef.current.querySelector("form");

                    if (form) {
                        console.log("3D Secure formu bulundu");

                        // Form action URL'sini al
                        const originalAction = form.getAttribute("action");

                        if (originalAction) {
                            console.log("Form action URL:", originalAction);

                            // Iframe oluştur
                            const iframeContainer =
                                document.createElement("div");
                            iframeContainer.style.width = "100%";
                            iframeContainer.style.height = "500px";
                            iframeContainer.style.overflow = "hidden";

                            const iframe = document.createElement("iframe");
                            iframe.name = "iyzico3DSecureFrame";
                            iframe.id = "iyzico3DSecureFrame";
                            iframe.style.width = "100%";
                            iframe.style.height = "100%";
                            iframe.style.border = "none";

                            // Iframe'i sayfaya ekle
                            iframeContainer.appendChild(iframe);

                            // Form içeriğini sakla
                            const formContent = contentRef.current.innerHTML;

                            // İframe container'ı ekle
                            contentRef.current.innerHTML = "";
                            contentRef.current.appendChild(iframeContainer);

                            // Form'u gizli bir div içine ekle
                            const hiddenDiv = document.createElement("div");
                            hiddenDiv.style.display = "none";
                            hiddenDiv.innerHTML = formContent;
                            contentRef.current.appendChild(hiddenDiv);

                            // Form'u tekrar seç
                            const newForm = hiddenDiv.querySelector("form");

                            if (newForm) {
                                // Form hedefini iframe olarak ayarla
                                newForm.target = "iyzico3DSecureFrame";

                                // Form submit olayını dinle
                                const originalSubmit = newForm.submit;
                                newForm.submit = function () {
                                    console.log(
                                        "Form iframe'e gönderiliyor..."
                                    );
                                    // Orijinal submit fonksiyonunu çağır
                                    return originalSubmit.apply(
                                        this,
                                        arguments
                                    );
                                };

                                // Formu gönder
                                console.log("Form iframe'e gönderiliyor...");
                                newForm.submit();
                            } else {
                                console.error("Form tekrar seçilemedi");
                            }
                        } else {
                            console.log(
                                "Form action URL bulunamadı, normal işlem yapılıyor"
                            );

                            // Form submit olayını dinle
                            const originalSubmit = form.submit;
                            form.submit = function () {
                                console.log(
                                    "Form normal şekilde gönderiliyor..."
                                );
                                // Orijinal submit fonksiyonunu çağır
                                return originalSubmit.apply(this, arguments);
                            };

                            // Formu gönder
                            form.submit();
                        }
                    } else {
                        console.log(
                            "Form bulunamadı, içerik olduğu gibi gösteriliyor"
                        );

                        // İframe var mı kontrol et
                        const iframe =
                            contentRef.current.querySelector("iframe");
                        if (iframe) {
                            console.log(
                                "İframe bulundu, boyutları ayarlanıyor"
                            );
                            iframe.style.width = "100%";
                            iframe.style.height = "500px";
                            iframe.style.border = "none";
                        }
                    }
                }, 100);
            } catch (error) {
                console.error(
                    "HTML içeriği yerleştirilirken hata oluştu:",
                    error
                );
            }
        }
    }, [isOpen, htmlContent]);

    // İyzico ödeme sonucunu kontrol et
    const checkPaymentStatus = (orderNumber) => {
        setLoading(true);

        // Ödeme durumunu kontrol et
        fetch(`/api/check-payment-status?order_number=${orderNumber}`, {
            method: "GET",
            headers: {
                Accept: "application/json",
                "X-Requested-With": "XMLHttpRequest",
            },
        })
            .then((response) => response.json())
            .then((data) => {
                setLoading(false);

                if (data.success) {
                    setStatus("success");
                    // 3 saniye sonra otomatik olarak başarılı sayfasına yönlendir
                    setTimeout(() => {
                        if (onPaymentComplete) {
                            onPaymentComplete(orderNumber);
                        } else {
                            // Eğer callback fonksiyonu yoksa, doğrudan yönlendir
                            window.location.href = `/checkout/success?order_number=${orderNumber}`;
                        }
                    }, 2000);
                } else {
                    setStatus("error");
                }
            })
            .catch((error) => {
                console.error(
                    "Ödeme durumu kontrol edilirken hata oluştu:",
                    error
                );
                setLoading(false);
                setStatus("error");
            });
    };

    // Modal dışına tıklandığında kapat - ödeme işlemi devam ederken devre dışı bırak
    useEffect(() => {
        function handleClickOutside(event) {
            if (
                modalRef.current &&
                !modalRef.current.contains(event.target) &&
                !loading
            ) {
                // Kullanıcıya onay sor
                if (
                    confirm(
                        "Ödeme işlemini yarıda kesmek istediğinize emin misiniz?"
                    )
                ) {
                    onClose();
                }
            }
        }

        if (isOpen) {
            document.addEventListener("mousedown", handleClickOutside);
        } else {
            document.removeEventListener("mousedown", handleClickOutside);
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [isOpen, onClose, loading]);

    // ESC tuşuna basıldığında kapat - ödeme işlemi devam ederken devre dışı bırak
    useEffect(() => {
        function handleEscKey(event) {
            if (event.key === "Escape" && !loading) {
                // Kullanıcıya onay sor
                if (
                    confirm(
                        "Ödeme işlemini yarıda kesmek istediğinize emin misiniz?"
                    )
                ) {
                    onClose();
                }
            }
        }

        if (isOpen) {
            document.addEventListener("keydown", handleEscKey);
        } else {
            document.removeEventListener("keydown", handleEscKey);
        }

        return () => {
            document.removeEventListener("keydown", handleEscKey);
        };
    }, [isOpen, onClose, loading]);

    // iFrame mesajlarını dinle (PayWithIyzico için)
    useEffect(() => {
        const handleMessage = (event) => {
            console.log("Mesaj alındı:", event.data);

            // Basit başarı mesajı kontrolü
            if (event.data === "success") {
                console.log("Basit başarı mesajı alındı");

                // Doğrudan başarılı duruma geç
                setStatus("success");

                // 2 saniye sonra modalı kapat ve yönlendir
                setTimeout(() => {
                    // Modalı kapat
                    onClose();

                    // Sipariş özet sayfasına yönlendir
                    window.location.href = "/checkout/success";
                }, 2000);

                return;
            }

            // iyzico'dan gelen mesajları kontrol et
            if (event.data) {
                // Obje formatındaki mesajları kontrol et
                if (typeof event.data === "object" && event.data !== null) {
                    console.log("Obje formatında mesaj alındı:", event.data);

                    // iyzico başarılı ödeme mesajını kontrol et
                    if (event.data.type === "iyzico:success") {
                        console.log(
                            "iyzico başarılı ödeme mesajı alındı (obje formatı)"
                        );

                        // Redirect URL ve sipariş numarasını al
                        const redirectUrl = event.data.redirectUrl;
                        const orderNumber = event.data.orderNumber;

                        console.log("Yönlendirme URL'si:", redirectUrl);
                        console.log("Sipariş numarası:", orderNumber);

                        // Doğrudan başarılı duruma geç
                        setStatus("success");

                        // 2 saniye sonra modalı kapat ve yönlendir
                        setTimeout(() => {
                            // Modalı kapat
                            onClose();

                            // Sipariş özet sayfasına yönlendir
                            if (redirectUrl) {
                                window.location.href = redirectUrl;
                            } else if (orderNumber) {
                                window.location.href = `/checkout/success?order_number=${orderNumber}`;
                            } else {
                                window.location.href = "/checkout/success";
                            }
                        }, 2000);

                        return;
                    }

                    // iyzico hata mesajını kontrol et
                    if (event.data.type === "iyzico:error") {
                        console.log(
                            "iyzico hata mesajı alındı (obje formatı):",
                            event.data.message
                        );
                        setStatus("error");
                        return;
                    }
                }

                // String formatındaki mesajları kontrol et
                if (typeof event.data === "string") {
                    console.log("String formatında mesaj alındı:", event.data);

                    // iyzico başarılı ödeme mesajını kontrol et
                    if (
                        event.data.includes("iyzico") &&
                        event.data.includes("success")
                    ) {
                        console.log(
                            "iyzico başarılı ödeme mesajı alındı (string formatı)"
                        );

                        let orderNumber = null;
                        let redirectUrl = null;

                        // Redirect URL'den gelen mesajı kontrol et
                        if (event.data.startsWith("iyzico:success:")) {
                            // Doğrudan sipariş numarası formatı: iyzico:success:order_number=ABC123
                            if (event.data.includes("order_number=")) {
                                const orderNumberMatch =
                                    event.data.match(/order_number=([^&]+)/);
                                if (orderNumberMatch && orderNumberMatch[1]) {
                                    orderNumber = orderNumberMatch[1];
                                    console.log(
                                        "Sipariş numarası doğrudan alındı:",
                                        orderNumber
                                    );
                                }
                            } else {
                                // URL formatı: iyzico:success:https://example.com/checkout/success?order_number=ABC123
                                redirectUrl = event.data.replace(
                                    "iyzico:success:",
                                    ""
                                );
                                console.log("Yönlendirme URL'si:", redirectUrl);

                                // Sipariş numarasını URL'den çıkar
                                const orderNumberMatch =
                                    redirectUrl.match(/order_number=([^&]+)/);
                                if (orderNumberMatch && orderNumberMatch[1]) {
                                    orderNumber = orderNumberMatch[1];
                                    console.log(
                                        "Sipariş numarası URL'den alındı:",
                                        orderNumber
                                    );
                                }
                            }
                        } else {
                            // Eski format için sipariş numarasını al
                            const orderNumberMatch =
                                event.data.match(/order_number=([^&]+)/);
                            if (orderNumberMatch && orderNumberMatch[1]) {
                                orderNumber = orderNumberMatch[1];
                                console.log(
                                    "Sipariş numarası eski formattan alındı:",
                                    orderNumber
                                );
                            }
                        }

                        // Doğrudan başarılı duruma geç
                        setStatus("success");

                        // 2 saniye sonra modalı kapat ve yönlendir
                        setTimeout(() => {
                            // Modalı kapat
                            onClose();

                            // Sipariş özet sayfasına yönlendir
                            if (redirectUrl) {
                                window.location.href = redirectUrl;
                            } else if (orderNumber) {
                                window.location.href = `/checkout/success?order_number=${orderNumber}`;
                            } else {
                                window.location.href = "/checkout/success";
                            }
                        }, 2000);

                        return;
                    }

                    // iyzico hata mesajını kontrol et
                    if (
                        event.data.includes("iyzico") &&
                        event.data.includes("error")
                    ) {
                        console.log(
                            "iyzico hata mesajı alındı (string formatı)"
                        );
                        setStatus("error");
                    }
                }

                // iframe'den gelen özel mesajları kontrol et
                try {
                    // iframe'den gelen mesajları kontrol et
                    if (
                        event.source &&
                        contentRef.current &&
                        event.source ===
                            contentRef.current.querySelector("iframe")
                                ?.contentWindow
                    ) {
                        console.log("iframe'den mesaj alındı:", event.data);

                        // Başarılı ödeme mesajını kontrol et
                        if (
                            event.data.success === true &&
                            event.data.order_number
                        ) {
                            console.log(
                                "iframe'den başarılı ödeme mesajı alındı:",
                                event.data.order_number
                            );

                            const orderNumber = event.data.order_number;

                            // Doğrudan başarılı duruma geç
                            setStatus("success");

                            // 2 saniye sonra modalı kapat ve yönlendir
                            setTimeout(() => {
                                // Modalı kapat
                                onClose();

                                // Sipariş özet sayfasına yönlendir
                                window.location.href = `/checkout/success?order_number=${orderNumber}`;
                            }, 2000);
                        }

                        // Hata mesajını kontrol et
                        if (event.data.success === false) {
                            console.log(
                                "iframe'den hata mesajı alındı:",
                                event.data.message
                            );
                            setStatus("error");
                        }
                    }
                } catch (e) {
                    console.error("iframe mesajı işlenirken hata oluştu:", e);
                }
            }
        };

        if (isOpen) {
            window.addEventListener("message", handleMessage);
        }

        return () => {
            window.removeEventListener("message", handleMessage);
        };
    }, [isOpen, onClose]);

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div
                ref={modalRef}
                className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col"
            >
                <div className="flex justify-between items-center p-4 border-b">
                    <h2 className="text-lg font-semibold">Ödeme İşlemi</h2>
                    <button
                        onClick={() => {
                            if (loading) {
                                alert(
                                    "Ödeme işlemi devam ediyor, lütfen bekleyin."
                                );
                                return;
                            }

                            // Kullanıcıya onay sor
                            if (
                                confirm(
                                    "Ödeme işlemini yarıda kesmek istediğinize emin misiniz?"
                                )
                            ) {
                                onClose();
                            }
                        }}
                        className="text-gray-500 hover:text-gray-700 focus:outline-none"
                        disabled={loading}
                    >
                        <svg
                            className={`w-6 h-6 ${loading ? "opacity-50" : ""}`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M6 18L18 6M6 6l12 12"
                            />
                        </svg>
                    </button>
                </div>

                <div className="p-4 flex-1 overflow-auto">
                    {loading && (
                        <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-10">
                            <div className="text-center">
                                <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
                                <p className="text-lg font-medium">
                                    İşlem doğrulanıyor...
                                </p>
                                <p className="text-sm text-gray-500">
                                    Lütfen bekleyin, ödeme işleminiz
                                    doğrulanıyor.
                                </p>
                            </div>
                        </div>
                    )}

                    {status === "success" && (
                        <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
                            <div className="text-center p-6 bg-white rounded-lg shadow-lg max-w-md">
                                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg
                                        className="w-10 h-10 text-green-500"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth="2"
                                            d="M5 13l4 4L19 7"
                                        ></path>
                                    </svg>
                                </div>
                                <h3 className="text-xl font-bold text-gray-900 mb-2">
                                    Ödeme Başarılı!
                                </h3>
                                <p className="text-gray-600 mb-4">
                                    Ödeme işleminiz başarıyla tamamlandı.
                                    Siparişiniz hazırlanıyor.
                                </p>
                                <button
                                    onClick={() => {
                                        if (onPaymentComplete) {
                                            // Eğer sipariş numarası varsa gönder
                                            const orderNumberMatch =
                                                window.location.href.match(
                                                    /order_number=([^&]+)/
                                                );
                                            const orderNumber = orderNumberMatch
                                                ? orderNumberMatch[1]
                                                : null;
                                            onPaymentComplete(orderNumber);
                                        } else {
                                            // Eğer callback fonksiyonu yoksa, doğrudan yönlendir
                                            const orderNumberMatch =
                                                window.location.href.match(
                                                    /order_number=([^&]+)/
                                                );
                                            if (
                                                orderNumberMatch &&
                                                orderNumberMatch[1]
                                            ) {
                                                window.location.href = `/checkout/success?order_number=${orderNumberMatch[1]}`;
                                            } else {
                                                onClose();
                                            }
                                        }
                                    }}
                                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                                >
                                    Tamam
                                </button>
                            </div>
                        </div>
                    )}

                    {status === "error" && (
                        <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
                            <div className="text-center p-6 bg-white rounded-lg shadow-lg max-w-md">
                                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg
                                        className="w-10 h-10 text-red-500"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth="2"
                                            d="M6 18L18 6M6 6l12 12"
                                        ></path>
                                    </svg>
                                </div>
                                <h3 className="text-xl font-bold text-gray-900 mb-2">
                                    Ödeme Başarısız
                                </h3>
                                <p className="text-gray-600 mb-4">
                                    Ödeme işlemi sırasında bir sorun oluştu.
                                    Lütfen tekrar deneyin.
                                </p>
                                <button
                                    onClick={onClose}
                                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                                >
                                    Kapat
                                </button>
                            </div>
                        </div>
                    )}

                    <div
                        ref={contentRef}
                        className="payment-content w-full h-full min-h-[400px]"
                        style={{ overflow: "hidden" }}
                    ></div>
                </div>
            </div>
        </div>
    );
}
