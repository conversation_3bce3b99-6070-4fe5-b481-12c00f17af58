import React, { useState } from "react";
import { Link } from "@inertiajs/react";
import { toast } from "react-hot-toast";
import axios from "axios";

export default function HomeProductCard({ product }) {
    const [loading, setLoading] = useState(false);

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    // Sepete ekle
    const addToCart = () => {
        if (loading) return;

        // Ürünün varyantları varsa ürün detay sayfasına yönlendir
        if (product.has_variants) {
            window.location.href = product.url;
            return;
        }

        setLoading(true);

        // Sepete eklenecek verileri hazırla
        const cartData = {
            product_id: product.id,
            quantity: 1,
        };

        // Eğer varsayılan varyant varsa, onu ekle
        if (product.default_variant_id) {
            cartData.variant_id = product.default_variant_id;
        }

        // Axios ile POST isteği gönder
        axios
            .post(route("cart.add"), cartData)
            .then((response) => {
                toast.success("Ürün sepete eklendi");

                // Sepet sayısını güncelle
                if (response.data && response.data.cartCount !== undefined) {
                    // Özel olay tetikle - Bu, tüm komponentlerin sepet sayısını güncellemesini sağlar
                    window.dispatchEvent(
                        new CustomEvent("cart-updated", {
                            detail: { cartCount: response.data.cartCount },
                        })
                    );
                }

                setLoading(false);
            })
            .catch((error) => {
                console.error("Sepete ekleme hatası:", error);
                toast.error("Ürün sepete eklenirken bir hata oluştu");
                setLoading(false);
            });
    };

    return (
        <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
            <Link href={product.url}>
                <div className="h-48 bg-gray-200 relative">
                    {product.is_on_sale && (
                        <div className="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                            İndirim
                        </div>
                    )}
                    {product.image ? (
                        <img
                            src={product.image}
                            alt={product.name}
                            className="w-full h-full object-cover"
                        />
                    ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-400">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-12 w-12"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={1}
                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                />
                            </svg>
                        </div>
                    )}
                </div>
            </Link>
            <div className="p-4">
                <Link href={product.url}>
                    <h3 className="font-semibold text-lg mb-1 hover:text-blue-600 transition">
                        {product.name}
                    </h3>
                </Link>
                <p className="text-gray-600 text-sm mb-2">
                    {product.category?.name || "Kategori"}
                </p>
                <div className="flex justify-between items-center">
                    <div>
                        {product.is_on_sale && product.sale_price ? (
                            <div>
                                <span className="font-bold text-lg text-red-600">
                                    {formatPrice(product.sale_price)}
                                </span>
                                <span className="text-gray-500 text-sm line-through ml-2">
                                    {formatPrice(product.price)}
                                </span>
                            </div>
                        ) : (
                            <span className="font-bold text-lg">
                                {formatPrice(product.price)}
                            </span>
                        )}
                    </div>
                    <button
                        onClick={addToCart}
                        disabled={loading}
                        className={`${
                            loading
                                ? "bg-gray-400"
                                : "bg-blue-600 hover:bg-blue-700"
                        } text-white px-3 py-1 rounded transition`}
                    >
                        {loading ? "..." : "Sepete Ekle"}
                    </button>
                </div>
            </div>
        </div>
    );
}
