import React, { useState } from "react";
import { router } from "@inertiajs/react";

export default function OrderStatusManager({ order, statuses, nextStatuses }) {
    const [selectedStatus, setSelectedStatus] = useState("");
    const [statusNote, setStatusNote] = useState("");
    const [notifyCustomer, setNotifyCustomer] = useState(true);

    const handleStatusChange = (e) => {
        e.preventDefault();

        if (!selectedStatus) {
            alert("Lütfen bir durum seçin");
            return;
        }

        router.put(
            route("admin.orders.update-status", order.id),
            {
                status: selectedStatus,
                note: statusNote,
                notify_customer: notifyCustomer,
            },
            {
                onSuccess: () => {
                    setSelectedStatus("");
                    setStatusNote("");
                },
            }
        );
    };

    return (
        <div className="bg-white shadow rounded-lg p-4 mb-6">
            <h3 className="text-lg font-medium mb-4">
                <PERSON><PERSON>iş Durumu Yönetimi
            </h3>

            <div className="mb-4">
                <div className="flex items-center mb-2">
                    <span className="font-medium mr-2">Mevcut Durum:</span>
                    <span
                        className={`px-3 py-1 rounded-full text-sm bg-${order.status_color}-100 text-${order.status_color}-800`}
                    >
                        {statuses[order.status]}
                    </span>
                </div>
                <p className="text-sm text-gray-600">
                    {order.status_description}
                </p>
            </div>

            <form onSubmit={handleStatusChange}>
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Yeni Durum
                    </label>
                    <select
                        value={selectedStatus}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                        className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                    >
                        <option value="">Durum Seçin</option>
                        {nextStatuses.map((status) => (
                            <option key={status.value} value={status.value}>
                                {statuses[status.value]}
                            </option>
                        ))}
                    </select>
                </div>

                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Durum Notu (Opsiyonel)
                    </label>
                    <textarea
                        value={statusNote}
                        onChange={(e) => setStatusNote(e.target.value)}
                        className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        rows="3"
                        placeholder="Durum değişikliği hakkında not ekleyin..."
                    ></textarea>
                </div>

                <div className="mb-4">
                    <label className="flex items-center">
                        <input
                            type="checkbox"
                            checked={notifyCustomer}
                            onChange={(e) =>
                                setNotifyCustomer(e.target.checked)
                            }
                            className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                            Müşteriye bildirim gönder
                        </span>
                    </label>
                </div>

                <button
                    type="submit"
                    className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
                    disabled={!selectedStatus}
                >
                    Durumu Güncelle
                </button>
            </form>
        </div>
    );
}
