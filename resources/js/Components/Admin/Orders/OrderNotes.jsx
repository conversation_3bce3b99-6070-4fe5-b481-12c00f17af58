import React, { useState } from "react";
import { router } from "@inertiajs/react";

export default function OrderNotes({ order }) {
    const [note, setNote] = useState("");
    const [isPrivate, setIsPrivate] = useState(true);
    const [noteType, setNoteType] = useState("general");
    const [notifyCustomer, setNotifyCustomer] = useState(false);
    
    const handleAddNote = (e) => {
        e.preventDefault();
        
        if (!note.trim()) {
            alert("Lütfen bir not girin");
            return;
        }
        
        router.post(route("admin.orders.notes.add-with-type", order.id), {
            note,
            is_private: isPrivate,
            note_type: noteType,
            notify_customer: notifyCustomer
        }, {
            onSuccess: () => {
                setNote("");
            }
        });
    };
    
    const handleDeleteNote = (noteId) => {
        if (confirm("Bu notu silmek istediğinizden emin misiniz?")) {
            router.delete(route("admin.orders.notes.delete", [order.id, noteId]));
        }
    };
    
    // Not tipi için renk sınıfı
    const getNoteTypeClass = (type) => {
        switch (type) {
            case 'general':
                return 'bg-gray-100 text-gray-800';
            case 'status_change':
                return 'bg-blue-100 text-blue-800';
            case 'payment':
                return 'bg-green-100 text-green-800';
            case 'shipping':
                return 'bg-purple-100 text-purple-800';
            case 'customer':
                return 'bg-yellow-100 text-yellow-800';
            case 'system':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    
    // Not tipi için etiket
    const getNoteTypeLabel = (type) => {
        switch (type) {
            case 'general':
                return 'Genel Not';
            case 'status_change':
                return 'Durum Değişikliği';
            case 'payment':
                return 'Ödeme Notu';
            case 'shipping':
                return 'Kargo Notu';
            case 'customer':
                return 'Müşteri Notu';
            case 'system':
                return 'Sistem Notu';
            default:
                return 'Diğer';
        }
    };

    return (
        <div className="bg-white shadow rounded-lg p-4 mb-6">
            <h3 className="text-lg font-medium mb-4">Sipariş Notları</h3>
            
            <div className="mb-6">
                <form onSubmit={handleAddNote}>
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Yeni Not Ekle
                        </label>
                        <textarea
                            value={note}
                            onChange={(e) => setNote(e.target.value)}
                            className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            rows="3"
                            placeholder="Sipariş hakkında not ekleyin..."
                        ></textarea>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Not Tipi
                            </label>
                            <select
                                value={noteType}
                                onChange={(e) => setNoteType(e.target.value)}
                                className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            >
                                <option value="general">Genel Not</option>
                                <option value="status_change">Durum Değişikliği</option>
                                <option value="payment">Ödeme Notu</option>
                                <option value="shipping">Kargo Notu</option>
                                <option value="customer">Müşteri Notu</option>
                                <option value="system">Sistem Notu</option>
                            </select>
                        </div>
                        
                        <div className="flex flex-col justify-end">
                            <div className="flex items-center mb-2">
                                <input
                                    type="checkbox"
                                    checked={isPrivate}
                                    onChange={(e) => setIsPrivate(e.target.checked)}
                                    className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                />
                                <span className="ml-2 text-sm text-gray-600">Sadece admin görebilir</span>
                            </div>
                            
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={notifyCustomer}
                                    onChange={(e) => setNotifyCustomer(e.target.checked)}
                                    className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                    disabled={isPrivate}
                                />
                                <span className="ml-2 text-sm text-gray-600">Müşteriye bildirim gönder</span>
                            </div>
                        </div>
                    </div>
                    
                    <button
                        type="submit"
                        className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
                    >
                        Not Ekle
                    </button>
                </form>
            </div>
            
            <div>
                <h4 className="font-medium text-gray-700 mb-2">Not Geçmişi</h4>
                
                {order.notes && order.notes.length > 0 ? (
                    <div className="space-y-4">
                        {order.notes.map((note) => (
                            <div key={note.id} className="border rounded-lg p-3">
                                <div className="flex justify-between items-start mb-2">
                                    <div>
                                        <span className={`inline-block px-2 py-1 rounded-full text-xs ${getNoteTypeClass(note.note_type)}`}>
                                            {getNoteTypeLabel(note.note_type)}
                                        </span>
                                        {note.is_private && (
                                            <span className="ml-2 inline-block px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                                                Özel
                                            </span>
                                        )}
                                        {note.is_customer_notified && (
                                            <span className="ml-2 inline-block px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                                                Bildirim Gönderildi
                                            </span>
                                        )}
                                    </div>
                                    <button
                                        onClick={() => handleDeleteNote(note.id)}
                                        className="text-red-500 hover:text-red-700"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                                
                                <p className="text-gray-700 mb-2 whitespace-pre-line">{note.note}</p>
                                
                                <div className="flex justify-between text-xs text-gray-500">
                                    <span>
                                        {note.user ? note.user.name : 'Sistem'}
                                    </span>
                                    <span>
                                        {new Date(note.created_at).toLocaleString('tr-TR')}
                                    </span>
                                </div>
                                
                                {note.status_before && note.status_after && (
                                    <div className="mt-2 text-xs">
                                        <span className="text-gray-600">Durum değişikliği: </span>
                                        <span className={`px-2 py-1 rounded-full bg-${note.status_before_color}-100 text-${note.status_before_color}-800`}>
                                            {note.status_before_label}
                                        </span>
                                        <span className="mx-2">→</span>
                                        <span className={`px-2 py-1 rounded-full bg-${note.status_after_color}-100 text-${note.status_after_color}-800`}>
                                            {note.status_after_label}
                                        </span>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                ) : (
                    <p className="text-gray-500 italic">Henüz not bulunmuyor</p>
                )}
            </div>
        </div>
    );
}
