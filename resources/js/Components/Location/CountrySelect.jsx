import React, { useState, useEffect } from "react";
import axios from "axios";

export default function CountrySelect({
    value,
    onChange,
    className = "",
    required = false,
    disabled = false,
}) {
    const [countries, setCountries] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchCountries = async () => {
            try {
                setLoading(true);
                // Göreceli URL kullan
                const response = await axios.get("/api/locations/countries");
                if (response.data && response.data.length > 0) {
                    setCountries(response.data);
                } else {
                    console.warn("Ülke verisi bulunamadı veya boş.");
                }
                setLoading(false);
            } catch (err) {
                console.error("Ülkeler yüklenirken hata oluştu:", err);
                setError("Ülkeler yüklenirken bir hata oluştu.");
                setLoading(false);
            }
        };

        fetchCountries();
    }, []);

    return (
        <div>
            <select
                value={value || ""}
                onChange={(e) =>
                    onChange(e.target.value ? parseInt(e.target.value) : "")
                }
                className={`w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 ${className}`}
                disabled={disabled || loading}
                required={required}
            >
                <option value="">Ülke Seçiniz</option>
                {countries.map((country) => (
                    <option key={country.id} value={country.id}>
                        {country.name}
                    </option>
                ))}
            </select>
            {loading && (
                <p className="text-sm text-gray-500 mt-1">Yükleniyor...</p>
            )}
            {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
        </div>
    );
}
