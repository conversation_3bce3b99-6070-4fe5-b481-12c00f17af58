import React, { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';

export default function AddressSelector({ addresses, value, onChange, className = '', required = false }) {
    const [selectedAddress, setSelectedAddress] = useState(value || '');

    useEffect(() => {
        if (value) {
            setSelectedAddress(value);
        }
    }, [value]);

    const handleChange = (e) => {
        const addressId = e.target.value;
        setSelectedAddress(addressId);
        onChange(addressId);
    };

    return (
        <div className={className}>
            {addresses && addresses.length > 0 ? (
                <div>
                    <select
                        value={selectedAddress}
                        onChange={handleChange}
                        className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                        required={required}
                    >
                        <option value="">Adres Seçiniz</option>
                        {addresses.map((address) => (
                            <option key={address.id} value={address.id}>
                                {address.title} - {address.name} ({address.district}, {address.city})
                            </option>
                        ))}
                    </select>
                    <div className="mt-2 flex justify-between text-sm">
                        <Link
                            href={route('customer.addresses.create')}
                            className="text-blue-600 hover:text-blue-800"
                        >
                            Yeni Adres Ekle
                        </Link>
                        {selectedAddress && (
                            <Link
                                href={route('customer.addresses.edit', selectedAddress)}
                                className="text-blue-600 hover:text-blue-800"
                            >
                                Seçili Adresi Düzenle
                            </Link>
                        )}
                    </div>
                </div>
            ) : (
                <div className="text-center py-4 border border-dashed border-gray-300 rounded-md">
                    <p className="text-gray-500 mb-2">Kayıtlı adresiniz bulunmamaktadır.</p>
                    <Link
                        href={route('customer.addresses.create')}
                        className="text-blue-600 hover:text-blue-800"
                    >
                        Yeni Adres Ekle
                    </Link>
                </div>
            )}
        </div>
    );
}
