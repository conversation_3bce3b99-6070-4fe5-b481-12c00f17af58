import React from "react";
import CountrySelect from "./CountrySelect";
import StateSelect from "./StateSelect";
import CitySelect from "./CitySelect";

export default function LocationFields({
    countryId,
    stateId,
    cityId,
    onCountryChange,
    onStateChange,
    onCityChange,
    required = false,
    disabled = false,
    prefix = "", // Prefix for IDs (e.g., "billing_" or "shipping_")
}) {
    return (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label className="block text-gray-700 mb-1">
                    Ülke {required && <span className="text-red-500">*</span>}
                </label>
                <CountrySelect
                    value={countryId}
                    onChange={onCountryChange}
                    required={required}
                    disabled={disabled}
                    id={`${prefix}country_select`}
                />
            </div>

            <div>
                <label className="block text-gray-700 mb-1">
                    İl {required && <span className="text-red-500">*</span>}
                </label>
                <StateSelect
                    countryId={countryId}
                    value={stateId}
                    onChange={onStateChange}
                    required={required}
                    disabled={disabled}
                    id={`${prefix}state_select`}
                />
            </div>

            <div>
                <label className="block text-gray-700 mb-1">
                    İlçe {required && <span className="text-red-500">*</span>}
                </label>
                <CitySelect
                    countryId={countryId}
                    stateId={stateId}
                    value={cityId}
                    onChange={onCityChange}
                    required={required}
                    disabled={disabled}
                    id={`${prefix}city_select`}
                />
            </div>
        </div>
    );
}
