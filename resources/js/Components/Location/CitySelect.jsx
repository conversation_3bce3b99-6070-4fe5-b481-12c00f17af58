import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import { debounce } from "lodash";

export default function CitySelect({
    stateId,
    countryId,
    value,
    onChange,
    className = "",
    required = false,
    disabled = false,
    id = "city_select",
}) {
    const [cities, setCities] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [search, setSearch] = useState("");
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [totalCities, setTotalCities] = useState(0);
    const [initialized, setInitialized] = useState(false);

    // İlçeleri getir
    const fetchCities = async (
        pageNum = 1,
        searchTerm = search,
        reset = false
    ) => {
        // Eğer il seçilmemişse, ilçe listesini temizle
        if (!stateId) {
            setCities([]);
            setHasMore(false);
            return;
        }

        try {
            setLoading(true);
            setError(null);

            // Türkiye için optimize edilmiş endpoint kullan
            const endpoint =
                countryId === 225
                    ? `/api/locations/turkiye/states/${stateId}/cities`
                    : `/api/locations/states/${stateId}/cities`;

            console.log(
                `Fetching cities with search: "${searchTerm}", page: ${pageNum}`
            );

            const response = await axios.get(endpoint, {
                params: {
                    page: pageNum,
                    per_page: 100,
                    search: searchTerm,
                },
            });

            if (response.data && response.data.data) {
                if (reset) {
                    setCities(response.data.data);
                } else {
                    setCities((prev) => [...prev, ...response.data.data]);
                }

                setTotalCities(response.data.total);
                setHasMore(
                    response.data.current_page < response.data.last_page
                );
                setPage(response.data.current_page + 1);
                setInitialized(true);
            } else {
                console.warn("İlçe verisi bulunamadı veya boş.");
                setHasMore(false);

                if (reset) {
                    setCities([]);
                }
            }

            setLoading(false);
        } catch (err) {
            console.error("İlçeler yüklenirken hata oluştu:", err);
            setError("İlçeler yüklenirken bir hata oluştu.");
            setLoading(false);
            setHasMore(false);

            if (reset) {
                setCities([]);
            }
        }
    };

    // Arama işlemi için debounce fonksiyonu
    const handleSearchChange = (e) => {
        const value = e.target.value;
        setSearch(value);

        // Arama metnini değiştirdiğimizde state'i sıfırlayalım
        setPage(1);
        setLoading(true);

        // Debounce için timeout kullanıyoruz
        clearTimeout(window.searchTimeout);
        window.searchTimeout = setTimeout(() => {
            setCities([]);
            fetchCities(1, value, true);
        }, 300);
    };

    // Arama metnini temizleme işlevi
    const clearSearch = () => {
        setSearch("");
        setPage(1);
        setCities([]);
        setLoading(true);
        fetchCities(1, "", true);
    };

    // İl değiştiğinde, ilçeleri yeniden yükle
    useEffect(() => {
        setSearch("");
        setPage(1);
        setCities([]);
        setHasMore(true);
        setInitialized(false);

        if (stateId) {
            fetchCities(1, "", true);
        }

        // İl değiştiğinde, seçili ilçeyi sıfırla
        onChange("");

        // Component unmount olduğunda timeout'u temizle
        return () => {
            clearTimeout(window.searchTimeout);
        };
    }, [stateId, countryId]);

    return (
        <div>
            {stateId && (
                <div className="mb-2 relative">
                    <input
                        type="text"
                        placeholder="İlçe ara..."
                        value={search}
                        onChange={handleSearchChange}
                        className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 pr-10"
                        disabled={disabled || !stateId}
                    />
                    {search && (
                        <button
                            type="button"
                            onClick={clearSearch}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                            title="Aramayı temizle"
                        >
                            ✕
                        </button>
                    )}
                </div>
            )}

            <select
                id={id}
                value={value || ""}
                onChange={(e) =>
                    onChange(e.target.value ? parseInt(e.target.value) : "")
                }
                className={`w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 ${className}`}
                disabled={disabled || loading || !stateId}
                required={required}
            >
                <option value="">İlçe Seçiniz</option>
                {cities.map((city) => (
                    <option key={city.id} value={city.id}>
                        {city.name}
                    </option>
                ))}
            </select>

            {error && <p className="text-sm text-red-500 mt-1">{error}</p>}

            {loading && (
                <div className="flex items-center justify-center py-2">
                    <svg
                        className="animate-spin h-5 w-5 text-blue-500"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                    >
                        <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                        ></circle>
                        <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                    </svg>
                    <span className="ml-2 text-sm text-gray-500">
                        Yükleniyor...
                    </span>
                </div>
            )}

            {initialized && hasMore && cities.length > 0 && !loading && (
                <button
                    type="button"
                    onClick={() => fetchCities(page)}
                    className="w-full text-sm text-blue-500 mt-1 py-1 border border-blue-200 rounded hover:bg-blue-50 focus:outline-none"
                >
                    Daha fazla ilçe yükle ({cities.length}/{totalCities})
                </button>
            )}

            {initialized && totalCities > 0 && (
                <p className="text-xs text-gray-500 mt-1">
                    {search ? `"${search}" için ` : ""}Toplam {totalCities}{" "}
                    ilçe, {cities.length} ilçe yüklendi
                </p>
            )}

            {initialized && totalCities === 0 && search && (
                <p className="text-xs text-red-500 mt-1">
                    "{search}" için sonuç bulunamadı
                </p>
            )}
        </div>
    );
}
