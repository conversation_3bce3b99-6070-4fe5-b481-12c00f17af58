import React, { useState, useEffect } from "react";
import axios from "axios";

export default function ShippingMethodSelector({
    countryId,
    stateId,
    cityId,
    postalCode,
    orderAmount,
    onSelect,
    initialMethodId = null,
    initialCost = 0,
}) {
    const [shippingMethods, setShippingMethods] = useState([]);
    const [loading, setLoading] = useState(false);
    const [selectedMethodId, setSelectedMethodId] = useState(initialMethodId);
    const [selectedCost, setSelectedCost] = useState(initialCost);

    // Adres bilgileri değiştiğinde kargo metodlarını getir
    useEffect(() => {
        if (!countryId) return;

        setLoading(true);
        
        axios.post("/api/shipping/available-methods", {
            country_id: countryId,
            state_id: stateId || null,
            city_id: cityId || null,
            postal_code: postalCode || null,
            order_amount: orderAmount || 0,
        })
        .then((response) => {
            if (response.data.success) {
                setShippingMethods(response.data.methods);
                
                // Eğer metodlar varsa ve seçili metod yoksa, ilk metodu seç
                if (response.data.methods.length > 0 && !selectedMethodId) {
                    const firstMethod = response.data.methods[0];
                    setSelectedMethodId(firstMethod.id);
                    setSelectedCost(firstMethod.cost);
                    
                    if (onSelect) {
                        onSelect(firstMethod.id, firstMethod.cost);
                    }
                }
            }
        })
        .catch((error) => {
            console.error("Kargo metodları alınırken hata oluştu:", error);
        })
        .finally(() => {
            setLoading(false);
        });
    }, [countryId, stateId, cityId, postalCode, orderAmount]);

    // Kargo metodu seçildiğinde
    const handleSelectMethod = (methodId, cost) => {
        setSelectedMethodId(methodId);
        setSelectedCost(cost);
        
        if (onSelect) {
            onSelect(methodId, cost);
        }
    };

    if (loading) {
        return <div className="mt-4">Kargo metodları yükleniyor...</div>;
    }

    if (shippingMethods.length === 0) {
        return (
            <div className="mt-4 text-red-500">
                Seçili adres için uygun kargo metodu bulunamadı.
            </div>
        );
    }

    return (
        <div className="mt-4">
            <h3 className="text-lg font-medium mb-2">Kargo Metodu Seçin</h3>
            <div className="space-y-2">
                {shippingMethods.map((method) => (
                    <div
                        key={method.id}
                        className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                            selectedMethodId === method.id
                                ? "border-blue-500 bg-blue-50"
                                : "border-gray-200 hover:border-blue-300"
                        }`}
                        onClick={() => handleSelectMethod(method.id, method.cost)}
                    >
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <input
                                    type="radio"
                                    name="shipping_method"
                                    id={`shipping_method_${method.id}`}
                                    value={method.id}
                                    checked={selectedMethodId === method.id}
                                    onChange={() => handleSelectMethod(method.id, method.cost)}
                                    className="mr-2"
                                />
                                <label
                                    htmlFor={`shipping_method_${method.id}`}
                                    className="cursor-pointer"
                                >
                                    <div className="font-medium">{method.name}</div>
                                    {method.description && (
                                        <div className="text-sm text-gray-500">
                                            {method.description}
                                        </div>
                                    )}
                                    {method.estimated_delivery_text && (
                                        <div className="text-sm text-gray-600 mt-1">
                                            {method.estimated_delivery_text}
                                        </div>
                                    )}
                                </label>
                            </div>
                            <div className="font-medium">
                                {method.formatted_cost}
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}
