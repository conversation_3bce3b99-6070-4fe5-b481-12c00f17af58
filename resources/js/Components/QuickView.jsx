import React, { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import axios from "axios";

export default function QuickView({ productId, isOpen, onClose }) {
    const [product, setProduct] = useState(null);
    const [variantAttributes, setVariantAttributes] = useState([]);
    const [variantOptions, setVariantOptions] = useState([]);
    const [defaultVariant, setDefaultVariant] = useState(null);

    const [quantity, setQuantity] = useState(1);
    const [selectedVariant, setSelectedVariant] = useState(null);
    const [selectedAttributes, setSelectedAttributes] = useState({});
    const [availableVariants, setAvailableVariants] = useState([]);
    const [currentPrice, setCurrentPrice] = useState(0);
    const [loading, setLoading] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    // Popup açıldığında ürün detaylarını yükle
    useEffect(() => {
        if (isOpen && productId) {
            setIsLoading(true);

            // Ürün detaylarını API'den al
            axios
                .get(`/products/${productId}/details`)
                .then((response) => {
                    const data = response.data;
                    setProduct(data.product);
                    setVariantAttributes(data.variantAttributes);
                    setVariantOptions(data.variantOptions);
                    setDefaultVariant(data.defaultVariant);

                    // Varsayılan değerleri ayarla
                    setQuantity(1);

                    if (data.defaultVariant) {
                        setSelectedVariant(data.defaultVariant);
                        setCurrentPrice(data.defaultVariant.price);
                        setSelectedAttributes(
                            data.defaultVariant.attribute_values || {}
                        );
                    } else {
                        setSelectedVariant(null);
                        setCurrentPrice(data.product.price);
                        setSelectedAttributes({});
                    }

                    setIsLoading(false);
                })
                .catch((error) => {
                    console.error("Ürün detayları alınamadı:", error);
                    toast.error("Ürün detayları yüklenirken bir hata oluştu");
                    setIsLoading(false);
                    onClose(); // Hata durumunda popup'ı kapat
                });
        }
    }, [isOpen, productId]);

    // Varyant seçimlerini izle ve uygun varyantı bul
    useEffect(() => {
        if (!product || !variantOptions || variantOptions.length === 0) return;

        // Seçilen özelliklere göre uygun varyantları filtrele
        const filteredVariants = variantOptions.filter((variant) => {
            // Tüm seçilen özellikler için kontrol yap
            for (const [code, value] of Object.entries(selectedAttributes)) {
                if (
                    !variant.attribute_values[code] ||
                    variant.attribute_values[code] !== value
                ) {
                    return false;
                }
            }
            return true;
        });

        setAvailableVariants(filteredVariants);

        // Eğer tam olarak bir varyant seçilmişse, onu aktif varyant olarak ayarla
        if (
            filteredVariants.length === 1 &&
            Object.keys(selectedAttributes).length === variantAttributes.length
        ) {
            setSelectedVariant(filteredVariants[0]);
            setCurrentPrice(filteredVariants[0].price);
        } else if (filteredVariants.length === 0) {
            // Eğer hiç varyant bulunamazsa, seçimi sıfırla
            setSelectedVariant(null);
            setCurrentPrice(product.price);
        } else {
            // Kısmi eşleşme durumunda varyant seçilmemiş olarak işaretle
            setSelectedVariant(null);
            setCurrentPrice(product.price);
        }
    }, [selectedAttributes, product, variantOptions, variantAttributes]);

    // Özellik seçimini güncelle
    const handleAttributeChange = (attributeCode, value) => {
        setSelectedAttributes((prev) => ({
            ...prev,
            [attributeCode]: value,
        }));
    };

    // Sepete ekle
    const addToCart = () => {
        if (loading) return;

        setLoading(true);

        const cartData = {
            product_id: product.id,
            quantity: quantity,
        };

        // Eğer varyant seçilmişse, varyant ID'sini ekle
        if (selectedVariant) {
            cartData.variant_id = selectedVariant.id;
        }

        // Sunucuya yük binmemesi için kısa bir gecikme ekle (500ms)
        setTimeout(() => {
            // Axios ile POST isteği gönder
            axios
                .post(route("cart.add"), cartData)
                .then((response) => {
                    toast.success("Ürün sepete eklendi");

                    // Sepet sayısını güncelle
                    if (
                        response.data &&
                        response.data.cartCount !== undefined
                    ) {
                        // Özel olay tetikle - Bu, tüm komponentlerin sepet sayısını güncellemesini sağlar
                        window.dispatchEvent(
                            new CustomEvent("cart-updated", {
                                detail: { cartCount: response.data.cartCount },
                            })
                        );
                    } else {
                        // Eğer response'da cartCount yoksa, API'den sepet sayısını al
                        import("@/Utils/cartUtils").then(
                            ({ updateCartCount }) => {
                                updateCartCount();
                            }
                        );
                    }

                    // Popup'ı kapat
                    onClose();
                    setLoading(false);
                })
                .catch((error) => {
                    toast.error("Ürün sepete eklenirken bir hata oluştu");
                    console.error("Sepete ekleme hatası:", error);
                    setLoading(false);
                });
        }, 500); // 500ms gecikme
    };

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
            <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto">
                {/* Kapatma butonu */}
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                        />
                    </svg>
                </button>

                {isLoading ? (
                    <div className="flex items-center justify-center p-12">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                ) : product ? (
                    <div className="flex flex-col md:flex-row">
                        {/* Ürün görseli */}
                        <div className="md:w-1/2 p-6">
                            <img
                                src={product.image || "/images/placeholder.png"}
                                alt={product.name}
                                className="w-full h-auto object-cover rounded-lg"
                            />
                        </div>

                        {/* Ürün bilgileri */}
                        <div className="md:w-1/2 p-6">
                            <h2 className="text-2xl font-bold mb-2">
                                {product.name}
                            </h2>

                            {product.category && (
                                <div className="text-sm text-gray-600 mb-4">
                                    {product.category.name}
                                </div>
                            )}

                            <div className="text-xl font-bold text-blue-600 mb-4">
                                {formatPrice(currentPrice)}
                                {selectedVariant &&
                                    selectedVariant.additional_price > 0 && (
                                        <span className="text-sm text-gray-500 ml-2">
                                            (Temel fiyat:{" "}
                                            {formatPrice(product.price)})
                                        </span>
                                    )}
                            </div>

                            <div className="mb-4">
                                <p className="text-gray-700">
                                    {product.description}
                                </p>
                            </div>

                            {/* Varyant seçenekleri */}
                            {variantAttributes &&
                                variantAttributes.length > 0 && (
                                    <div className="mb-6">
                                        <h3 className="text-lg font-semibold mb-3">
                                            Varyant Seçenekleri
                                        </h3>

                                        {variantAttributes.map((attribute) => (
                                            <div
                                                key={attribute.id}
                                                className="mb-4"
                                            >
                                                <label className="block text-gray-700 mb-2">
                                                    {attribute.name}:
                                                </label>
                                                <div className="flex flex-wrap gap-2">
                                                    {attribute.values.map(
                                                        (value) => (
                                                            <button
                                                                key={
                                                                    value.value
                                                                }
                                                                type="button"
                                                                onClick={() =>
                                                                    handleAttributeChange(
                                                                        attribute.code,
                                                                        value.value
                                                                    )
                                                                }
                                                                className={`px-3 py-2 border rounded-md ${
                                                                    selectedAttributes[
                                                                        attribute
                                                                            .code
                                                                    ] ===
                                                                    value.value
                                                                        ? "border-blue-500 bg-blue-50 text-blue-700"
                                                                        : "border-gray-300 hover:border-gray-400"
                                                                }`}
                                                            >
                                                                {value.label}
                                                            </button>
                                                        )
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}

                            {/* Stok durumu */}
                            <div className="mb-4">
                                <div className="flex items-center mb-4">
                                    <span className="text-gray-700 mr-3">
                                        Stok Durumu:
                                    </span>
                                    {selectedVariant ? (
                                        // Varyant seçilmişse varyantın stok durumunu göster
                                        selectedVariant.stock > 0 &&
                                        selectedVariant.status ===
                                            "in_stock" ? (
                                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                                                Stokta ({selectedVariant.stock}{" "}
                                                adet)
                                            </span>
                                        ) : (
                                            <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
                                                Stokta Yok
                                            </span>
                                        )
                                    ) : // Varyant seçilmemişse ürünün stok durumunu göster
                                    product.stock > 0 ? (
                                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                                            Stokta ({product.stock} adet)
                                        </span>
                                    ) : (
                                        <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
                                            Stokta Yok
                                        </span>
                                    )}
                                </div>

                                {/* Miktar seçici */}
                                {((selectedVariant &&
                                    selectedVariant.stock > 0 &&
                                    selectedVariant.status === "in_stock") ||
                                    (!selectedVariant &&
                                        product.stock > 0)) && (
                                    <div className="flex items-center mb-4">
                                        <span className="text-gray-700 mr-3">
                                            Adet:
                                        </span>
                                        <div className="flex items-center">
                                            <button
                                                onClick={() =>
                                                    setQuantity(
                                                        Math.max(
                                                            1,
                                                            quantity - 1
                                                        )
                                                    )
                                                }
                                                className="w-8 h-8 rounded-l border border-gray-300 flex items-center justify-center hover:bg-gray-100"
                                            >
                                                -
                                            </button>
                                            <div className="w-12 h-8 border-t border-b border-gray-300 flex items-center justify-center">
                                                {quantity}
                                            </div>
                                            <button
                                                onClick={() =>
                                                    setQuantity(
                                                        Math.min(
                                                            selectedVariant
                                                                ? selectedVariant.stock
                                                                : product.stock,
                                                            quantity + 1
                                                        )
                                                    )
                                                }
                                                className="w-8 h-8 rounded-r border border-gray-300 flex items-center justify-center hover:bg-gray-100"
                                            >
                                                +
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Sepete ekle butonu */}
                            <div className="flex flex-wrap gap-3">
                                <button
                                    onClick={addToCart}
                                    disabled={
                                        loading ||
                                        (selectedVariant &&
                                            (selectedVariant.stock <= 0 ||
                                                selectedVariant.status !==
                                                    "in_stock")) ||
                                        (!selectedVariant &&
                                            product.stock <= 0) ||
                                        (variantAttributes &&
                                            variantAttributes.length > 0 &&
                                            !selectedVariant)
                                    }
                                    className={`w-full py-3 px-4 rounded-lg font-semibold ${
                                        loading
                                            ? "bg-gray-400 text-white cursor-wait"
                                            : (selectedVariant &&
                                                  selectedVariant.stock > 0 &&
                                                  selectedVariant.status ===
                                                      "in_stock") ||
                                              (!selectedVariant &&
                                                  product.stock > 0 &&
                                                  (!variantAttributes ||
                                                      variantAttributes.length ===
                                                          0))
                                            ? "bg-blue-600 text-white hover:bg-blue-700"
                                            : "bg-gray-300 text-gray-500 cursor-not-allowed"
                                    }`}
                                >
                                    {loading
                                        ? "İşleniyor..."
                                        : variantAttributes &&
                                          variantAttributes.length > 0 &&
                                          !selectedVariant
                                        ? "Lütfen varyant seçiniz"
                                        : (selectedVariant &&
                                              selectedVariant.stock > 0 &&
                                              selectedVariant.status ===
                                                  "in_stock") ||
                                          (!selectedVariant &&
                                              product.stock > 0)
                                        ? "Sepete Ekle"
                                        : "Stokta Yok"}
                                </button>

                                {/* Ürün detayına git butonu */}
                                <a
                                    href={`/products/${product.id}`}
                                    className="w-full text-center py-3 px-4 rounded-lg font-semibold border border-blue-600 text-blue-600 hover:bg-blue-50"
                                >
                                    Ürün Detayına Git
                                </a>
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="p-6 text-center">
                        <p className="text-gray-500">
                            Ürün bilgileri yüklenemedi.
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
}
