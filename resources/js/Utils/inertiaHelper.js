import { router } from "@inertiajs/react";
import axios from "axios";

/**
 * Inertia.js router.delete metodunu düzenleyen yardımcı fonksiyon
 * Bu fonksiyon, DELETE isteklerini POST isteği olarak gönderir ve method spoofing kullanır
 *
 * @param {string} url - Silme işlemi için URL
 * @param {object} options - Inertia.js router.delete metoduna geçirilen seçenekler
 */
export const safeDelete = (url, options = {}) => {
    // POST isteği olarak gönder ve _method parametresi ekle
    return router.post(
        url,
        {
            _method: "DELETE",
        },
        options
    );
};

/**
 * Axios ile DELETE isteği gönderen yardımcı fonksiyon
 * Bu fonksiyon, DELETE isteklerini POST isteği olarak gönderir ve method spoofing kullanır
 *
 * @param {string} url - Silme işlemi için URL
 * @param {object} options - Axios'a geçirilen ek seçenekler
 * @returns {Promise} - Axios isteği sonucu
 */
export const axiosDelete = (url, options = {}) => {
    // CSRF token'ı al
    const csrfToken = document.querySelector(
        'meta[name="csrf-token"]'
    )?.content;

    // POST isteği olarak gönder ve _method parametresi ekle
    return axios.post(
        url,
        {
            _method: "DELETE",
            ...options,
        },
        {
            headers: {
                "X-CSRF-TOKEN": csrfToken,
                "X-Requested-With": "XMLHttpRequest",
                "Content-Type": "application/json",
                Accept: "application/json",
            },
        }
    );
};
