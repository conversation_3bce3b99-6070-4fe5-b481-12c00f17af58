import axios from "axios";
import { toast } from "react-hot-toast";

/**
 * Sepet sayısını güncelleme fonksiyonu
 * Bu fonksiyon, sepet sayısını API'den alır ve DOM'da günceller
 */
export const updateCartCount = async () => {
    try {
        const response = await axios.get("/api/cart/count");
        const count = response.data.count;

        // Özel olay tetikle - Bu, tüm komponentlerin sepet sayısını güncellemesini sağlar
        window.dispatchEvent(
            new CustomEvent("cart-updated", {
                detail: { cartCount: count },
            })
        );

        // Ayrıca DOM'u doğrudan güncelle (yedek yöntem)
        const cartCountElement = document.getElementById("cart-count");
        if (cartCountElement) {
            cartCountElement.textContent = count;

            // Eğer sepet boşsa gizle, doluysa göster
            if (count > 0) {
                cartCountElement.classList.remove("hidden");
            } else {
                cartCountElement.classList.add("hidden");
            }
        }

        return count;
    } catch (error) {
        console.error("Sepet sayısı güncelleme hatası:", error);

        // Hata durumunda da bir olay tetikle, böylece komponentler hata durumunu işleyebilir
        window.dispatchEvent(
            new CustomEvent("cart-update-error", {
                detail: { error: error.message },
            })
        );

        return null;
    }
};

/**
 * Sepete ürün ekleme fonksiyonu
 * @param {Object} productData - Ürün bilgileri (product_id, quantity, options, variant_id)
 * @returns {Promise} - İşlem sonucu
 */
export const addToCart = async (productData) => {
    try {
        const response = await axios.post(route("cart.add"), {
            ...productData,
            _token: document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        });

        // Sepet sayısını güncelle
        updateCartCount();

        toast.success(response.data.message || "Ürün sepete eklendi");
        return response.data;
    } catch (error) {
        const errorMessage =
            error.response?.data?.error ||
            "Ürün sepete eklenirken bir hata oluştu";
        toast.error(errorMessage);
        throw error;
    }
};

/**
 * Sepetten ürün çıkarma fonksiyonu
 * @param {number} cartItemId - Sepet öğesi ID
 * @returns {Promise} - İşlem sonucu
 */
export const removeFromCart = async (cartItemId) => {
    try {
        const formData = new FormData();
        formData.append("_method", "DELETE");
        formData.append(
            "_token",
            document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content")
        );

        const response = await axios.post(
            route("cart.remove", cartItemId),
            formData
        );

        // Sepet sayısını güncelle
        updateCartCount();

        toast.success(response.data.message || "Ürün sepetten çıkarıldı");
        return response.data;
    } catch (error) {
        const errorMessage =
            error.response?.data?.error ||
            "Ürün sepetten çıkarılırken bir hata oluştu";
        toast.error(errorMessage);
        throw error;
    }
};

/**
 * Sepet öğesi miktarını güncelleme fonksiyonu
 * @param {number} cartItemId - Sepet öğesi ID
 * @param {number} quantity - Yeni miktar
 * @returns {Promise} - İşlem sonucu
 */
export const updateCartItemQuantity = async (cartItemId, quantity) => {
    try {
        const response = await axios.put(route("cart.update", cartItemId), {
            quantity,
            _token: document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        });

        // Sepet sayısını güncelle
        updateCartCount();

        toast.success(response.data.message || "Sepet güncellendi");
        return response.data;
    } catch (error) {
        const errorMessage =
            error.response?.data?.error ||
            "Sepet güncellenirken bir hata oluştu";
        toast.error(errorMessage);
        throw error;
    }
};

/**
 * Sepeti boşaltma fonksiyonu
 * @returns {Promise} - İşlem sonucu
 */
export const clearCart = async () => {
    try {
        const response = await axios.post(route("cart.clear"), {
            _token: document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        });

        // Sepet sayısını güncelle
        updateCartCount();

        toast.success(response.data.message || "Sepet boşaltıldı");
        return response.data;
    } catch (error) {
        const errorMessage =
            error.response?.data?.error ||
            "Sepet boşaltılırken bir hata oluştu";
        toast.error(errorMessage);
        throw error;
    }
};

/**
 * Kupon uygulama fonksiyonu
 * @param {string} couponCode - Kupon kodu
 * @returns {Promise} - İşlem sonucu
 */
export const applyCoupon = async (couponCode) => {
    try {
        // CSRF token'ı header olarak ekle
        const token = document
            .querySelector('meta[name="csrf-token"]')
            .getAttribute("content");

        const response = await axios.post(
            route("cart.coupon.apply"),
            {
                coupon_code: couponCode,
            },
            {
                headers: {
                    "X-CSRF-TOKEN": token,
                },
            }
        );

        toast.success(response.data.message || "Kupon uygulandı");
        return response.data;
    } catch (error) {
        const errorMessage =
            error.response?.data?.error || "Kupon uygulanırken bir hata oluştu";
        toast.error(errorMessage);
        throw error;
    }
};

/**
 * Kuponu kaldırma fonksiyonu
 * @returns {Promise} - İşlem sonucu
 */
export const removeCoupon = async () => {
    try {
        // CSRF token'ı header olarak ekle
        const token = document
            .querySelector('meta[name="csrf-token"]')
            .getAttribute("content");

        const response = await axios.delete(route("cart.coupon.remove"), {
            headers: {
                "X-CSRF-TOKEN": token,
            },
        });

        toast.success(response.data.message || "Kupon kaldırıldı");
        return response.data;
    } catch (error) {
        const errorMessage =
            error.response?.data?.error ||
            "Kupon kaldırılırken bir hata oluştu";
        toast.error(errorMessage);
        throw error;
    }
};

/**
 * Fiyat formatla
 * @param {number} price - Fiyat
 * @returns {string} - Formatlanmış fiyat
 */
export const formatPrice = (price) => {
    return new Intl.NumberFormat("tr-TR", {
        style: "currency",
        currency: "TRY",
    }).format(price);
};
