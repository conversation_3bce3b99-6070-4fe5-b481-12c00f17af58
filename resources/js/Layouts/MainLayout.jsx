import React, { useState, useEffect, useRef } from "react";
import { Link, usePage, router } from "@inertiajs/react";
import { Toaster } from "react-hot-toast";
import axios from "axios";
import { updateCartCount } from "@/Utils/cartUtils";
import SearchAutocomplete from "@/Components/SearchAutocomplete";

export default function MainLayout({ children }) {
    // Sayfa props'larını al
    const { auth, flash } = usePage().props;

    // State tanımlamaları
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const [cartCount, setCartCount] = useState(0);
    const [userMenuOpen, setUserMenuOpen] = useState(false);
    const userMenuRef = useRef(null);

    // Sayfa yüklendiğinde ve sayfa geçişlerinde sepet sayısını al
    useEffect(() => {
        const fetchCartCount = async () => {
            try {
                const response = await fetch("/api/cart/count");
                const data = await response.json();
                setCartCount(data.count);
            } catch (error) {
                console.error("Sepet sayısı alınamadı:", error);
            }
        };

        // İlk yüklemede sepet sayısını al
        fetchCartCount();

        // Sepete ürün eklendiğinde sepet sayısını güncelle
        const handleCartUpdate = (event) => {
            if (event.detail && event.detail.cartCount !== undefined) {
                setCartCount(event.detail.cartCount);
            } else {
                // Eğer cartCount belirtilmemişse, API'den al
                fetchCartCount();
            }
        };

        // Özel olay dinleyicisi ekle
        window.addEventListener("cart-updated", handleCartUpdate);

        // Sayfa değişikliklerini izlemek için document.addEventListener kullanıyoruz
        // Bu, Inertia.js'in router.on/off metodlarına bağımlılığı ortadan kaldırır
        const handleVisibilityChange = () => {
            // Sayfa görünür olduğunda sepet sayısını güncelle
            if (document.visibilityState === "visible") {
                fetchCartCount();
            }
        };

        // Sayfa görünürlük değişikliklerini dinle
        document.addEventListener("visibilitychange", handleVisibilityChange);

        // Temizlik fonksiyonu
        return () => {
            window.removeEventListener("cart-updated", handleCartUpdate);
            document.removeEventListener(
                "visibilitychange",
                handleVisibilityChange
            );
        };
    }, []);

    // Flash mesajlarını izle
    useEffect(() => {
        if (flash && flash.cartCount !== undefined) {
            setCartCount(flash.cartCount);
        }
    }, [flash]);

    // Kullanıcı menüsü dışına tıklandığında kapat
    useEffect(() => {
        function handleClickOutside(event) {
            if (
                userMenuRef.current &&
                !userMenuRef.current.contains(event.target)
            ) {
                setUserMenuOpen(false);
            }
        }

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    return (
        <div className="min-h-screen bg-gray-100">
            <Toaster position="bottom-right" />

            {/* Header */}
            <header className="bg-white shadow">
                <div className="container mx-auto px-4">
                    <div className="flex justify-between items-center py-4">
                        {/* Logo */}
                        <Link
                            href="/"
                            className="text-xl font-bold text-blue-600"
                        >
                            E-Ticaret
                        </Link>

                        {/* Desktop Navigation */}
                        <nav className="hidden md:flex items-center space-x-6">
                            <a
                                href="/"
                                className="text-gray-700 hover:text-blue-600"
                            >
                                Ana Sayfa
                            </a>
                            <a
                                href="/products"
                                className="text-gray-700 hover:text-blue-600"
                            >
                                Ürünler
                            </a>
                            <a
                                href="/categories"
                                className="text-gray-700 hover:text-blue-600"
                            >
                                Kategoriler
                            </a>
                            <a
                                href="/contact"
                                className="text-gray-700 hover:text-blue-600"
                            >
                                İletişim
                            </a>
                            <Link
                                href={route("orders.track")}
                                className="text-gray-700 hover:text-blue-600"
                            >
                                Sipariş Takip
                            </Link>
                        </nav>

                        {/* Search Bar */}
                        <div className="hidden md:block w-1/3">
                            <SearchAutocomplete placeholder="Ürün, kategori ara..." />
                        </div>

                        {/* User Menu & Cart */}
                        <div className="flex items-center space-x-4">
                            <Link
                                href="/cart"
                                className="text-gray-700 hover:text-blue-600 relative"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-6 w-6"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                                    />
                                </svg>
                                <span
                                    id="cart-count"
                                    className={`absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center ${
                                        cartCount > 0 ? "" : "hidden"
                                    }`}
                                >
                                    {cartCount}
                                </span>
                            </Link>

                            {auth && auth.user ? (
                                <div className="relative" ref={userMenuRef}>
                                    <button
                                        className="flex items-center text-gray-700 hover:text-blue-600"
                                        onClick={() =>
                                            setUserMenuOpen(!userMenuOpen)
                                        }
                                    >
                                        <span className="mr-1">
                                            {auth.user.name}
                                        </span>
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            className="h-4 w-4"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M19 9l-7 7-7-7"
                                            />
                                        </svg>
                                    </button>
                                    {userMenuOpen && (
                                        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                                            <Link
                                                href="/customer/dashboard"
                                                className="block px-4 py-2 text-gray-700 hover:bg-gray-100"
                                            >
                                                Hesabım
                                            </Link>
                                            <Link
                                                href="/customer/orders"
                                                className="block px-4 py-2 text-gray-700 hover:bg-gray-100"
                                            >
                                                Siparişlerim
                                            </Link>
                                            <Link
                                                href={route("customer.logout")}
                                                method="post"
                                                as="button"
                                                className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                                            >
                                                Çıkış Yap
                                            </Link>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="flex items-center space-x-2">
                                    <Link
                                        href={route("customer.login")}
                                        className="text-gray-700 hover:text-blue-600"
                                    >
                                        Giriş Yap
                                    </Link>
                                    <span className="text-gray-300">|</span>
                                    <Link
                                        href={route("customer.register")}
                                        className="text-gray-700 hover:text-blue-600"
                                    >
                                        Kayıt Ol
                                    </Link>
                                </div>
                            )}

                            {/* Mobile Menu Button */}
                            <button
                                className="md:hidden text-gray-700 hover:text-blue-600"
                                onClick={() =>
                                    setMobileMenuOpen(!mobileMenuOpen)
                                }
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-6 w-6"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M4 6h16M4 12h16M4 18h16"
                                    />
                                </svg>
                            </button>
                        </div>
                    </div>

                    {/* Mobile Navigation */}
                    {mobileMenuOpen && (
                        <nav className="md:hidden py-4 border-t">
                            {/* Mobile Search */}
                            <div className="py-2 mb-2">
                                <SearchAutocomplete placeholder="Ürün, kategori ara..." />
                            </div>

                            <a
                                href="/"
                                className="block py-2 text-gray-700 hover:text-blue-600"
                            >
                                Ana Sayfa
                            </a>
                            <a
                                href="/products"
                                className="block py-2 text-gray-700 hover:text-blue-600"
                            >
                                Ürünler
                            </a>
                            <a
                                href="/categories"
                                className="block py-2 text-gray-700 hover:text-blue-600"
                            >
                                Kategoriler
                            </a>
                            <a
                                href="/contact"
                                className="block py-2 text-gray-700 hover:text-blue-600"
                            >
                                İletişim
                            </a>
                            <Link
                                href={route("orders.track")}
                                className="block py-2 text-gray-700 hover:text-blue-600"
                            >
                                Sipariş Takip
                            </Link>
                        </nav>
                    )}
                </div>
            </header>

            {/* Main Content */}
            <main>{children}</main>

            {/* Footer */}
            <footer className="bg-gray-800 text-white py-8">
                <div className="container mx-auto px-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div>
                            <h3 className="text-lg font-semibold mb-4">
                                E-Ticaret
                            </h3>
                            <p className="text-gray-400">
                                Kaliteli ürünler, uygun fiyatlar ve hızlı
                                teslimat ile hizmetinizdeyiz.
                            </p>
                        </div>

                        <div>
                            <h3 className="text-lg font-semibold mb-4">
                                Hızlı Erişim
                            </h3>
                            <ul className="space-y-2">
                                <li>
                                    <Link
                                        href="/"
                                        className="text-gray-400 hover:text-white"
                                    >
                                        Ana Sayfa
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/products"
                                        className="text-gray-400 hover:text-white"
                                    >
                                        Ürünler
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/categories"
                                        className="text-gray-400 hover:text-white"
                                    >
                                        Kategoriler
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/contact"
                                        className="text-gray-400 hover:text-white"
                                    >
                                        İletişim
                                    </Link>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h3 className="text-lg font-semibold mb-4">
                                Yardım
                            </h3>
                            <ul className="space-y-2">
                                <li>
                                    <Link
                                        href={route("orders.track")}
                                        className="text-gray-400 hover:text-white"
                                    >
                                        Sipariş Takip
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/faq"
                                        className="text-gray-400 hover:text-white"
                                    >
                                        Sıkça Sorulan Sorular
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/shipping"
                                        className="text-gray-400 hover:text-white"
                                    >
                                        Kargo Bilgileri
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/returns"
                                        className="text-gray-400 hover:text-white"
                                    >
                                        İade Koşulları
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/privacy"
                                        className="text-gray-400 hover:text-white"
                                    >
                                        Gizlilik Politikası
                                    </Link>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h3 className="text-lg font-semibold mb-4">
                                İletişim
                            </h3>
                            <ul className="space-y-2 text-gray-400">
                                <li className="flex items-start">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-5 w-5 mr-2 mt-0.5"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                                        />
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                                        />
                                    </svg>
                                    <span>İstanbul, Türkiye</span>
                                </li>
                                <li className="flex items-start">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-5 w-5 mr-2 mt-0.5"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                        />
                                    </svg>
                                    <span><EMAIL></span>
                                </li>
                                <li className="flex items-start">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-5 w-5 mr-2 mt-0.5"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                        />
                                    </svg>
                                    <span>+90 212 123 4567</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div className="border-t border-gray-700 mt-8 pt-6 text-center text-gray-400">
                        <p>
                            &copy; {new Date().getFullYear()} E-Ticaret. Tüm
                            hakları saklıdır.
                        </p>
                    </div>
                </div>
            </footer>
        </div>
    );
}
