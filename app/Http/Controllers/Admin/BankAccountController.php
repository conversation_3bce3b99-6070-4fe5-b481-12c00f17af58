<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BankAccount;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BankAccountController extends Controller
{
    /**
     * Banka hesapları listesi
     */
    public function index()
    {
        $bankAccounts = BankAccount::orderBy('bank_name')->get();

        return Inertia::render('Admin/BankAccounts/Index', [
            'bankAccounts' => $bankAccounts,
        ]);
    }

    /**
     * Yeni banka hesabı oluşturma formu
     */
    public function create()
    {
        return Inertia::render('Admin/BankAccounts/Create');
    }

    /**
     * Yeni banka hesabı kaydet
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'bank_name' => 'required|string|max:255',
            'account_name' => 'required|string|max:255',
            'account_number' => 'nullable|string|max:50',
            'iban' => 'required|string|max:50',
            'branch_code' => 'nullable|string|max:20',
            'branch_name' => 'nullable|string|max:255',
            'swift_code' => 'nullable|string|max:20',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        BankAccount::create($validated);

        return redirect()->route('admin.bank-accounts.index')
            ->with('success', 'Banka hesabı başarıyla oluşturuldu.');
    }

    /**
     * Banka hesabı düzenleme formu
     */
    public function edit(BankAccount $bankAccount)
    {
        return Inertia::render('Admin/BankAccounts/Edit', [
            'bankAccount' => $bankAccount,
        ]);
    }

    /**
     * Banka hesabını güncelle
     */
    public function update(Request $request, BankAccount $bankAccount)
    {
        $validated = $request->validate([
            'bank_name' => 'required|string|max:255',
            'account_name' => 'required|string|max:255',
            'account_number' => 'nullable|string|max:50',
            'iban' => 'required|string|max:50',
            'branch_code' => 'nullable|string|max:20',
            'branch_name' => 'nullable|string|max:255',
            'swift_code' => 'nullable|string|max:20',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        $bankAccount->update($validated);

        return redirect()->route('admin.bank-accounts.index')
            ->with('success', 'Banka hesabı başarıyla güncellendi.');
    }

    /**
     * Banka hesabını sil
     */
    public function destroy(BankAccount $bankAccount)
    {
        // Banka hesabının siparişlerde kullanılıp kullanılmadığını kontrol et
        if ($bankAccount->orders()->count() > 0) {
            return redirect()->route('admin.bank-accounts.index')
                ->with('error', 'Bu banka hesabı siparişlerde kullanıldığı için silinemez.');
        }

        $bankAccount->delete();

        return redirect()->route('admin.bank-accounts.index')
            ->with('success', 'Banka hesabı başarıyla silindi.');
    }

    /**
     * Banka hesabının durumunu değiştir
     */
    public function toggleStatus(BankAccount $bankAccount)
    {
        $bankAccount->is_active = !$bankAccount->is_active;
        $bankAccount->save();

        return redirect()->route('admin.bank-accounts.index')
            ->with('success', 'Banka hesabı durumu başarıyla değiştirildi.');
    }
}
