<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\City;
use App\Models\Country;
use App\Models\ShippingZone;
use App\Models\ShippingZoneLocation;
use App\Models\State;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ShippingZoneController extends Controller
{
    /**
     * Kargo bölgeleri listesini göster
     */
    public function index()
    {
        $zones = ShippingZone::orderBy('order')
            ->withCount('locations')
            ->withCount('methods')
            ->get();

        return Inertia::render('Admin/Shipping/Zones/Index', [
            'zones' => $zones,
        ]);
    }

    /**
     * Yeni kargo bölgesi oluşturma formunu göster
     */
    public function create()
    {
        return Inertia::render('Admin/Shipping/Zones/Create');
    }

    /**
     * Yeni kargo bölgesi kaydet
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'order' => 'integer|min:0',
        ]);

        ShippingZone::create($validated);

        return redirect()->route('admin.shipping.zones.index')
            ->with('success', 'Kargo bölgesi başarıyla oluşturuldu.');
    }

    /**
     * Kargo bölgesi düzenleme formunu göster
     */
    public function edit(ShippingZone $zone)
    {
        return Inertia::render('Admin/Shipping/Zones/Edit', [
            'zone' => $zone,
        ]);
    }

    /**
     * Kargo bölgesini güncelle
     */
    public function update(Request $request, ShippingZone $zone)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'order' => 'integer|min:0',
        ]);

        $zone->update($validated);

        return redirect()->route('admin.shipping.zones.index')
            ->with('success', 'Kargo bölgesi başarıyla güncellendi.');
    }

    /**
     * Kargo bölgesini sil
     */
    public function destroy(ShippingZone $zone)
    {
        $zone->delete();

        if (request()->wantsJson() || request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Kargo bölgesi başarıyla silindi.'
            ]);
        }

        return redirect()->route('admin.shipping.zones.index')
            ->with('success', 'Kargo bölgesi başarıyla silindi.');
    }

    /**
     * Kargo bölgesi lokasyonlarını göster
     */
    public function locations(ShippingZone $zone)
    {
        // Lokasyonları yükleyelim
        $locationsQuery = ShippingZoneLocation::where('zone_id', $zone->id);
        $locations = $locationsQuery->get();

        // Lokasyonları manuel olarak işleyelim
        $processedLocations = [];
        foreach ($locations as $location) {
            $locationData = $location->toArray();

            // Lokasyon adını manuel olarak belirleyelim
            if (!empty($location->location_name)) {
                $locationData['location_name'] = $location->location_name;
            } else {
                // Lokasyon türüne göre ilgili modeli bulalım
                if ($location->location_type === 'country') {
                    $relatedModel = Country::find($location->location_id);
                    $locationData['location_name'] = $relatedModel ? $relatedModel->name : 'Bilinmeyen Ülke';
                } elseif ($location->location_type === 'state') {
                    $relatedModel = State::find($location->location_id);
                    $locationData['location_name'] = $relatedModel ? $relatedModel->name : 'Bilinmeyen Eyalet/İl';
                } elseif ($location->location_type === 'city') {
                    $relatedModel = City::find($location->location_id);
                    $locationData['location_name'] = $relatedModel ? $relatedModel->name : 'Bilinmeyen Şehir/İlçe';
                } else {
                    $locationData['location_name'] = $location->location_code ?? 'Bilinmeyen Lokasyon';
                }
            }

            $processedLocations[] = $locationData;
        }

        $countries = Country::where('is_active', true)
            ->orderBy('name')
            ->get();

        return Inertia::render('Admin/Shipping/Zones/Locations', [
            'zone' => $zone,
            'countries' => $countries,
            'locations' => $processedLocations,
        ]);
    }

    /**
     * Kargo bölgesine lokasyon ekle
     */
    public function addLocation(Request $request, ShippingZone $zone)
    {
        $validated = $request->validate([
            'location_type' => 'required|in:country,state,city,postal_code',
            'location_id' => 'required_unless:location_type,postal_code|nullable|integer',
            'location_code' => 'required_if:location_type,postal_code|nullable|string|max:20',
            'location_name' => 'nullable|string|max:255',
        ]);

        // Lokasyon adını otomatik olarak doldur
        if (empty($validated['location_name'])) {
            if ($validated['location_type'] === 'country' && !empty($validated['location_id'])) {
                $country = Country::find($validated['location_id']);
                if ($country) {
                    $validated['location_name'] = $country->name;
                }
            } elseif ($validated['location_type'] === 'state' && !empty($validated['location_id'])) {
                $state = State::find($validated['location_id']);
                if ($state) {
                    $validated['location_name'] = $state->name;
                }
            }
        }

        $zone->locations()->create($validated);

        return redirect()->route('admin.shipping.zones.locations', $zone->id)
            ->with('success', 'Lokasyon başarıyla eklendi.');
    }

    /**
     * Kargo bölgesinden lokasyon kaldır
     */
    public function removeLocation(ShippingZone $zone, ShippingZoneLocation $location)
    {
        if ($location->zone_id !== $zone->id) {
            if (request()->wantsJson() || request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lokasyon bulunamadı.'
                ], 404);
            }

            abort(404);
        }

        $location->delete();

        if (request()->wantsJson() || request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Lokasyon başarıyla kaldırıldı.'
            ]);
        }

        return redirect()->route('admin.shipping.zones.locations', $zone->id)
            ->with('success', 'Lokasyon başarıyla kaldırıldı.');
    }
}
