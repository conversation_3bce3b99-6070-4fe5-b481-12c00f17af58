<?php

namespace App\Http\Controllers;

use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;

class CustomerAuthController extends Controller
{
    /**
     * Müşteri giriş formunu göster
     */
    public function showLoginForm(Request $request)
    {
        // Eğer kullanıcı zaten giriş yapmışsa, dashboard'a yönlendir
        if (Auth::guard('customer')->check()) {
            return redirect()->route('customer.dashboard');
        }

        // Referrer URL'i session'a kaydet (eğer varsa ve aynı site içindeyse)
        $referrer = $request->headers->get('referer');
        if ($referrer && str_starts_with($referrer, config('app.url'))) {
            session(['url.intended' => $referrer]);
        }

        // Sepetten gelip gelmediğini kontrol et
        $fromCart = false;
        if ($referrer && str_contains($referrer, '/cart')) {
            $fromCart = true;
        }

        // Redirect parametresi varsa onu da gönder
        $redirect = $request->query('redirect');

        return Inertia::render('Auth/Customer/Login', [
            'fromCart' => $fromCart,
            'redirect' => $redirect,
        ]);
    }

    /**
     * Müşteri giriş işlemi
     */
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        // Kullanıcının customer rolüne sahip olup olmadığını kontrol et
        $user = User::where('email', $credentials['email'])->first();
        $customerRole = Role::where('name', 'customer')->first();

        if (!$user || !$customerRole || !$user->roles->contains($customerRole->id)) {
            return back()->withErrors([
                'email' => 'Bu e-posta adresi ile müşteri hesabı bulunamadı.',
            ])->onlyInput('email');
        }

        if (Auth::guard('customer')->attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();

            // Redirect parametresi varsa oraya yönlendir
            if ($request->has('redirect')) {
                return redirect($request->input('redirect'));
            }

            // Eğer intended URL varsa oraya, yoksa dashboard'a yönlendir
            return redirect()->intended(route('customer.dashboard'));
        }

        return back()->withErrors([
            'email' => 'Girdiğiniz bilgiler kayıtlarımızla eşleşmiyor.',
        ])->onlyInput('email');
    }

    /**
     * Müşteri kayıt formunu göster
     */
    public function showRegisterForm()
    {
        return Inertia::render('Auth/Customer/Register');
    }

    /**
     * Müşteri kayıt işlemi
     */
    public function register(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        // Kullanıcı oluştur
        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
        ]);

        // Customer rolünü ata
        $customerRole = Role::where('name', 'customer')->first();
        if ($customerRole) {
            $user->roles()->attach($customerRole->id);
        }

        Auth::login($user);

        return redirect('/')->with('success', 'Kayıt işleminiz başarıyla tamamlandı.');
    }

    /**
     * Müşteri çıkış işlemi
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/')->with('success', 'Başarıyla çıkış yaptınız.');
    }
}
