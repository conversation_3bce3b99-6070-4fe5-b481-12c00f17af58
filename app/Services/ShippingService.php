<?php

namespace App\Services;

use App\Models\Order;
use App\Models\ShippingCompany;
use App\Enums\OrderStatus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class ShippingService
{
    /**
     * Kargo şirketlerini listele
     * 
     * @param bool $onlyActive Sadece aktif olanları getir
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function listCompanies($onlyActive = true)
    {
        $query = ShippingCompany::query();
        
        if ($onlyActive) {
            $query->where('is_active', true);
        }
        
        return $query->orderBy('name')->get();
    }
    
    /**
     * Kargo şirketini kodu ile bul
     * 
     * @param string $code Kargo şirketi kodu
     * @return ShippingCompany|null
     */
    public function getCompanyByCode($code)
    {
        return ShippingCompany::where('code', $code)->first();
    }
    
    /**
     * Kargo takip URL'ini oluştur
     * 
     * @param string $companyCode Kargo şirketi kodu
     * @param string $trackingNumber Takip numarası
     * @return string|null
     */
    public function generateTrackingUrl($companyCode, $trackingNumber)
    {
        $company = $this->getCompanyByCode($companyCode);
        
        if (!$company || !$trackingNumber) {
            return null;
        }
        
        return $company->generateTrackingUrl($trackingNumber);
    }
    
    /**
     * Siparişi kargoya ver
     * 
     * @param Order $order Sipariş
     * @param string $companyCode Kargo şirketi kodu
     * @param string $trackingNumber Takip numarası
     * @param string|null $shippingDate Kargoya verilme tarihi
     * @param string|null $note Kargo notu
     * @param int|null $userId İşlemi yapan kullanıcı ID
     * @return Order
     */
    public function shipOrder(Order $order, $companyCode, $trackingNumber, $shippingDate = null, $note = null, $userId = null)
    {
        // Kargo şirketini kontrol et
        $company = $this->getCompanyByCode($companyCode);
        
        if (!$company) {
            throw new \Exception("Geçersiz kargo şirketi kodu: {$companyCode}");
        }
        
        // Sipariş durumunu güncelle
        $order->status = OrderStatus::SHIPPED;
        $order->shipping_company = $companyCode;
        $order->tracking_number = $trackingNumber;
        $order->shipping_date = $shippingDate ?: now();
        
        // Tahmini teslimat tarihini hesapla (varsayılan olarak 3 gün sonra)
        $order->estimated_delivery_date = now()->addDays(3);
        
        $order->save();
        
        // Sipariş notu ekle
        if ($note) {
            $order->addNote(
                $note,
                $userId,
                false, // Müşteriye görünür
                'shipping',
                true // Müşteriye bildirim gönder
            );
        } else {
            $order->addNote(
                "Siparişiniz kargoya verildi. Kargo Firması: {$company->name}, Takip No: {$trackingNumber}",
                $userId,
                false,
                'shipping',
                true
            );
        }
        
        // Kargo etiketini oluştur (eğer API entegrasyonu varsa)
        $this->generateShippingLabel($order);
        
        return $order;
    }
    
    /**
     * Kargo etiketini oluştur
     * 
     * @param Order $order Sipariş
     * @return string|null Etiket dosya yolu
     */
    public function generateShippingLabel(Order $order)
    {
        if (!$order->shipping_company || !$order->tracking_number) {
            return null;
        }
        
        $company = $this->getCompanyByCode($order->shipping_company);
        
        if (!$company || !$company->api_key || !$company->api_endpoint) {
            // API bilgileri yoksa etiket oluşturulamaz
            return null;
        }
        
        try {
            // Bu kısım kargo şirketinin API'sine göre özelleştirilmelidir
            // Şimdilik sahte bir etiket oluşturalım
            
            Log::info('Kargo etiketi oluşturuluyor', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'shipping_company' => $order->shipping_company,
                'tracking_number' => $order->tracking_number
            ]);
            
            // Gerçek entegrasyonda burada kargo şirketinin API'si çağrılacak
            // Örnek:
            /*
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $company->api_key,
                'Content-Type' => 'application/json',
            ])->post($company->api_endpoint . '/shipments/label', [
                'tracking_number' => $order->tracking_number,
                'sender' => [
                    'name' => 'Şirket Adı',
                    'address' => 'Şirket Adresi',
                    'city' => 'İstanbul',
                    'country' => 'Türkiye',
                    'phone' => '0212 123 45 67',
                ],
                'recipient' => [
                    'name' => $order->shipping_name,
                    'address' => $order->shipping_address,
                    'city' => $order->shipping_city,
                    'country' => $order->shipping_country,
                    'phone' => $order->shipping_phone,
                ],
                'package' => [
                    'weight' => 1, // kg
                    'dimensions' => [
                        'length' => 10, // cm
                        'width' => 10, // cm
                        'height' => 10, // cm
                    ],
                ],
            ]);
            
            if ($response->successful()) {
                $labelData = $response->json();
                
                if (isset($labelData['label_url'])) {
                    // Etiket URL'ini kaydet
                    $order->shipping_label_pdf = $labelData['label_url'];
                    $order->save();
                    
                    return $labelData['label_url'];
                }
            }
            */
            
            return null;
        } catch (\Exception $e) {
            Log::error('Kargo etiketi oluşturma hatası', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }
    
    /**
     * Kargo durumunu kontrol et
     * 
     * @param Order $order Sipariş
     * @return array Kargo durumu bilgileri
     */
    public function checkShippingStatus(Order $order)
    {
        if (!$order->shipping_company || !$order->tracking_number) {
            return [
                'success' => false,
                'message' => 'Kargo bilgileri bulunamadı',
                'status' => null,
                'details' => null,
            ];
        }
        
        $company = $this->getCompanyByCode($order->shipping_company);
        
        if (!$company || !$company->api_key || !$company->api_endpoint) {
            // API bilgileri yoksa durum kontrol edilemez
            return [
                'success' => false,
                'message' => 'Kargo şirketi API bilgileri bulunamadı',
                'status' => null,
                'details' => null,
            ];
        }
        
        try {
            // Bu kısım kargo şirketinin API'sine göre özelleştirilmelidir
            // Şimdilik sahte bir durum döndürelim
            
            Log::info('Kargo durumu kontrol ediliyor', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'shipping_company' => $order->shipping_company,
                'tracking_number' => $order->tracking_number
            ]);
            
            // Gerçek entegrasyonda burada kargo şirketinin API'si çağrılacak
            // Örnek:
            /*
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $company->api_key,
                'Content-Type' => 'application/json',
            ])->get($company->api_endpoint . '/shipments/track', [
                'tracking_number' => $order->tracking_number,
            ]);
            
            if ($response->successful()) {
                $trackingData = $response->json();
                
                if (isset($trackingData['status'])) {
                    // Kargo durumunu kontrol et
                    if ($trackingData['status'] === 'delivered' && !$order->isDelivered()) {
                        // Sipariş teslim edilmiş, durumu güncelle
                        $order->markAsDelivered(
                            $trackingData['delivery_date'] ?? now(),
                            'Kargo şirketi tarafından teslim edildi olarak işaretlendi.',
                            null // Sistem tarafından
                        );
                    }
                    
                    return [
                        'success' => true,
                        'message' => 'Kargo durumu başarıyla alındı',
                        'status' => $trackingData['status'],
                        'details' => $trackingData,
                    ];
                }
            }
            */
            
            // Sahte durum
            $statuses = ['in_transit', 'out_for_delivery', 'delivered'];
            $randomStatus = $statuses[array_rand($statuses)];
            
            // Eğer durum "delivered" ise ve sipariş henüz teslim edilmemiş olarak işaretlenmediyse
            if ($randomStatus === 'delivered' && !$order->isDelivered()) {
                // Sipariş durumunu güncelle
                $order->markAsDelivered(
                    now(),
                    'Kargo şirketi tarafından teslim edildi olarak işaretlendi.',
                    null // Sistem tarafından
                );
            }
            
            return [
                'success' => true,
                'message' => 'Kargo durumu başarıyla alındı (Demo)',
                'status' => $randomStatus,
                'details' => [
                    'company' => $company->name,
                    'tracking_number' => $order->tracking_number,
                    'status' => $randomStatus,
                    'status_text' => $randomStatus === 'in_transit' ? 'Taşıma Sürecinde' : 
                                    ($randomStatus === 'out_for_delivery' ? 'Dağıtıma Çıktı' : 'Teslim Edildi'),
                    'last_update' => now()->format('Y-m-d H:i:s'),
                    'estimated_delivery' => $order->estimated_delivery_date?->format('Y-m-d'),
                ],
            ];
            
        } catch (\Exception $e) {
            Log::error('Kargo durumu kontrol hatası', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => 'Kargo durumu kontrol edilirken bir hata oluştu: ' . $e->getMessage(),
                'status' => null,
                'details' => null,
            ];
        }
    }
    
    /**
     * Tüm bekleyen siparişlerin kargo durumlarını kontrol et
     * Bu metod zamanlanmış görev olarak çalıştırılabilir
     * 
     * @return array İşlem sonuçları
     */
    public function checkAllPendingShipments()
    {
        $results = [
            'total' => 0,
            'updated' => 0,
            'delivered' => 0,
            'errors' => 0,
        ];
        
        // Kargoya verilmiş ama teslim edilmemiş siparişleri bul
        $orders = Order::where('status', OrderStatus::SHIPPED)
                      ->whereNotNull('tracking_number')
                      ->whereNotNull('shipping_company')
                      ->get();
        
        $results['total'] = $orders->count();
        
        foreach ($orders as $order) {
            try {
                $status = $this->checkShippingStatus($order);
                
                if ($status['success']) {
                    $results['updated']++;
                    
                    if ($status['status'] === 'delivered') {
                        $results['delivered']++;
                    }
                } else {
                    $results['errors']++;
                }
            } catch (\Exception $e) {
                Log::error('Toplu kargo durumu kontrol hatası', [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'error' => $e->getMessage()
                ]);
                
                $results['errors']++;
            }
        }
        
        return $results;
    }
}
