<?php

namespace App\Services;

use App\Models\ShippingZone;
use App\Models\ShippingMethod;
use App\Models\ShippingZoneMethod;
use App\Models\ShippingZoneLocation;
use App\Models\ShippingCompany;
use App\Models\Cart;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class ShippingCalculationService
{
    /**
     * Belirtilen adres için uygun kargo metodlarını ve fiyatlarını hesaplar
     *
     * @param array $address Adres bilgileri (country_id, state_id, city_id, postal_code)
     * @param float $cartTotal Sepet toplamı
     * @param float $cartWeight Sepet ağırlığı (opsiyonel)
     * @param float $cartDesi Sepet desi değeri (opsiyonel)
     * @return array Uygun kargo metodları ve fiyatları
     */
    public function getAvailableShippingMethods(array $address, float $cartTotal, float $cartWeight = 0, float $cartDesi = 0): array
    {
        // Adres bilgilerini al
        $countryId = $address['country_id'] ?? null;
        $stateId = $address['state_id'] ?? null;
        $cityId = $address['city_id'] ?? null;
        $postalCode = $address['postal_code'] ?? null;

        // Uygun bölgeleri bul
        $matchingZones = $this->findMatchingZones($countryId, $stateId, $cityId, $postalCode);

        if (empty($matchingZones)) {
            return [];
        }

        // Bölgelere ait kargo metodlarını ve fiyatlarını hesapla
        $availableMethods = [];
        foreach ($matchingZones as $zone) {
            $zoneMethods = $this->getZoneShippingMethods($zone, $cartTotal, $cartWeight, $cartDesi);
            $availableMethods = array_merge($availableMethods, $zoneMethods);
        }

        // Kargo metodlarını şirketlere göre grupla
        $groupedMethods = [];
        foreach ($availableMethods as $method) {
            $companyId = $method['shipping_company_id'];
            if (!isset($groupedMethods[$companyId])) {
                $company = ShippingCompany::find($companyId);
                if (!$company) continue;

                $groupedMethods[$companyId] = [
                    'company' => [
                        'id' => $company->id,
                        'name' => $company->name,
                        'logo' => $company->logo,
                        'description' => $company->description,
                    ],
                    'methods' => []
                ];
            }

            $groupedMethods[$companyId]['methods'][] = $method;
        }

        return array_values($groupedMethods);
    }

    /**
     * Belirtilen adres için uygun kargo bölgelerini bulur
     */
    private function findMatchingZones($countryId, $stateId, $cityId, $postalCode): array
    {
        Log::info('Eşleşen bölgeler aranıyor', [
            'countryId' => $countryId,
            'stateId' => $stateId,
            'cityId' => $cityId,
            'postalCode' => $postalCode
        ]);

        // Aktif kargo bölgelerini al
        $zones = ShippingZone::where('is_active', true)
            ->orderBy('order')
            ->get();

        \Log::info('Aktif kargo bölgeleri', ['zones' => $zones->pluck('name', 'id')->toArray()]);

        $matchingZones = [];

        foreach ($zones as $zone) {
            // Bölgenin lokasyonlarını al
            $locations = ShippingZoneLocation::where('zone_id', $zone->id)->get();

            \Log::info("Bölge lokasyonları kontrol ediliyor", [
                'zone_id' => $zone->id,
                'zone_name' => $zone->name,
                'locations_count' => $locations->count()
            ]);

            // Eğer bölgede hiç lokasyon yoksa, bu bölge tüm dünya için geçerlidir
            if ($locations->isEmpty()) {
                \Log::info("Bölgede lokasyon yok, tüm dünya için geçerli", ['zone_id' => $zone->id]);
                $matchingZones[] = $zone;
                continue;
            }

            // Adres bilgilerine göre eşleşen lokasyon var mı kontrol et
            $matched = false;
            foreach ($locations as $location) {
                $isMatch = false;

                switch ($location->location_type) {
                    case 'country':
                        $isMatch = ($location->location_id == $countryId);
                        \Log::info("Ülke kontrolü", [
                            'location_id' => $location->location_id,
                            'countryId' => $countryId,
                            'isMatch' => $isMatch
                        ]);
                        break;
                    case 'state':
                        $isMatch = ($location->location_id == $stateId);
                        \Log::info("İl kontrolü", [
                            'location_id' => $location->location_id,
                            'stateId' => $stateId,
                            'isMatch' => $isMatch
                        ]);
                        break;
                    case 'city':
                        $isMatch = ($location->location_id == $cityId);
                        \Log::info("İlçe kontrolü", [
                            'location_id' => $location->location_id,
                            'cityId' => $cityId,
                            'isMatch' => $isMatch
                        ]);
                        break;
                    case 'postal_code':
                        // Posta kodu eşleşmesi için basit bir kontrol
                        $isMatch = ($location->location_code == $postalCode);
                        \Log::info("Posta kodu kontrolü", [
                            'location_code' => $location->location_code,
                            'postalCode' => $postalCode,
                            'isMatch' => $isMatch
                        ]);
                        break;
                }

                if ($isMatch) {
                    \Log::info("Eşleşme bulundu", [
                        'zone_id' => $zone->id,
                        'zone_name' => $zone->name,
                        'location_type' => $location->location_type,
                        'location_id' => $location->location_id
                    ]);
                    $matchingZones[] = $zone;
                    $matched = true;
                    break; // Bir eşleşme bulunduğunda diğer lokasyonları kontrol etmeye gerek yok
                }
            }

            if (!$matched) {
                \Log::info("Bölge için eşleşme bulunamadı", ['zone_id' => $zone->id, 'zone_name' => $zone->name]);
            }
        }

        \Log::info('Eşleşen bölgeler', ['matchingZones' => count($matchingZones)]);

        return $matchingZones;
    }

    /**
     * Belirtilen bölge için uygun kargo metodlarını ve fiyatlarını hesaplar
     */
    private function getZoneShippingMethods(ShippingZone $zone, float $cartTotal, float $cartWeight, float $cartDesi = 0): array
    {
        \Log::info('Bölge kargo metodları hesaplanıyor', [
            'zone_id' => $zone->id,
            'zone_name' => $zone->name,
            'cartTotal' => $cartTotal,
            'cartWeight' => $cartWeight,
            'cartDesi' => $cartDesi
        ]);

        // Bölgeye ait kargo metodlarını al
        $zoneMethods = ShippingZoneMethod::where('zone_id', $zone->id)
            ->where('is_active', true)
            ->get();

        \Log::info('Bölge kargo metodları', [
            'zone_id' => $zone->id,
            'methods_count' => $zoneMethods->count()
        ]);

        $availableMethods = [];

        foreach ($zoneMethods as $zoneMethod) {
            \Log::info('Kargo metodu kontrol ediliyor', [
                'zone_method_id' => $zoneMethod->id,
                'method_id' => $zoneMethod->method_id
            ]);

            // Kargo metodunu al
            $method = ShippingMethod::find($zoneMethod->method_id);

            if (!$method) {
                \Log::warning('Kargo metodu bulunamadı', ['method_id' => $zoneMethod->method_id]);
                continue;
            }

            if (!$method->is_active) {
                \Log::info('Kargo metodu aktif değil', ['method_id' => $method->id]);
                continue;
            }

            // Kargo şirketini al
            $company = ShippingCompany::find($method->shipping_company_id);

            if (!$company) {
                \Log::warning('Kargo şirketi bulunamadı', ['company_id' => $method->shipping_company_id]);
                continue;
            }

            if (!$company->is_active) {
                \Log::info('Kargo şirketi aktif değil', ['company_id' => $company->id]);
                continue;
            }

            // Minimum sipariş tutarı kontrolü
            if ($zoneMethod->min_order_amount > 0 && $cartTotal < $zoneMethod->min_order_amount) {
                \Log::info('Minimum sipariş tutarı kontrolü başarısız', [
                    'min_order_amount' => $zoneMethod->min_order_amount,
                    'cartTotal' => $cartTotal
                ]);
                continue;
            }

            // Maksimum sipariş tutarı kontrolü
            if ($zoneMethod->max_order_amount > 0 && $cartTotal > $zoneMethod->max_order_amount) {
                \Log::info('Maksimum sipariş tutarı kontrolü başarısız', [
                    'max_order_amount' => $zoneMethod->max_order_amount,
                    'cartTotal' => $cartTotal
                ]);
                continue;
            }

            // Minimum desi kontrolü
            if ($zoneMethod->min_desi > 0 && $cartDesi > 0 && $cartDesi < $zoneMethod->min_desi) {
                \Log::info('Minimum desi kontrolü başarısız', [
                    'min_desi' => $zoneMethod->min_desi,
                    'cartDesi' => $cartDesi
                ]);
                continue;
            }

            // Maksimum desi kontrolü
            if ($zoneMethod->max_desi > 0 && $cartDesi > 0 && $cartDesi > $zoneMethod->max_desi) {
                \Log::info('Maksimum desi kontrolü başarısız', [
                    'max_desi' => $zoneMethod->max_desi,
                    'cartDesi' => $cartDesi
                ]);
                continue;
            }

            // Kargo ücretini hesapla
            $shippingCost = $this->calculateShippingCostInternal($zoneMethod, $cartTotal, $cartWeight, $cartDesi);

            // Ücretsiz kargo kontrolü
            $isFree = false;
            if ($zoneMethod->free_shipping_min_amount > 0 && $cartTotal >= $zoneMethod->free_shipping_min_amount) {
                \Log::info('Ücretsiz kargo koşulu sağlandı', [
                    'free_shipping_min_amount' => $zoneMethod->free_shipping_min_amount,
                    'cartTotal' => $cartTotal
                ]);
                $shippingCost = 0;
                $isFree = true;
            }

            $methodData = [
                'id' => $zoneMethod->id,
                'method_id' => $method->id,
                'shipping_company_id' => $company->id,
                'name' => $method->name,
                'description' => $method->description,
                'delivery_time' => $method->delivery_time,
                'cost' => $shippingCost,
                'formatted_cost' => $shippingCost > 0 ? number_format($shippingCost, 2, ',', '.') . ' ₺' : 'Ücretsiz',
                'is_free' => $isFree,
                'zone_name' => $zone->name,
            ];

            \Log::info('Uygun kargo metodu bulundu', $methodData);

            $availableMethods[] = $methodData;
        }

        \Log::info('Bölge için uygun kargo metodları', [
            'zone_id' => $zone->id,
            'available_methods_count' => count($availableMethods)
        ]);

        return $availableMethods;
    }

    /**
     * Tahmini teslimat süresini metin olarak döndürür
     *
     * @param int|null $days Gün sayısı
     * @return string|null
     */
    protected function getEstimatedDeliveryText($days)
    {
        if ($days === null) {
            return null;
        }

        if ($days === 0) {
            return 'Aynı gün teslimat';
        }

        if ($days === 1) {
            return 'Ertesi gün teslimat';
        }

        return "{$days} gün içinde teslimat";
    }

    /**
     * Belirli bir kargo metodu için kargo ücretini hesaplar
     *
     * @param int $methodId Kargo metodu ID
     * @param int $countryId Ülke ID
     * @param int|null $stateId Eyalet/İl ID
     * @param int|null $cityId Şehir/İlçe ID
     * @param string|null $postalCode Posta kodu
     * @param float $orderAmount Sipariş tutarı
     * @param float|null $weight Sipariş ağırlığı
     * @return float|null Kargo ücreti (null ise bu metod uygulanamaz)
     */
    public function calculateShippingCost($methodId, $countryId, $stateId = null, $cityId = null, $postalCode = null, $orderAmount = 0, $weight = null)
    {
        // Adres için uygun bölgeyi bul
        $zone = ShippingZone::findForAddress($countryId, $stateId, $cityId, $postalCode);

        if (!$zone) {
            return null;
        }

        // Bölgedeki belirli kargo metodunu al
        $zoneMethod = $zone->methods()
            ->where('method_id', $methodId)
            ->where('is_active', true)
            ->first();

        if (!$zoneMethod) {
            return null;
        }

        // Kargo ücretini hesapla
        return $zoneMethod->calculateCost($orderAmount, $weight);
    }

    /**
     * Belirtilen kargo metodu için kargo ücretini hesaplar
     *
     * @param int $zoneMethodId Kargo bölgesi metodu ID
     * @param float $cartTotal Sepet toplamı
     * @param float $cartWeight Sepet ağırlığı (opsiyonel)
     * @param float $cartDesi Sepet desi değeri (opsiyonel)
     * @return float|null Kargo ücreti veya null (metod bulunamazsa)
     */
    public function calculateShippingCostForMethod(int $zoneMethodId, float $cartTotal, float $cartWeight = 0, float $cartDesi = 0): ?float
    {
        $zoneMethod = ShippingZoneMethod::find($zoneMethodId);

        if (!$zoneMethod || !$zoneMethod->is_active) {
            return null;
        }

        // Minimum sipariş tutarı kontrolü
        if ($zoneMethod->min_order_amount > 0 && $cartTotal < $zoneMethod->min_order_amount) {
            return null;
        }

        // Maksimum sipariş tutarı kontrolü
        if ($zoneMethod->max_order_amount > 0 && $cartTotal > $zoneMethod->max_order_amount) {
            return null;
        }

        // Minimum ağırlık kontrolü
        if ($zoneMethod->min_weight > 0 && $cartWeight > 0 && $cartWeight < $zoneMethod->min_weight) {
            return null;
        }

        // Maksimum ağırlık kontrolü
        if ($zoneMethod->max_weight > 0 && $cartWeight > 0 && $cartWeight > $zoneMethod->max_weight) {
            return null;
        }

        // Minimum desi kontrolü
        if ($zoneMethod->min_desi > 0 && $cartDesi > 0 && $cartDesi < $zoneMethod->min_desi) {
            return null;
        }

        // Maksimum desi kontrolü
        if ($zoneMethod->max_desi > 0 && $cartDesi > 0 && $cartDesi > $zoneMethod->max_desi) {
            return null;
        }

        // Kargo ücretini hesapla
        $shippingCost = $this->calculateShippingCostInternal($zoneMethod, $cartTotal, $cartWeight, $cartDesi);

        // Ücretsiz kargo kontrolü
        if ($zoneMethod->free_shipping_min_amount > 0 && $cartTotal >= $zoneMethod->free_shipping_min_amount) {
            $shippingCost = 0;
        }

        return $shippingCost;
    }

    /**
     * Kargo ücretini hesaplar (iç metod)
     */
    private function calculateShippingCostInternal(ShippingZoneMethod $zoneMethod, float $cartTotal, float $cartWeight, float $cartDesi = 0): float
    {
        // Önce desi bazlı fiyatlandırma oranlarını kontrol et
        $rates = $zoneMethod->activeRates()->get();

        if ($rates->isNotEmpty() && $cartDesi > 0) {
            // Desi değerine uygun oranı bul
            foreach ($rates as $rate) {
                if ($rate->isApplicableForDesi($cartDesi)) {
                    return round($rate->cost, 2);
                }
            }
        }

        // Desi bazlı fiyatlandırma yoksa veya uygun oran bulunamazsa, eski hesaplama yöntemini kullan

        // Sabit ücret
        $cost = $zoneMethod->cost;

        // Sepet tutarına göre ek ücret
        if ($zoneMethod->cost_per_order_percent > 0) {
            $cost += ($cartTotal * $zoneMethod->cost_per_order_percent / 100);
        }

        // Ağırlığa göre ek ücret
        if ($zoneMethod->cost_per_weight > 0 && $cartWeight > 0) {
            $cost += ($cartWeight * $zoneMethod->cost_per_weight);
        }

        // Desi'ye göre ek ücret
        if ($zoneMethod->cost_per_desi > 0 && $cartDesi > 0) {
            $cost += ($cartDesi * $zoneMethod->cost_per_desi);
        }

        return round($cost, 2);
    }

    /**
     * Sepet için kargo ücretini hesaplar
     *
     * @param Cart $cart Sepet
     * @param int $zoneMethodId Kargo bölgesi metodu ID
     * @return float|null Kargo ücreti veya null (metod bulunamazsa)
     */
    public function calculateShippingCostForCart(Cart $cart, int $zoneMethodId): ?float
    {
        $cartTotal = $cart->total;

        // Sepet ağırlığını ve desi değerini hesapla
        $cartWeight = 0;
        $cartDesi = 0;

        // Sepetteki ürünlerin ağırlık ve desi değerlerini topla
        foreach ($cart->items as $item) {
            $quantity = $item->quantity;

            if ($item->variant_id) {
                $variant = $item->variant;
                if ($variant) {
                    $cartWeight += ($variant->weight ?? 0) * $quantity;
                    $cartDesi += ($variant->desi ?? 0) * $quantity;
                }
            } else {
                $product = $item->product;
                if ($product) {
                    $cartWeight += ($product->weight ?? 0) * $quantity;
                    $cartDesi += ($product->desi ?? 0) * $quantity;
                }
            }
        }

        return $this->calculateShippingCostForMethod($zoneMethodId, $cartTotal, $cartWeight, $cartDesi);
    }
}
