<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Cart;
use Illuminate\Support\Facades\Session;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class IyzicoService
{
    /**
     * İyzico API anahtarları
     */
    protected $apiKey;
    protected $secretKey;
    protected $baseUrl;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->apiKey = Config::get('services.iyzico.api_key');
        $this->secretKey = Config::get('services.iyzico.secret_key');
        $this->baseUrl = Config::get('services.iyzico.base_url');
    }

    /**
     * İyzico ödeme işlemini başlat
     *
     * @param Order $order Sipariş
     * @param array $cardData Kredi kartı bilgileri
     * @return array İşlem sonucu
     */
    public function processPayment(Order $order, array $cardData)
    {
        try {
            // İyzico entegrasyonu burada yapılacak
            // Şimdilik sahte bir başarılı yanıt döndürelim

            Log::info('İyzico ödeme işlemi başlatıldı', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'amount' => $order->total_amount
            ]);

            // Gerçek entegrasyonda burada iyzico API'si çağrılacak
            // İyzipay PHP SDK kurulduğunda aşağıdaki gibi bir kod kullanılabilir:

            $options = new \Iyzipay\Options();
            $options->setApiKey($this->apiKey);
            $options->setSecretKey($this->secretKey);
            $options->setBaseUrl($this->baseUrl);

            $request = new \Iyzipay\Request\CreatePaymentRequest();
            $request->setLocale(\Iyzipay\Model\Locale::TR);
            $request->setConversationId($order->order_number);
            $request->setPrice($order->total_amount);
            $request->setPaidPrice($order->total_amount);
            $request->setCurrency(\Iyzipay\Model\Currency::TL);
            $request->setInstallment(1);
            $request->setBasketId($order->order_number);
            $request->setPaymentChannel(\Iyzipay\Model\PaymentChannel::WEB);
            $request->setPaymentGroup(\Iyzipay\Model\PaymentGroup::PRODUCT);

            $paymentCard = new \Iyzipay\Model\PaymentCard();
            $paymentCard->setCardHolderName($cardData['holder_name']);
            $paymentCard->setCardNumber($cardData['number']);
            $paymentCard->setExpireMonth($cardData['expiry_month']);
            $paymentCard->setExpireYear('20' . $cardData['expiry_year']);
            $paymentCard->setCvc($cardData['cvc']);
            $paymentCard->setRegisterCard(0);
            $request->setPaymentCard($paymentCard);

            $buyer = new \Iyzipay\Model\Buyer();
            // Eğer user_id null ise, sipariş numarasını kullan
            $buyerId = $order->user_id ?? 'guest_' . $order->order_number;
            $buyer->setId($buyerId);

            // İsim ve soyisim ayırma
            $nameParts = explode(' ', $order->billing_name);
            $firstName = $nameParts[0];
            $lastName = count($nameParts) > 1 ? implode(' ', array_slice($nameParts, 1)) : 'NoSurname';

            // İsim ve soyisim boş olmamalı
            $firstName = !empty($firstName) ? $firstName : 'Guest';
            $lastName = !empty($lastName) ? $lastName : 'User';

            $buyer->setName($firstName);
            $buyer->setSurname($lastName);
            $buyer->setGsmNumber($order->billing_phone);
            $buyer->setEmail($order->billing_email);
            $buyer->setIdentityNumber('11111111111'); // TC Kimlik No zorunlu, misafir için sabit değer
            $buyer->setRegistrationAddress($order->billing_address);
            $buyer->setIp($_SERVER['REMOTE_ADDR'] ?? '127.0.0.1');
            $buyer->setCity($order->billing_city);
            $buyer->setCountry($order->billing_country);
            $request->setBuyer($buyer);

            $shippingAddress = new \Iyzipay\Model\Address();
            $shippingAddress->setContactName($order->shipping_name);
            $shippingAddress->setCity($order->shipping_city);
            $shippingAddress->setCountry($order->shipping_country);
            $shippingAddress->setAddress($order->shipping_address);
            $request->setShippingAddress($shippingAddress);

            $billingAddress = new \Iyzipay\Model\Address();
            $billingAddress->setContactName($order->billing_name);
            $billingAddress->setCity($order->billing_city);
            $billingAddress->setCountry($order->billing_country);
            $billingAddress->setAddress($order->billing_address);
            $request->setBillingAddress($billingAddress);

            $basketItems = [];
            foreach ($order->items as $item) {
                $basketItem = new \Iyzipay\Model\BasketItem();
                $basketItem->setId($item->id);
                $basketItem->setName($item->product_name);
                $basketItem->setCategory1('Genel');
                $basketItem->setItemType(\Iyzipay\Model\BasketItemType::PHYSICAL);
                $basketItem->setPrice($item->price * $item->quantity);
                $basketItems[] = $basketItem;
            }
            $request->setBasketItems($basketItems);

            $payment = \Iyzipay\Model\Payment::create($request, $options);

            if ($payment->getStatus() == 'success') {
                return [
                    'success' => true,
                    'message' => 'Ödeme işlemi başarıyla tamamlandı.',
                    'transaction_id' => $payment->getPaymentId(),
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $payment->getErrorMessage(),
                ];
            }

        } catch (\Exception $e) {
            Log::error('İyzico ödeme işlemi hatası', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Ödeme işlemi sırasında bir hata oluştu: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * İyzico ödeme sonucunu işle
     *
     * @param array $callbackData İyzico'dan gelen callback verileri
     * @return array İşlem sonucu
     */
    public function handleCallback(array $callbackData)
    {
        try {
            Log::info('İyzico callback işlemi', [
                'data' => $callbackData
            ]);

            $options = new \Iyzipay\Options();
            $options->setApiKey($this->apiKey);
            $options->setSecretKey($this->secretKey);
            $options->setBaseUrl($this->baseUrl);

            // 3D secure işlemi tamamlandıktan sonra ödemeyi tamamla
            $request = new \Iyzipay\Request\CreateThreedsPaymentRequest();
            $request->setLocale(\Iyzipay\Model\Locale::TR);
            $request->setConversationId($callbackData['conversationId']);
            $request->setPaymentId($callbackData['paymentId']);

            $threedsPayment = \Iyzipay\Model\ThreedsPayment::create($request, $options);

            if ($threedsPayment->getStatus() == 'success') {
                // Siparişi güncelle
                $order = Order::where('order_number', $threedsPayment->getBasketId())->first();
                if ($order) {
                    $order->status = OrderStatus::PROCESSING->value;
                    $order->payment_status = PaymentStatus::PAID->value;
                    $order->save();

                    // Sipariş notu ekle
                    $order->addNote(
                        '3D Secure ödeme başarıyla tamamlandı. İşlem ID: ' . $threedsPayment->getPaymentId(),
                        $order->user_id,
                        false,
                        'system'
                    );

                    // Sepeti temizle
                    $sessionId = session()->getId();

                    // Session ID'yi logla
                    Log::info('Session ID bilgileri (handleCallback)', [
                        'session_id' => $sessionId,
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'user_id' => $order->user_id
                    ]);

                    // Tüm sepetleri logla
                    Log::info('Tüm sepetler (handleCallback)', [
                        'all_carts' => Cart::all()->toArray()
                    ]);

                    // Misafir kullanıcı için session_id ile sepeti bul
                    if (!$order->user_id) {
                        // Tüm misafir sepetlerini bul
                        $guestCarts = Cart::whereNull('user_id')->get();

                        Log::info('Misafir sepetleri (handleCallback)', [
                            'guest_carts_count' => $guestCarts->count(),
                            'guest_carts' => $guestCarts->toArray()
                        ]);

                        // Mevcut session_id ile sepeti bul
                        $guestCart = Cart::where('session_id', $sessionId)->first();

                        // Sepet bulundu mu kontrol et
                        if ($guestCart) {
                            Log::info('Misafir kullanıcı sepeti bulundu (handleCallback)', [
                                'cart_id' => $guestCart->id,
                                'session_id' => $sessionId,
                                'items_count' => $guestCart->items->count()
                            ]);

                            // Sepeti temizle
                            $guestCart->items()->delete();
                            $guestCart->removeCoupon();

                            Log::info('Misafir kullanıcı sepeti temizlendi (handleCallback)', [
                                'cart_id' => $guestCart->id,
                                'session_id' => $sessionId
                            ]);
                        } else {
                            Log::warning('Misafir kullanıcı sepeti bulunamadı (handleCallback)', [
                                'session_id' => $sessionId
                            ]);

                            // Tüm misafir sepetlerini temizle
                            foreach ($guestCarts as $cart) {
                                $cart->items()->delete();
                                $cart->removeCoupon();

                                Log::info('Alternatif misafir sepeti temizlendi (handleCallback)', [
                                    'cart_id' => $cart->id,
                                    'session_id' => $cart->session_id
                                ]);
                            }
                        }
                    }

                    // Giriş yapmış kullanıcı için user_id ile sepeti bul
                    if ($order->user_id) {
                        $userCart = Cart::where('user_id', $order->user_id)->first();

                        // Sepet bulundu mu kontrol et
                        if ($userCart) {
                            Log::info('Giriş yapmış kullanıcı sepeti bulundu (handleCallback)', [
                                'cart_id' => $userCart->id,
                                'user_id' => $order->user_id,
                                'items_count' => $userCart->items->count()
                            ]);

                            // Sepeti temizle
                            $userCart->items()->delete();
                            $userCart->removeCoupon();

                            Log::info('Giriş yapmış kullanıcı sepeti temizlendi (handleCallback)', [
                                'cart_id' => $userCart->id,
                                'user_id' => $order->user_id
                            ]);
                        } else {
                            Log::warning('Giriş yapmış kullanıcı sepeti bulunamadı (handleCallback)', [
                                'user_id' => $order->user_id
                            ]);
                        }
                    }

                    // Sepet temizleme işlemini logla
                    Log::info('Sepet temizleme işlemi tamamlandı (handleCallback)', [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'user_id' => $order->user_id,
                        'session_id' => $sessionId
                    ]);
                }

                return [
                    'success' => true,
                    'message' => 'Ödeme işlemi başarıyla tamamlandı.',
                    'order_number' => $order ? $order->order_number : null,
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $threedsPayment->getErrorMessage() ?? 'Ödeme işlemi başarısız oldu.',
                ];
            }

        } catch (\Exception $e) {
            Log::error('İyzico callback işlemi hatası', [
                'error' => $e->getMessage(),
                'data' => $callbackData
            ]);

            return [
                'success' => false,
                'message' => 'Ödeme sonucu işlenirken bir hata oluştu: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * İyzico 3D ödeme işlemini başlat
     *
     * @param Order $order Sipariş
     * @param array $cardData Kredi kartı bilgileri
     * @return array İşlem sonucu
     */
    public function initiate3DPayment(Order $order, array $cardData)
    {
        try {
            Log::info('İyzico 3D ödeme işlemi başlatıldı', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'amount' => $order->total_amount
            ]);

            $options = new \Iyzipay\Options();
            $options->setApiKey($this->apiKey);
            $options->setSecretKey($this->secretKey);
            $options->setBaseUrl($this->baseUrl);

            $request = new \Iyzipay\Request\CreatePaymentRequest();
            $request->setLocale(\Iyzipay\Model\Locale::TR);
            $request->setConversationId($order->order_number);
            $request->setPrice($order->total_amount);
            $request->setPaidPrice($order->total_amount);
            $request->setCurrency(\Iyzipay\Model\Currency::TL);
            $request->setInstallment(1);
            $request->setBasketId($order->order_number);
            $request->setPaymentChannel(\Iyzipay\Model\PaymentChannel::WEB);
            $request->setPaymentGroup(\Iyzipay\Model\PaymentGroup::PRODUCT);
            $request->setCallbackUrl(route('checkout.callback'));

            $paymentCard = new \Iyzipay\Model\PaymentCard();
            $paymentCard->setCardHolderName($cardData['holder_name']);
            $paymentCard->setCardNumber($cardData['number']);
            $paymentCard->setExpireMonth($cardData['expiry_month']);
            $paymentCard->setExpireYear('20' . $cardData['expiry_year']);
            $paymentCard->setCvc($cardData['cvc']);
            $paymentCard->setRegisterCard(0);
            $request->setPaymentCard($paymentCard);

            $buyer = new \Iyzipay\Model\Buyer();
            // Eğer user_id null ise, sipariş numarasını kullan
            $buyerId = $order->user_id ?? 'guest_' . $order->order_number;
            $buyer->setId($buyerId);

            // İsim ve soyisim ayırma
            $nameParts = explode(' ', $order->billing_name);
            $firstName = $nameParts[0];
            $lastName = count($nameParts) > 1 ? implode(' ', array_slice($nameParts, 1)) : 'NoSurname';

            // İsim ve soyisim boş olmamalı
            $firstName = !empty($firstName) ? $firstName : 'Guest';
            $lastName = !empty($lastName) ? $lastName : 'User';

            $buyer->setName($firstName);
            $buyer->setSurname($lastName);
            $buyer->setGsmNumber($order->billing_phone);
            $buyer->setEmail($order->billing_email);
            $buyer->setIdentityNumber('11111111111'); // TC Kimlik No zorunlu, misafir için sabit değer
            $buyer->setRegistrationAddress($order->billing_address);
            $buyer->setIp($_SERVER['REMOTE_ADDR'] ?? '127.0.0.1');
            $buyer->setCity($order->billing_city);
            $buyer->setCountry($order->billing_country);
            $request->setBuyer($buyer);

            $shippingAddress = new \Iyzipay\Model\Address();
            $shippingAddress->setContactName($order->shipping_name);
            $shippingAddress->setCity($order->shipping_city);
            $shippingAddress->setCountry($order->shipping_country);
            $shippingAddress->setAddress($order->shipping_address);
            $request->setShippingAddress($shippingAddress);

            $billingAddress = new \Iyzipay\Model\Address();
            $billingAddress->setContactName($order->billing_name);
            $billingAddress->setCity($order->billing_city);
            $billingAddress->setCountry($order->billing_country);
            $billingAddress->setAddress($order->billing_address);
            $request->setBillingAddress($billingAddress);

            $basketItems = [];
            foreach ($order->items as $item) {
                $basketItem = new \Iyzipay\Model\BasketItem();
                $basketItem->setId($item->id);
                $basketItem->setName($item->product_name);
                $basketItem->setCategory1('Genel');
                $basketItem->setItemType(\Iyzipay\Model\BasketItemType::PHYSICAL);
                $basketItem->setPrice($item->price * $item->quantity);
                $basketItems[] = $basketItem;
            }
            $request->setBasketItems($basketItems);

            $threedsInitialize = \Iyzipay\Model\ThreedsInitialize::create($request, $options);

            if ($threedsInitialize->getStatus() == 'success') {
                return [
                    'success' => true,
                    'message' => '3D ödeme işlemi başlatıldı.',
                    'html_content' => $threedsInitialize->getHtmlContent(),
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $threedsInitialize->getErrorMessage(),
                ];
            }

        } catch (\Exception $e) {
            Log::error('İyzico 3D ödeme işlemi hatası', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '3D ödeme işlemi başlatılırken bir hata oluştu: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * İyzico PayWithIyzico ödeme işlemini başlat
     *
     * @param Order $order Sipariş
     * @return array İşlem sonucu
     */
    public function initiatePayWithIyzico(Order $order)
    {
        try {
            Log::info('İyzico PayWithIyzico ödeme işlemi başlatıldı', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'amount' => $order->total_amount
            ]);

            $options = new \Iyzipay\Options();
            $options->setApiKey($this->apiKey);
            $options->setSecretKey($this->secretKey);
            $options->setBaseUrl($this->baseUrl);

            $request = new \Iyzipay\Request\CreatePayWithIyzicoInitializeRequest();
            $request->setLocale(\Iyzipay\Model\Locale::TR);
            $request->setConversationId($order->order_number);
            $request->setPrice($order->total_amount);
            $request->setPaidPrice($order->total_amount);
            $request->setCurrency(\Iyzipay\Model\Currency::TL);
            $request->setBasketId($order->order_number);
            $request->setPaymentGroup(\Iyzipay\Model\PaymentGroup::PRODUCT);
            $request->setCallbackUrl(route('checkout.paywithiyzico.callback'));
            $request->setEnabledInstallments([1, 2, 3, 6, 9, 12]);

            $buyer = new \Iyzipay\Model\Buyer();
            // Eğer user_id null ise, sipariş numarasını kullan
            $buyerId = $order->user_id ?? 'guest_' . $order->order_number;
            $buyer->setId($buyerId);

            // İsim ve soyisim ayırma
            $nameParts = explode(' ', $order->billing_name);
            $firstName = $nameParts[0];
            $lastName = count($nameParts) > 1 ? implode(' ', array_slice($nameParts, 1)) : 'NoSurname';

            // İsim ve soyisim boş olmamalı
            $firstName = !empty($firstName) ? $firstName : 'Guest';
            $lastName = !empty($lastName) ? $lastName : 'User';

            $buyer->setName($firstName);
            $buyer->setSurname($lastName);
            $buyer->setGsmNumber($order->billing_phone);
            $buyer->setEmail($order->billing_email);
            $buyer->setIdentityNumber('11111111111'); // TC Kimlik No zorunlu, misafir için sabit değer
            $buyer->setRegistrationAddress($order->billing_address);
            $buyer->setIp($_SERVER['REMOTE_ADDR'] ?? '127.0.0.1');
            $buyer->setCity($order->billing_city);
            $buyer->setCountry($order->billing_country);

            // Buyer bilgilerini logla
            Log::info('Buyer bilgileri', [
                'buyerId' => $buyerId,
                'name' => $firstName,
                'surname' => $lastName,
                'email' => $order->billing_email,
                'phone' => $order->billing_phone
            ]);

            $request->setBuyer($buyer);

            $shippingAddress = new \Iyzipay\Model\Address();
            $shippingAddress->setContactName($order->shipping_name);
            $shippingAddress->setCity($order->shipping_city);
            $shippingAddress->setCountry($order->shipping_country);
            $shippingAddress->setAddress($order->shipping_address);
            $request->setShippingAddress($shippingAddress);

            $billingAddress = new \Iyzipay\Model\Address();
            $billingAddress->setContactName($order->billing_name);
            $billingAddress->setCity($order->billing_city);
            $billingAddress->setCountry($order->billing_country);
            $billingAddress->setAddress($order->billing_address);
            $request->setBillingAddress($billingAddress);

            $basketItems = [];
            foreach ($order->items as $item) {
                $basketItem = new \Iyzipay\Model\BasketItem();
                $basketItem->setId($item->id);
                $basketItem->setName($item->product_name);
                $basketItem->setCategory1('Genel');
                $basketItem->setItemType(\Iyzipay\Model\BasketItemType::PHYSICAL);
                $basketItem->setPrice($item->price * $item->quantity);
                $basketItems[] = $basketItem;
            }
            $request->setBasketItems($basketItems);

            $payWithIyzicoInitialize = \Iyzipay\Model\PayWithIyzicoInitialize::create($request, $options);

            if ($payWithIyzicoInitialize->getStatus() == 'success') {
                return [
                    'success' => true,
                    'message' => 'PayWithIyzico ödeme işlemi başlatıldı.',
                    'token' => $payWithIyzicoInitialize->getToken(),
                    'checkout_url' => $payWithIyzicoInitialize->getPayWithIyzicoPageUrl(),
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $payWithIyzicoInitialize->getErrorMessage(),
                ];
            }

        } catch (\Exception $e) {
            Log::error('İyzico PayWithIyzico ödeme işlemi hatası', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'PayWithIyzico ödeme işlemi başlatılırken bir hata oluştu: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * İyzico PayWithIyzico ödeme sonucunu işle
     *
     * @param string $token İyzico'dan gelen token
     * @return array İşlem sonucu
     */
    public function handlePayWithIyzicoCallback($token)
    {
        try {
            Log::info('İyzico PayWithIyzico callback işlemi', [
                'token' => $token
            ]);

            $options = new \Iyzipay\Options();
            $options->setApiKey($this->apiKey);
            $options->setSecretKey($this->secretKey);
            $options->setBaseUrl($this->baseUrl);

            $request = new \Iyzipay\Request\RetrievePayWithIyzicoRequest();
            $request->setLocale(\Iyzipay\Model\Locale::TR);
            $request->setToken($token);

            $payWithIyzico = \Iyzipay\Model\PayWithIyzico::retrieve($request, $options);

            if ($payWithIyzico->getStatus() == 'success' && $payWithIyzico->getPaymentStatus() == 'SUCCESS') {
                // Siparişi güncelle
                $basketId = $payWithIyzico->getBasketId();
                $order = Order::where('order_number', $basketId)->first();

                if ($order) {
                    $order->status = OrderStatus::PROCESSING->value;
                    $order->payment_status = PaymentStatus::PAID->value;
                    $order->save();

                    // Sipariş notu ekle
                    $order->addNote(
                        'PayWithIyzico ödeme başarıyla tamamlandı. İşlem ID: ' . $payWithIyzico->getPaymentId(),
                        $order->user_id,
                        false,
                        'system'
                    );

                    // Sepeti temizle
                    $sessionId = session()->getId();

                    // Session ID'yi logla
                    Log::info('Session ID bilgileri (handlePayWithIyzicoCallback)', [
                        'session_id' => $sessionId,
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'user_id' => $order->user_id
                    ]);

                    // Tüm sepetleri logla
                    Log::info('Tüm sepetler (handlePayWithIyzicoCallback)', [
                        'all_carts' => Cart::all()->toArray()
                    ]);

                    // Misafir kullanıcı için session_id ile sepeti bul
                    if (!$order->user_id) {
                        // Tüm misafir sepetlerini bul
                        $guestCarts = Cart::whereNull('user_id')->get();

                        Log::info('Misafir sepetleri (handlePayWithIyzicoCallback)', [
                            'guest_carts_count' => $guestCarts->count(),
                            'guest_carts' => $guestCarts->toArray()
                        ]);

                        // Mevcut session_id ile sepeti bul
                        $guestCart = Cart::where('session_id', $sessionId)->first();

                        // Sepet bulundu mu kontrol et
                        if ($guestCart) {
                            Log::info('Misafir kullanıcı sepeti bulundu (handlePayWithIyzicoCallback)', [
                                'cart_id' => $guestCart->id,
                                'session_id' => $sessionId,
                                'items_count' => $guestCart->items->count()
                            ]);

                            // Sepeti temizle
                            $guestCart->items()->delete();
                            $guestCart->removeCoupon();

                            Log::info('Misafir kullanıcı sepeti temizlendi (handlePayWithIyzicoCallback)', [
                                'cart_id' => $guestCart->id,
                                'session_id' => $sessionId
                            ]);
                        } else {
                            Log::warning('Misafir kullanıcı sepeti bulunamadı (handlePayWithIyzicoCallback)', [
                                'session_id' => $sessionId
                            ]);

                            // Tüm misafir sepetlerini temizle
                            foreach ($guestCarts as $cart) {
                                $cart->items()->delete();
                                $cart->removeCoupon();

                                Log::info('Alternatif misafir sepeti temizlendi (handlePayWithIyzicoCallback)', [
                                    'cart_id' => $cart->id,
                                    'session_id' => $cart->session_id
                                ]);
                            }
                        }
                    }

                    // Giriş yapmış kullanıcı için user_id ile sepeti bul
                    if ($order->user_id) {
                        $userCart = Cart::where('user_id', $order->user_id)->first();

                        // Sepet bulundu mu kontrol et
                        if ($userCart) {
                            Log::info('Giriş yapmış kullanıcı sepeti bulundu (handlePayWithIyzicoCallback)', [
                                'cart_id' => $userCart->id,
                                'user_id' => $order->user_id,
                                'items_count' => $userCart->items->count()
                            ]);

                            // Sepeti temizle
                            $userCart->items()->delete();
                            $userCart->removeCoupon();

                            Log::info('Giriş yapmış kullanıcı sepeti temizlendi (handlePayWithIyzicoCallback)', [
                                'cart_id' => $userCart->id,
                                'user_id' => $order->user_id
                            ]);
                        } else {
                            Log::warning('Giriş yapmış kullanıcı sepeti bulunamadı (handlePayWithIyzicoCallback)', [
                                'user_id' => $order->user_id
                            ]);
                        }
                    }

                    // Sepet temizleme işlemini logla
                    Log::info('Sepet temizleme işlemi tamamlandı (handlePayWithIyzicoCallback)', [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'user_id' => $order->user_id,
                        'session_id' => $sessionId
                    ]);
                }

                return [
                    'success' => true,
                    'message' => 'Ödeme işlemi başarıyla tamamlandı.',
                    'order_number' => $order ? $order->order_number : null,
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $payWithIyzico->getErrorMessage() ?? 'Ödeme işlemi başarısız oldu.',
                ];
            }

        } catch (\Exception $e) {
            Log::error('İyzico PayWithIyzico callback işlemi hatası', [
                'error' => $e->getMessage(),
                'token' => $token
            ]);

            return [
                'success' => false,
                'message' => 'Ödeme sonucu işlenirken bir hata oluştu: ' . $e->getMessage(),
            ];
        }
    }
}
