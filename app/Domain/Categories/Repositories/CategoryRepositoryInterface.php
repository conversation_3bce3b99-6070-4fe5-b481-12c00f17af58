<?php

namespace App\Domain\Categories\Repositories;

use App\Domain\Categories\Entities\Category;
use App\Domain\Categories\ValueObjects\CategorySlug;
use App\Domain\Categories\ValueObjects\CategoryPath;

interface CategoryRepositoryInterface
{
    /**
     * Kategori kaydet
     */
    public function save(Category $category): Category;

    /**
     * ID ile kategori bul
     */
    public function findById(int $id): ?Category;

    /**
     * Slug ile kategori bul
     */
    public function findBySlug(CategorySlug $slug): ?Category;

    /**
     * Parent ID'sine göre kategorileri al
     */
    public function findByParentId(?int $parentId, int $limit = 100, int $offset = 0): array;

    /**
     * Root kategorileri al
     */
    public function findRootCategories(int $limit = 100, int $offset = 0): array;

    /**
     * Aktif kategorileri al
     */
    public function findActive(int $limit = 100, int $offset = 0): array;

    /**
     * Öne çıkan kategorileri al
     */
    public function findFeatured(int $limit = 100, int $offset = 0): array;

    /**
     * Menüde gösterilen kategorileri al
     */
    public function findVisibleInMenu(int $limit = 100, int $offset = 0): array;

    /**
     * Kategori ağacını al
     */
    public function getTree(?int $parentId = null, int $maxDepth = 10): array;

    /**
     * Kategori yolunu al (breadcrumb için)
     */
    public function getPath(int $categoryId): array;

    /**
     * Alt kategorileri al (tüm seviyeler)
     */
    public function getDescendants(int $categoryId, int $maxDepth = 10): array;

    /**
     * Üst kategorileri al (tüm seviyeler)
     */
    public function getAncestors(int $categoryId): array;

    /**
     * Kardeş kategorileri al
     */
    public function getSiblings(int $categoryId): array;

    /**
     * Arama kriterlerine göre kategorileri al
     */
    public function search(array $criteria, int $limit = 100, int $offset = 0): array;

    /**
     * Kategori sayısını al
     */
    public function count(array $criteria = []): int;

    /**
     * Parent ID'sine göre kategori sayısını al
     */
    public function countByParentId(?int $parentId): int;

    /**
     * Kategoriyi sil
     */
    public function delete(Category $category): bool;

    /**
     * ID ile kategoriyi sil
     */
    public function deleteById(int $id): bool;

    /**
     * Kategori var mı kontrol et
     */
    public function exists(int $id): bool;

    /**
     * Slug var mı kontrol et
     */
    public function existsBySlug(CategorySlug $slug): bool;

    /**
     * Alt kategorisi var mı kontrol et
     */
    public function hasChildren(int $categoryId): bool;

    /**
     * Ürünü var mı kontrol et
     */
    public function hasProducts(int $categoryId): bool;

    /**
     * Kategori seviyesini al
     */
    public function getLevel(int $categoryId): int;

    /**
     * Maksimum seviyeyi al
     */
    public function getMaxLevel(): int;

    /**
     * Kategori pozisyonunu güncelle
     */
    public function updatePosition(int $categoryId, int $position): bool;

    /**
     * Toplu pozisyon güncelleme
     */
    public function bulkUpdatePositions(array $positions): bool;

    /**
     * Kategori yolunu güncelle
     */
    public function updatePath(int $categoryId, CategoryPath $path): bool;

    /**
     * Alt kategorilerin yollarını güncelle
     */
    public function updateDescendantPaths(int $categoryId, CategoryPath $newPath): bool;

    /**
     * Kategori seviyesini güncelle
     */
    public function updateLevel(int $categoryId, int $level): bool;

    /**
     * Alt kategorilerin seviyelerini güncelle
     */
    public function updateDescendantLevels(int $categoryId, int $levelDifference): bool;

    /**
     * Ürün sayısını güncelle
     */
    public function updateProductCount(int $categoryId, int $count): bool;

    /**
     * Tüm kategorilerin ürün sayılarını yeniden hesapla
     */
    public function recalculateProductCounts(): bool;

    /**
     * Toplu durum güncelleme
     */
    public function bulkUpdateStatus(array $categoryIds, bool $status): bool;

    /**
     * Kategori istatistiklerini al
     */
    public function getStatistics(): array;

    /**
     * Seviye bazlı istatistikleri al
     */
    public function getLevelStatistics(): array;

    /**
     * Boş kategorileri al (ürünü olmayan)
     */
    public function findEmpty(int $limit = 100, int $offset = 0): array;

    /**
     * Popüler kategorileri al (ürün sayısına göre)
     */
    public function findPopular(int $limit = 10): array;

    /**
     * Son eklenen kategorileri al
     */
    public function findLatest(int $limit = 10): array;

    /**
     * Belirli bir tarihten sonra güncellenen kategorileri al
     */
    public function findUpdatedAfter(\DateTime $date, int $limit = 100, int $offset = 0): array;

    /**
     * Kategori ağacını yeniden düzenle
     */
    public function rebuildTree(): bool;

    /**
     * Kategori yollarını yeniden hesapla
     */
    public function rebuildPaths(): bool;

    /**
     * Kategori seviyelerini yeniden hesapla
     */
    public function rebuildLevels(): bool;
}
