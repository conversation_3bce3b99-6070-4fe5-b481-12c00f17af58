<?php

namespace App\Domain\Categories\Entities;

use App\Domain\Categories\ValueObjects\CategoryPath;
use App\Domain\Categories\ValueObjects\CategorySlug;
use App\Domain\Categories\ValueObjects\SEOData;
use App\Domain\Categories\Events\CategoryCreated;
use App\Domain\Categories\Events\CategoryUpdated;
use App\Domain\Categories\Events\CategoryMoved;
use App\Domain\Categories\Events\CategoryDeleted;
use App\Domain\Shared\Traits\AggregateRoot;
use Carbon\Carbon;

class Category
{
    use AggregateRoot;

    private ?int $id;
    private string $name;
    private CategorySlug $slug;
    private ?string $description;
    private ?int $parentId;
    private CategoryPath $path;
    private int $level;
    private int $position;
    private bool $status;
    private bool $featured;
    private bool $showInMenu;
    private ?string $image;
    private ?string $icon;
    private ?SEOData $seoData;
    private array $children = [];
    private array $attributes = [];
    private int $productCount = 0;
    private Carbon $createdAt;
    private Carbon $updatedAt;

    public function __construct(
        string $name,
        CategorySlug $slug,
        ?int $parentId = null,
        ?string $description = null,
        int $position = 0,
        bool $status = true,
        bool $featured = false,
        bool $showInMenu = true
    ) {
        $this->id = null; // Initialize as null, will be set by repository
        $this->name = $name;
        $this->slug = $slug;
        $this->parentId = $parentId;
        $this->description = $description;
        $this->position = $position;
        $this->status = $status;
        $this->featured = $featured;
        $this->showInMenu = $showInMenu;
        $this->image = null; // Initialize as null
        $this->icon = null; // Initialize as null
        $this->seoData = null; // Initialize as null
        $this->children = []; // Initialize as empty array
        $this->attributes = []; // Initialize as empty array
        $this->productCount = 0; // Initialize as 0
        $this->level = $parentId ? 1 : 0; // Will be calculated properly later
        $this->path = CategoryPath::fromParentId($parentId);
        $this->createdAt = Carbon::now();
        $this->updatedAt = Carbon::now();

        $this->recordEvent(new CategoryCreated($this));
    }

    public static function create(
        string $name,
        CategorySlug $slug,
        ?int $parentId = null,
        ?string $description = null,
        int $position = 0,
        bool $status = true,
        bool $featured = false,
        bool $showInMenu = true
    ): self {
        return new self(
            $name,
            $slug,
            $parentId,
            $description,
            $position,
            $status,
            $featured,
            $showInMenu
        );
    }

    public function updateBasicInfo(
        string $name,
        CategorySlug $slug,
        ?string $description = null
    ): void {
        $oldName = $this->name;
        $oldSlug = $this->slug;

        $this->name = $name;
        $this->slug = $slug;
        $this->description = $description;
        $this->updatedAt = Carbon::now();

        $this->recordEvent(new CategoryUpdated($this, [
            'old_name' => $oldName,
            'new_name' => $name,
            'old_slug' => $oldSlug->getValue(),
            'new_slug' => $slug->getValue()
        ]));
    }

    public function moveToParent(?int $newParentId): void
    {
        $oldParentId = $this->parentId;
        $oldPath = $this->path;
        $oldLevel = $this->level;

        $this->parentId = $newParentId;
        $this->path = CategoryPath::fromParentId($newParentId);
        $this->level = $newParentId ? $this->calculateLevel() : 0;
        $this->updatedAt = Carbon::now();

        $this->recordEvent(new CategoryMoved($this, $oldParentId, $newParentId, $oldPath, $this->path));
    }

    public function updatePosition(int $position): void
    {
        $oldPosition = $this->position;
        $this->position = $position;
        $this->updatedAt = Carbon::now();

        $this->recordEvent(new CategoryUpdated($this, [
            'old_position' => $oldPosition,
            'new_position' => $position
        ]));
    }

    public function activate(): void
    {
        $this->status = true;
        $this->updatedAt = Carbon::now();
    }

    public function deactivate(): void
    {
        $this->status = false;
        $this->updatedAt = Carbon::now();
    }

    public function setFeatured(bool $featured): void
    {
        $this->featured = $featured;
        $this->updatedAt = Carbon::now();
    }

    public function setMenuVisibility(bool $showInMenu): void
    {
        $this->showInMenu = $showInMenu;
        $this->updatedAt = Carbon::now();
    }

    public function setImage(?string $image): void
    {
        $this->image = $image;
        $this->updatedAt = Carbon::now();
    }

    public function setIcon(?string $icon): void
    {
        $this->icon = $icon;
        $this->updatedAt = Carbon::now();
    }

    public function setSEOData(SEOData $seoData): void
    {
        $this->seoData = $seoData;
        $this->updatedAt = Carbon::now();
    }

    public function addChild(Category $child): void
    {
        $this->children[] = $child;
        $this->updatedAt = Carbon::now();
    }

    public function removeChild(int $childId): void
    {
        $this->children = array_filter(
            $this->children,
            fn($child) => $child->getId() !== $childId
        );
        $this->updatedAt = Carbon::now();
    }

    public function addAttribute(CategoryAttribute $attribute): void
    {
        $this->attributes[] = $attribute;
        $this->updatedAt = Carbon::now();
    }

    public function updateProductCount(int $count): void
    {
        $this->productCount = $count;
        $this->updatedAt = Carbon::now();
    }

    public function delete(): void
    {
        if ($this->hasChildren()) {
            throw new \InvalidArgumentException('Cannot delete category with children');
        }

        if ($this->hasProducts()) {
            throw new \InvalidArgumentException('Cannot delete category with products');
        }

        $this->recordEvent(new CategoryDeleted($this));
    }

    public function isRoot(): bool
    {
        return $this->parentId === null;
    }

    public function hasParent(): bool
    {
        return $this->parentId !== null;
    }

    public function hasChildren(): bool
    {
        return !empty($this->children);
    }

    public function hasProducts(): bool
    {
        return $this->productCount > 0;
    }

    public function isActive(): bool
    {
        return $this->status;
    }

    public function isFeatured(): bool
    {
        return $this->featured;
    }

    public function isVisibleInMenu(): bool
    {
        return $this->showInMenu;
    }

    public function getDepth(): int
    {
        return $this->level;
    }

    public function isDescendantOf(Category $category): bool
    {
        return $this->path->isDescendantOf($category->getPath());
    }

    public function isAncestorOf(Category $category): bool
    {
        return $category->getPath()->isDescendantOf($this->path);
    }

    public function isSiblingOf(Category $category): bool
    {
        return $this->parentId === $category->getParentId() && $this->id !== $category->getId();
    }

    public function canMoveTo(?int $newParentId): bool
    {
        // Cannot move to itself
        if ($newParentId === $this->id) {
            return false;
        }

        // Cannot move to its own descendant
        if ($newParentId !== null) {
            foreach ($this->children as $child) {
                if ($child->getId() === $newParentId || $child->isAncestorOf($this)) {
                    return false;
                }
            }
        }

        return true;
    }

    private function calculateLevel(): int
    {
        // This would typically be calculated based on the parent's level
        // For now, we'll use a simple calculation
        return $this->parentId ? 1 : 0;
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getName(): string { return $this->name; }
    public function getSlug(): CategorySlug { return $this->slug; }
    public function getDescription(): ?string { return $this->description; }
    public function getParentId(): ?int { return $this->parentId; }
    public function getPath(): CategoryPath { return $this->path; }
    public function getLevel(): int { return $this->level; }
    public function getPosition(): int { return $this->position; }
    public function getStatus(): bool { return $this->status; }
    public function getImage(): ?string { return $this->image; }
    public function getIcon(): ?string { return $this->icon; }
    public function getSEOData(): ?SEOData { return $this->seoData; }
    public function getChildren(): array { return $this->children; }
    public function getAttributes(): array { return $this->attributes; }
    public function getProductCount(): int { return $this->productCount; }
    public function getCreatedAt(): Carbon { return $this->createdAt; }
    public function getUpdatedAt(): Carbon { return $this->updatedAt; }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
    public function setLevel(int $level): void { $this->level = $level; }
    public function setPath(CategoryPath $path): void { $this->path = $path; }
}
