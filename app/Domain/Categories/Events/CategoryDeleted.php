<?php

namespace App\Domain\Categories\Events;

use App\Domain\Categories\Entities\Category;
use App\Domain\Shared\Events\DomainEvent;
use Carbon\Carbon;

class CategoryDeleted implements DomainEvent
{
    private Category $category;
    private Carbon $occurredOn;

    public function __construct(Category $category)
    {
        $this->category = $category;
        $this->occurredOn = Carbon::now();
    }

    public function getCategory(): Category
    {
        return $this->category;
    }

    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    public function getEventName(): string
    {
        return 'category.deleted';
    }

    public function getEventData(): array
    {
        return [
            'category_id' => $this->category->getId(),
            'name' => $this->category->getName(),
            'slug' => $this->category->getSlug()->getValue(),
            'parent_id' => $this->category->getParentId(),
            'path' => $this->category->getPath()->getPathString(),
            'level' => $this->category->getLevel(),
            'position' => $this->category->getPosition(),
            'product_count' => $this->category->getProductCount(),
            'deleted_at' => $this->occurredOn->toISOString(),
        ];
    }

    public function getAggregateId(): string
    {
        return (string) $this->category->getId();
    }

    public function getAggregateType(): string
    {
        return 'Category';
    }
}
