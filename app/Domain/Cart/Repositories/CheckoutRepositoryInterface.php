<?php

namespace App\Domain\Cart\Repositories;

use App\Domain\Cart\Entities\CheckoutProcess;
use App\Domain\Cart\ValueObjects\CheckoutStep;

/**
 * CheckoutRepositoryInterface
 * Checkout process domain repository interface
 */
interface CheckoutRepositoryInterface
{
    /**
     * Checkout process'i kaydet
     */
    public function save(CheckoutProcess $checkoutProcess): CheckoutProcess;

    /**
     * ID ile checkout process bul
     */
    public function findById(int $id): ?CheckoutProcess;

    /**
     * Cart ID ile checkout process bul
     */
    public function findByCartId(int $cartId): ?CheckoutProcess;

    /**
     * Kullanıcı ID ile aktif checkout process bul
     */
    public function findActiveByUserId(int $userId): ?CheckoutProcess;

    /**
     * Session ID ile aktif checkout process bul
     */
    public function findActiveBySessionId(string $sessionId): ?CheckoutProcess;

    /**
     * Duruma göre checkout process'leri bul
     */
    public function findByStatus(string $status, int $limit = 10, int $offset = 0): array;

    /**
     * Adıma göre checkout process'leri bul
     */
    public function findByCurrentStep(CheckoutStep $step, int $limit = 10, int $offset = 0): array;

    /**
     * Checkout process'i sil
     */
    public function delete(CheckoutProcess $checkoutProcess): bool;

    /**
     * ID ile checkout process'i sil
     */
    public function deleteById(int $id): bool;

    /**
     * Checkout process var mı kontrol et
     */
    public function exists(int $id): bool;

    /**
     * Cart için aktif checkout var mı kontrol et
     */
    public function existsActiveForCart(int $cartId): bool;

    /**
     * Kullanıcı için aktif checkout var mı kontrol et
     */
    public function existsActiveForUser(int $userId): bool;

    /**
     * Terk edilmiş checkout process'leri bul
     */
    public function findAbandoned(int $hoursOld = 24, int $limit = 100): array;

    /**
     * Tamamlanmamış checkout process'leri temizle
     */
    public function clearIncomplete(int $daysOld = 7): int;

    /**
     * Checkout istatistiklerini al
     */
    public function getCheckoutStatistics(): array;

    /**
     * Checkout dönüşüm oranını al
     */
    public function getConversionRate(): float;

    /**
     * Adım bazında dönüşüm oranlarını al
     */
    public function getStepConversionRates(): array;

    /**
     * Ortalama checkout süresini al
     */
    public function getAverageCheckoutTime(): float;

    /**
     * En çok terk edilen adımları al
     */
    public function getMostAbandonedSteps(int $limit = 5): array;

    /**
     * Checkout funnel analizini al
     */
    public function getFunnelAnalysis(): array;

    /**
     * Tarih aralığında checkout sayısını al
     */
    public function countByDateRange(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate): int;

    /**
     * Başarılı checkout'ları al
     */
    public function findCompleted(int $limit = 10, int $offset = 0): array;

    /**
     * İptal edilen checkout'ları al
     */
    public function findCancelled(int $limit = 10, int $offset = 0): array;

    /**
     * Devam eden checkout'ları al
     */
    public function findInProgress(int $limit = 10, int $offset = 0): array;
}
