<?php

namespace App\Domain\Cart\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;

/**
 * PaymentMethod Value Object
 * Ödeme yöntemi için immutable value object
 */
class PaymentMethod extends ValueObject
{
    public const CREDIT_CARD = 'credit_card';
    public const BANK_TRANSFER = 'bank_transfer';
    public const CASH_ON_DELIVERY = 'cash_on_delivery';
    public const DIGITAL_WALLET = 'digital_wallet';
    public const INSTALLMENT = 'installment';

    private string $type;
    private string $provider;
    private array $metadata;

    private function __construct(string $type, string $provider = '', array $metadata = [])
    {
        $this->validate($type);
        $this->type = $type;
        $this->provider = $provider;
        $this->metadata = $metadata;
    }

    /**
     * Kredi kartı ödeme yöntemi
     */
    public static function creditCard(string $provider = 'iyzico', array $metadata = []): self
    {
        return new self(self::CREDIT_CARD, $provider, $metadata);
    }

    /**
     * Banka havalesi ödeme yöntemi
     */
    public static function bankTransfer(string $provider = '', array $metadata = []): self
    {
        return new self(self::BANK_TRANSFER, $provider, $metadata);
    }

    /**
     * Kapıda ödeme yöntemi
     */
    public static function cashOnDelivery(array $metadata = []): self
    {
        return new self(self::CASH_ON_DELIVERY, '', $metadata);
    }

    /**
     * Dijital cüzdan ödeme yöntemi
     */
    public static function digitalWallet(string $provider = 'paywithiyzico', array $metadata = []): self
    {
        return new self(self::DIGITAL_WALLET, $provider, $metadata);
    }

    /**
     * Taksitli ödeme yöntemi
     */
    public static function installment(string $provider = 'iyzico', array $metadata = []): self
    {
        return new self(self::INSTALLMENT, $provider, $metadata);
    }

    /**
     * String'den PaymentMethod oluştur
     */
    public static function fromString(string $type, string $provider = '', array $metadata = []): self
    {
        return new self($type, $provider, $metadata);
    }

    /**
     * Array'den PaymentMethod oluştur
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['type'] ?? '',
            $data['provider'] ?? '',
            $data['metadata'] ?? []
        );
    }

    /**
     * Ödeme yöntemi tipini getir
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * Ödeme sağlayıcısını getir
     */
    public function getProvider(): string
    {
        return $this->provider;
    }

    /**
     * Metadata'yı getir
     */
    public function getMetadata(): array
    {
        return $this->metadata;
    }

    /**
     * Belirli metadata değerini getir
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    /**
     * Ödeme yöntemi adını getir
     */
    public function getDisplayName(): string
    {
        return match ($this->type) {
            self::CREDIT_CARD => 'Kredi Kartı',
            self::BANK_TRANSFER => 'Banka Havalesi',
            self::CASH_ON_DELIVERY => 'Kapıda Ödeme',
            self::DIGITAL_WALLET => 'Dijital Cüzdan',
            self::INSTALLMENT => 'Taksitli Ödeme',
            default => 'Bilinmeyen Ödeme Yöntemi'
        };
    }

    /**
     * Online ödeme mi kontrol et
     */
    public function isOnlinePayment(): bool
    {
        return in_array($this->type, [
            self::CREDIT_CARD,
            self::DIGITAL_WALLET,
            self::INSTALLMENT
        ]);
    }

    /**
     * Offline ödeme mi kontrol et
     */
    public function isOfflinePayment(): bool
    {
        return in_array($this->type, [
            self::BANK_TRANSFER,
            self::CASH_ON_DELIVERY
        ]);
    }

    /**
     * Anında ödeme mi kontrol et
     */
    public function isInstantPayment(): bool
    {
        return in_array($this->type, [
            self::CREDIT_CARD,
            self::DIGITAL_WALLET,
            self::INSTALLMENT
        ]);
    }

    /**
     * Taksitli ödeme mi kontrol et
     */
    public function isInstallmentPayment(): bool
    {
        return $this->type === self::INSTALLMENT;
    }

    /**
     * 3D Secure gerekli mi kontrol et
     */
    public function requires3DSecure(): bool
    {
        return $this->isOnlinePayment() && 
               $this->getMetadataValue('requires_3d_secure', true);
    }

    /**
     * Komisyon oranını getir
     */
    public function getCommissionRate(): float
    {
        return (float) $this->getMetadataValue('commission_rate', 0.0);
    }

    /**
     * Minimum tutarı getir
     */
    public function getMinimumAmount(): float
    {
        return (float) $this->getMetadataValue('minimum_amount', 0.0);
    }

    /**
     * Maksimum tutarı getir
     */
    public function getMaximumAmount(): ?float
    {
        $max = $this->getMetadataValue('maximum_amount');
        return $max ? (float) $max : null;
    }

    /**
     * Desteklenen para birimlerini getir
     */
    public function getSupportedCurrencies(): array
    {
        return $this->getMetadataValue('supported_currencies', ['TRY']);
    }

    /**
     * Para birimi destekleniyor mu kontrol et
     */
    public function supportsCurrency(string $currency): bool
    {
        return in_array(strtoupper($currency), 
                       array_map('strtoupper', $this->getSupportedCurrencies()));
    }

    /**
     * Geçerli ödeme yöntemlerini getir
     */
    public static function getValidTypes(): array
    {
        return [
            self::CREDIT_CARD,
            self::BANK_TRANSFER,
            self::CASH_ON_DELIVERY,
            self::DIGITAL_WALLET,
            self::INSTALLMENT,
        ];
    }

    /**
     * Değeri validate et
     */
    protected function validate(string $type): void
    {
        if (empty(trim($type))) {
            throw new \InvalidArgumentException('Payment method type cannot be empty');
        }

        $validTypes = self::getValidTypes();
        
        if (!in_array($type, $validTypes)) {
            throw new \InvalidArgumentException(
                "Invalid payment method type: {$type}. Valid types are: " . implode(', ', $validTypes)
            );
        }
    }

    /**
     * Eşitlik kontrolü
     */
    public function equals(ValueObject $other): bool
    {
        return $other instanceof self && 
               $this->type === $other->type && 
               $this->provider === $other->provider;
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'type' => $this->type,
            'provider' => $this->provider,
            'display_name' => $this->getDisplayName(),
            'is_online' => $this->isOnlinePayment(),
            'is_offline' => $this->isOfflinePayment(),
            'is_instant' => $this->isInstantPayment(),
            'is_installment' => $this->isInstallmentPayment(),
            'requires_3d_secure' => $this->requires3DSecure(),
            'commission_rate' => $this->getCommissionRate(),
            'minimum_amount' => $this->getMinimumAmount(),
            'maximum_amount' => $this->getMaximumAmount(),
            'supported_currencies' => $this->getSupportedCurrencies(),
            'metadata' => $this->metadata,
        ];
    }
}
