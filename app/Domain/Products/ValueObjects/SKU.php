<?php

namespace App\Domain\Products\ValueObjects;

use InvalidArgumentException;

class SKU
{
    private string $value;

    public function __construct(string $value)
    {
        $this->validate($value);
        $this->value = strtoupper(trim($value));
    }

    public static function generate(string $prefix = 'SKU'): self
    {
        $value = $prefix . '-' . strtoupper(substr(md5(uniqid()), 0, 8));
        return new self($value);
    }

    public static function fromString(string $value): self
    {
        return new self($value);
    }

    private function validate(string $value): void
    {
        $value = trim($value);
        
        if (empty($value)) {
            throw new InvalidArgumentException('SKU cannot be empty');
        }

        if (strlen($value) < 3) {
            throw new InvalidArgumentException('SKU must be at least 3 characters long');
        }

        if (strlen($value) > 50) {
            throw new InvalidArgumentException('SKU cannot be longer than 50 characters');
        }

        // Sad<PERSON>e alfan<PERSON><PERSON> karak<PERSON>, tire ve alt çizgi kabul et
        if (!preg_match('/^[A-Za-z0-9\-_]+$/', $value)) {
            throw new InvalidArgumentException('SKU can only contain alphanumeric characters, hyphens and underscores');
        }
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function equals(SKU $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
