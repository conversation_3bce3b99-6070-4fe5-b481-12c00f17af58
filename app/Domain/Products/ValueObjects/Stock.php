<?php

namespace App\Domain\Products\ValueObjects;

use InvalidArgumentException;

class Stock
{
    private int $quantity;
    private int $reservedQuantity;
    private int $lowStockThreshold;
    private bool $trackQuantity;
    private bool $allowBackorders;

    public function __construct(
        int $quantity,
        int $reservedQuantity = 0,
        int $lowStockThreshold = 5,
        bool $trackQuantity = true,
        bool $allowBackorders = false
    ) {
        $this->validate($quantity, $reservedQuantity, $lowStockThreshold);
        
        $this->quantity = $quantity;
        $this->reservedQuantity = $reservedQuantity;
        $this->lowStockThreshold = $lowStockThreshold;
        $this->trackQuantity = $trackQuantity;
        $this->allowBackorders = $allowBackorders;
    }

    public static function unlimited(): self
    {
        return new self(
            quantity: 999999,
            trackQuantity: false,
            allowBackorders: true
        );
    }

    public static function outOfStock(): self
    {
        return new self(0);
    }

    public static function fromQuantity(int $quantity): self
    {
        return new self($quantity);
    }

    private function validate(int $quantity, int $reservedQuantity, int $lowStockThreshold): void
    {
        if ($quantity < 0) {
            throw new InvalidArgumentException('Stock quantity cannot be negative');
        }

        if ($reservedQuantity < 0) {
            throw new InvalidArgumentException('Reserved quantity cannot be negative');
        }

        if ($reservedQuantity > $quantity) {
            throw new InvalidArgumentException('Reserved quantity cannot be greater than total quantity');
        }

        if ($lowStockThreshold < 0) {
            throw new InvalidArgumentException('Low stock threshold cannot be negative');
        }
    }

    public function increase(int $amount): Stock
    {
        if ($amount <= 0) {
            throw new InvalidArgumentException('Amount must be positive');
        }

        return new Stock(
            $this->quantity + $amount,
            $this->reservedQuantity,
            $this->lowStockThreshold,
            $this->trackQuantity,
            $this->allowBackorders
        );
    }

    public function decrease(int $amount): Stock
    {
        if ($amount <= 0) {
            throw new InvalidArgumentException('Amount must be positive');
        }

        $newQuantity = $this->quantity - $amount;
        
        if ($newQuantity < 0 && !$this->allowBackorders) {
            throw new InvalidArgumentException('Cannot decrease stock below zero when backorders are not allowed');
        }

        return new Stock(
            max(0, $newQuantity),
            $this->reservedQuantity,
            $this->lowStockThreshold,
            $this->trackQuantity,
            $this->allowBackorders
        );
    }

    public function reserve(int $amount): Stock
    {
        if ($amount <= 0) {
            throw new InvalidArgumentException('Amount must be positive');
        }

        if (!$this->canReserve($amount)) {
            throw new InvalidArgumentException("Cannot reserve {$amount} items. Available: {$this->getAvailableQuantity()}");
        }

        return new Stock(
            $this->quantity,
            $this->reservedQuantity + $amount,
            $this->lowStockThreshold,
            $this->trackQuantity,
            $this->allowBackorders
        );
    }

    public function releaseReservation(int $amount): Stock
    {
        if ($amount <= 0) {
            throw new InvalidArgumentException('Amount must be positive');
        }

        if ($amount > $this->reservedQuantity) {
            throw new InvalidArgumentException("Cannot release {$amount} items. Reserved: {$this->reservedQuantity}");
        }

        return new Stock(
            $this->quantity,
            $this->reservedQuantity - $amount,
            $this->lowStockThreshold,
            $this->trackQuantity,
            $this->allowBackorders
        );
    }

    public function fulfillReservation(int $amount): Stock
    {
        if ($amount <= 0) {
            throw new InvalidArgumentException('Amount must be positive');
        }

        if ($amount > $this->reservedQuantity) {
            throw new InvalidArgumentException("Cannot fulfill {$amount} items. Reserved: {$this->reservedQuantity}");
        }

        return new Stock(
            $this->quantity - $amount,
            $this->reservedQuantity - $amount,
            $this->lowStockThreshold,
            $this->trackQuantity,
            $this->allowBackorders
        );
    }

    public function setLowStockThreshold(int $threshold): Stock
    {
        if ($threshold < 0) {
            throw new InvalidArgumentException('Low stock threshold cannot be negative');
        }

        return new Stock(
            $this->quantity,
            $this->reservedQuantity,
            $threshold,
            $this->trackQuantity,
            $this->allowBackorders
        );
    }

    public function enableBackorders(): Stock
    {
        return new Stock(
            $this->quantity,
            $this->reservedQuantity,
            $this->lowStockThreshold,
            $this->trackQuantity,
            true
        );
    }

    public function disableBackorders(): Stock
    {
        return new Stock(
            $this->quantity,
            $this->reservedQuantity,
            $this->lowStockThreshold,
            $this->trackQuantity,
            false
        );
    }

    public function enableTracking(): Stock
    {
        return new Stock(
            $this->quantity,
            $this->reservedQuantity,
            $this->lowStockThreshold,
            true,
            $this->allowBackorders
        );
    }

    public function disableTracking(): Stock
    {
        return new Stock(
            $this->quantity,
            $this->reservedQuantity,
            $this->lowStockThreshold,
            false,
            $this->allowBackorders
        );
    }

    public function canReserve(int $amount): bool
    {
        if (!$this->trackQuantity) {
            return true;
        }

        return $this->getAvailableQuantity() >= $amount || $this->allowBackorders;
    }

    public function isInStock(): bool
    {
        if (!$this->trackQuantity) {
            return true;
        }

        return $this->getAvailableQuantity() > 0 || $this->allowBackorders;
    }

    public function isLowStock(): bool
    {
        if (!$this->trackQuantity) {
            return false;
        }

        return $this->getAvailableQuantity() <= $this->lowStockThreshold;
    }

    public function isOutOfStock(): bool
    {
        if (!$this->trackQuantity) {
            return false;
        }

        return $this->getAvailableQuantity() <= 0 && !$this->allowBackorders;
    }

    public function getAvailableQuantity(): int
    {
        return max(0, $this->quantity - $this->reservedQuantity);
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function getReservedQuantity(): int
    {
        return $this->reservedQuantity;
    }

    public function getLowStockThreshold(): int
    {
        return $this->lowStockThreshold;
    }

    public function isTrackingEnabled(): bool
    {
        return $this->trackQuantity;
    }

    public function areBackordersAllowed(): bool
    {
        return $this->allowBackorders;
    }

    public function isBackorderAllowed(): bool
    {
        return $this->allowBackorders;
    }

    public function getReorderPoint(): ?int
    {
        // Varsayılan olarak low stock threshold'u reorder point olarak kullan
        return $this->lowStockThreshold;
    }

    public function getReorderQuantity(): ?int
    {
        // Varsayılan reorder quantity hesapla (threshold'un 5 katı)
        return $this->lowStockThreshold * 5;
    }

    public function getLeadTime(): ?int
    {
        // Varsayılan lead time (gün cinsinden)
        return 7; // 7 gün
    }

    public function getStatus(): string
    {
        if (!$this->trackQuantity) {
            return 'unlimited';
        }

        if ($this->isOutOfStock()) {
            return 'out_of_stock';
        }

        if ($this->isLowStock()) {
            return 'low_stock';
        }

        return 'in_stock';
    }

    public function toArray(): array
    {
        return [
            'quantity' => $this->quantity,
            'reserved_quantity' => $this->reservedQuantity,
            'available_quantity' => $this->getAvailableQuantity(),
            'low_stock_threshold' => $this->lowStockThreshold,
            'track_quantity' => $this->trackQuantity,
            'allow_backorders' => $this->allowBackorders,
            'status' => $this->getStatus(),
            'is_in_stock' => $this->isInStock(),
            'is_low_stock' => $this->isLowStock(),
            'is_out_of_stock' => $this->isOutOfStock(),
        ];
    }

    public function equals(Stock $other): bool
    {
        return $this->quantity === $other->quantity &&
               $this->reservedQuantity === $other->reservedQuantity &&
               $this->lowStockThreshold === $other->lowStockThreshold &&
               $this->trackQuantity === $other->trackQuantity &&
               $this->allowBackorders === $other->allowBackorders;
    }
}
