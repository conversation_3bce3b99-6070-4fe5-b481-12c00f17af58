<?php

namespace App\Domain\Products\ValueObjects;

use InvalidArgumentException;

class Price
{
    private float $amount;
    private string $currency;

    public function __construct(float $amount, string $currency = 'TRY')
    {
        $this->validate($amount, $currency);
        $this->amount = round($amount, 2);
        $this->currency = strtoupper($currency);
    }

    public static function fromAmount(float $amount, string $currency = 'TRY'): self
    {
        return new self($amount, $currency);
    }

    public static function zero(string $currency = 'TRY'): self
    {
        return new self(0.0, $currency);
    }

    public static function free(): self
    {
        return new self(0.0, 'TRY');
    }

    private function validate(float $amount, string $currency): void
    {
        if ($amount < 0) {
            throw new InvalidArgumentException('Price amount cannot be negative');
        }

        if (empty($currency)) {
            throw new InvalidArgumentException('Currency cannot be empty');
        }

        if (strlen($currency) !== 3) {
            throw new InvalidArgumentException('Currency must be 3 characters long');
        }
    }

    public function add(Price $other): Price
    {
        $this->ensureSameCurrency($other);
        return new Price($this->amount + $other->amount, $this->currency);
    }

    public function subtract(Price $other): Price
    {
        $this->ensureSameCurrency($other);
        $newAmount = $this->amount - $other->amount;
        
        if ($newAmount < 0) {
            throw new InvalidArgumentException('Subtraction would result in negative price');
        }
        
        return new Price($newAmount, $this->currency);
    }

    public function multiply(float $multiplier): Price
    {
        if ($multiplier < 0) {
            throw new InvalidArgumentException('Multiplier cannot be negative');
        }
        
        return new Price($this->amount * $multiplier, $this->currency);
    }

    public function divide(float $divisor): Price
    {
        if ($divisor <= 0) {
            throw new InvalidArgumentException('Divisor must be greater than zero');
        }
        
        return new Price($this->amount / $divisor, $this->currency);
    }

    public function percentage(float $percentage): Price
    {
        if ($percentage < 0) {
            throw new InvalidArgumentException('Percentage cannot be negative');
        }
        
        return new Price($this->amount * ($percentage / 100), $this->currency);
    }

    public function discount(float $percentage): Price
    {
        if ($percentage < 0 || $percentage > 100) {
            throw new InvalidArgumentException('Discount percentage must be between 0 and 100');
        }
        
        $discountAmount = $this->amount * ($percentage / 100);
        return new Price($this->amount - $discountAmount, $this->currency);
    }

    public function addTax(float $taxRate): Price
    {
        if ($taxRate < 0) {
            throw new InvalidArgumentException('Tax rate cannot be negative');
        }
        
        $taxAmount = $this->amount * ($taxRate / 100);
        return new Price($this->amount + $taxAmount, $this->currency);
    }

    public function isGreaterThan(Price $other): bool
    {
        $this->ensureSameCurrency($other);
        return $this->amount > $other->amount;
    }

    public function isLessThan(Price $other): bool
    {
        $this->ensureSameCurrency($other);
        return $this->amount < $other->amount;
    }

    public function isEqualTo(Price $other): bool
    {
        $this->ensureSameCurrency($other);
        return abs($this->amount - $other->amount) < 0.01; // Float comparison with tolerance
    }

    public function isGreaterThanOrEqualTo(Price $other): bool
    {
        return $this->isGreaterThan($other) || $this->isEqualTo($other);
    }

    public function isLessThanOrEqualTo(Price $other): bool
    {
        return $this->isLessThan($other) || $this->isEqualTo($other);
    }

    public function isZero(): bool
    {
        return abs($this->amount) < 0.01;
    }

    public function isPositive(): bool
    {
        return $this->amount > 0.01;
    }

    public function isFree(): bool
    {
        return $this->isZero();
    }

    private function ensureSameCurrency(Price $other): void
    {
        if ($this->currency !== $other->currency) {
            throw new InvalidArgumentException(
                "Cannot perform operation on different currencies: {$this->currency} and {$other->currency}"
            );
        }
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function format(): string
    {
        return match($this->currency) {
            'TRY' => number_format($this->amount, 2, ',', '.') . ' ₺',
            'USD' => '$' . number_format($this->amount, 2, '.', ','),
            'EUR' => '€' . number_format($this->amount, 2, ',', '.'),
            default => number_format($this->amount, 2, '.', ',') . ' ' . $this->currency,
        };
    }

    public function toArray(): array
    {
        return [
            'amount' => $this->amount,
            'currency' => $this->currency,
            'formatted' => $this->format(),
        ];
    }

    public function __toString(): string
    {
        return $this->format();
    }

    public function equals(Price $other): bool
    {
        return $this->currency === $other->currency && $this->isEqualTo($other);
    }

    /**
     * Fiyatları karşılaştır
     * @return int -1 if this < other, 0 if equal, 1 if this > other
     */
    public function compareTo(Price $other): int
    {
        $this->ensureSameCurrency($other);

        if ($this->amount < $other->amount) {
            return -1;
        } elseif ($this->amount > $other->amount) {
            return 1;
        } else {
            return 0;
        }
    }
}
