<?php

namespace App\Domain\Products\Services;

use App\Domain\Products\Entities\Product;
use App\Domain\Products\Entities\ProductVariant;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Domain\Products\Repositories\ProductRepositoryInterface;
use App\Domain\Shared\Services\BaseDomainService;

/**
 * VariantDomainService
 * Ürün varyant business logic'ini yönetir
 */
class VariantDomainService extends BaseDomainService
{
    public function __construct(
        private ProductRepositoryInterface $productRepository
    ) {}

    /**
     * Varyant oluştur
     */
    public function createVariant(
        Product $product,
        SKU $sku,
        array $attributeValues,
        Price $additionalPrice,
        Stock $stock,
        string $status = ProductVariant::STATUS_IN_STOCK,
        bool $isDefault = false
    ): ProductVariant {
        // SKU benzersizlik kontrolü
        $this->ensureUniqueSkuForVariant($sku);
        
        // Attribute kombinasyonu benzersizlik kontrolü
        $this->ensureUniqueAttributeCombination($product, $attributeValues);
        
        // Eğer bu ilk varyant ise ve default belirtilmemişse, default yap
        if (!$product->hasVariants() && !$isDefault) {
            $isDefault = true;
        }
        
        // Eğer default olarak işaretleniyorsa, diğer default'ları kaldır
        if ($isDefault) {
            $this->unsetOtherDefaultVariants($product);
        }

        $variant = ProductVariant::create(
            $product->getId(),
            $sku,
            $attributeValues,
            $additionalPrice,
            $stock,
            $status,
            $isDefault
        );

        $product->addVariant($variant);
        
        return $variant;
    }

    /**
     * Varyant güncelle
     */
    public function updateVariant(
        ProductVariant $variant,
        array $updateData
    ): ProductVariant {
        // SKU değişiyorsa benzersizlik kontrolü
        if (isset($updateData['sku'])) {
            $newSku = is_string($updateData['sku']) ? 
                SKU::fromString($updateData['sku']) : 
                $updateData['sku'];
            
            if (!$variant->getSku()->equals($newSku)) {
                $this->ensureUniqueSkuForVariant($newSku, $variant->getId());
                $variant->updateSku($newSku);
            }
        }

        // Attribute values değişiyorsa benzersizlik kontrolü
        if (isset($updateData['attribute_values'])) {
            $product = $this->productRepository->findById($variant->getProductId());
            $this->ensureUniqueAttributeCombination(
                $product, 
                $updateData['attribute_values'], 
                $variant->getId()
            );
            $variant->updateAttributeValues($updateData['attribute_values']);
        }

        // Ek fiyat güncelle
        if (isset($updateData['additional_price'])) {
            $newPrice = is_array($updateData['additional_price']) ? 
                Price::fromAmount($updateData['additional_price']['amount'], $updateData['additional_price']['currency']) :
                $updateData['additional_price'];
            $variant->updateAdditionalPrice($newPrice);
        }

        // Stok güncelle
        if (isset($updateData['stock'])) {
            $newStock = is_array($updateData['stock']) ?
                Stock::fromQuantity($updateData['stock']['quantity'], $updateData['stock']['threshold'] ?? 5) :
                $updateData['stock'];
            $variant->updateStock($newStock);
        }

        // Durum güncelle
        if (isset($updateData['status'])) {
            $variant->updateStatus($updateData['status']);
        }

        // Default durumu güncelle
        if (isset($updateData['is_default']) && $updateData['is_default']) {
            $product = $this->productRepository->findById($variant->getProductId());
            $this->unsetOtherDefaultVariants($product, $variant->getId());
            $variant->setAsDefault();
        }

        return $variant;
    }

    /**
     * Varyant sil
     */
    public function deleteVariant(ProductVariant $variant): void
    {
        // Default varyant siliniyorsa, başka bir varyantı default yap
        if ($variant->isDefault()) {
            $product = $this->productRepository->findById($variant->getProductId());
            $this->assignNewDefaultVariant($product, $variant->getId());
        }

        // Aktif siparişlerde kullanılıp kullanılmadığını kontrol et
        if ($this->isVariantInActiveOrders($variant)) {
            throw new \DomainException('Aktif siparişlerde bulunan varyant silinemez.');
        }

        $variant->delete();
    }

    /**
     * Toplu varyant oluştur (attribute kombinasyonlarından)
     */
    public function generateVariantsFromAttributes(
        Product $product,
        array $attributeGroups,
        Price $baseAdditionalPrice = null
    ): array {
        $baseAdditionalPrice = $baseAdditionalPrice ?? Price::zero();
        $combinations = $this->generateAttributeCombinations($attributeGroups);
        $variants = [];

        foreach ($combinations as $index => $combination) {
            $sku = $this->generateVariantSku($product, $combination);
            $additionalPrice = $this->calculateVariantAdditionalPrice($combination, $baseAdditionalPrice);
            $stock = Stock::fromQuantity(0); // Başlangıçta 0 stok
            
            $variant = $this->createVariant(
                $product,
                $sku,
                $combination,
                $additionalPrice,
                $stock,
                ProductVariant::STATUS_OUT_OF_STOCK,
                $index === 0 // İlk varyantı default yap
            );
            
            $variants[] = $variant;
        }

        return $variants;
    }

    /**
     * Attribute kombinasyonları oluştur
     */
    private function generateAttributeCombinations(array $attributeGroups): array
    {
        if (empty($attributeGroups)) {
            return [];
        }

        $combinations = [[]];
        
        foreach ($attributeGroups as $attributeId => $values) {
            $newCombinations = [];
            
            foreach ($combinations as $combination) {
                foreach ($values as $value) {
                    $newCombination = $combination;
                    $newCombination[$attributeId] = $value;
                    $newCombinations[] = $newCombination;
                }
            }
            
            $combinations = $newCombinations;
        }

        return $combinations;
    }

    /**
     * Varyant SKU oluştur
     */
    private function generateVariantSku(Product $product, array $attributeValues): SKU
    {
        $baseSku = $product->getSku()->getValue();
        $suffix = '';
        
        foreach ($attributeValues as $attributeId => $value) {
            $suffix .= '-' . strtoupper(substr($value, 0, 2));
        }
        
        $variantSkuValue = $baseSku . $suffix;
        
        // Benzersizlik için sayı ekle
        $counter = 1;
        $originalSkuValue = $variantSkuValue;
        
        while ($this->productRepository->findBySku(SKU::fromString($variantSkuValue))) {
            $variantSkuValue = $originalSkuValue . '-' . $counter;
            $counter++;
        }
        
        return SKU::fromString($variantSkuValue);
    }

    /**
     * Varyant ek fiyatını hesapla
     */
    private function calculateVariantAdditionalPrice(array $attributeValues, Price $basePrice): Price
    {
        // Bu metod attribute'lara göre ek fiyat hesaplayabilir
        // Şimdilik base price döndürüyoruz
        return $basePrice;
    }

    /**
     * SKU benzersizlik kontrolü
     */
    private function ensureUniqueSkuForVariant(SKU $sku, ?int $excludeVariantId = null): void
    {
        $existingProduct = $this->productRepository->findBySku($sku);
        
        if ($existingProduct) {
            throw new \DomainException("SKU '{$sku->getValue()}' zaten kullanımda.");
        }
        
        // Varyantlar arasında da kontrol et
        // Bu metod Infrastructure layer'da implement edilecek
    }

    /**
     * Attribute kombinasyonu benzersizlik kontrolü
     */
    private function ensureUniqueAttributeCombination(
        Product $product, 
        array $attributeValues, 
        ?int $excludeVariantId = null
    ): void {
        foreach ($product->getVariants() as $variant) {
            if ($excludeVariantId && $variant->getId() === $excludeVariantId) {
                continue;
            }
            
            if ($this->areAttributeValuesEqual($variant->getAttributeValues(), $attributeValues)) {
                throw new \DomainException('Bu attribute kombinasyonu zaten mevcut.');
            }
        }
    }

    /**
     * Attribute values eşitlik kontrolü
     */
    private function areAttributeValuesEqual(array $values1, array $values2): bool
    {
        if (count($values1) !== count($values2)) {
            return false;
        }
        
        foreach ($values1 as $key => $value) {
            if (!isset($values2[$key]) || $values2[$key] !== $value) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Diğer default varyantları kaldır
     */
    private function unsetOtherDefaultVariants(Product $product, ?int $excludeVariantId = null): void
    {
        foreach ($product->getVariants() as $variant) {
            if ($excludeVariantId && $variant->getId() === $excludeVariantId) {
                continue;
            }
            
            if ($variant->isDefault()) {
                $variant->unsetAsDefault();
            }
        }
    }

    /**
     * Yeni default varyant ata
     */
    private function assignNewDefaultVariant(Product $product, int $excludeVariantId): void
    {
        foreach ($product->getVariants() as $variant) {
            if ($variant->getId() !== $excludeVariantId) {
                $variant->setAsDefault();
                break;
            }
        }
    }

    /**
     * Varyantın aktif siparişlerde kullanılıp kullanılmadığını kontrol et
     */
    private function isVariantInActiveOrders(ProductVariant $variant): bool
    {
        // Bu metod Infrastructure layer'da implement edilecek
        // Şimdilik false döndürüyoruz
        return false;
    }
}
