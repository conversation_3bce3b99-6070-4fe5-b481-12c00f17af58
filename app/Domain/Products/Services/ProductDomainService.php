<?php

namespace App\Domain\Products\Services;

use App\Domain\Products\Entities\Product;
use App\Domain\Products\Entities\ProductVariant;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Domain\Products\ValueObjects\Weight;
use App\Domain\Products\ValueObjects\Dimensions;
use App\Domain\Products\ValueObjects\SEOData;
use App\Domain\Products\Repositories\ProductRepositoryInterface;
use App\Domain\Shared\Services\BaseDomainService;
use App\Domain\Products\Exceptions\ProductNotFoundException;
use Carbon\Carbon;

/**
 * ProductDomainService
 * Ürün domain business logic'ini yönetir
 */
class ProductDomainService extends BaseDomainService
{
    public function __construct(
        private ProductRepositoryInterface $productRepository
    ) {}

    /**
     * Ürün oluştur
     */
    public function createProduct(
        string $name,
        string $slug,
        SKU $sku,
        Price $price,
        Stock $stock,
        int $categoryId,
        ?string $description = null,
        bool $status = true,
        bool $isFeatured = false
    ): Product {
        // SKU benzersizlik kontrolü
        $this->ensureUniqueSkuForProduct($sku);
        
        // Slug benzersizlik kontrolü
        $this->ensureUniqueSlugForProduct($slug);

        $product = Product::create(
            $name,
            $slug,
            $sku,
            $price,
            $stock,
            $categoryId,
            $description,
            $status,
            $isFeatured
        );

        return $product;
    }

    /**
     * Ürün güncelle
     */
    public function updateProduct(
        Product $product,
        array $updateData
    ): Product {
        // Slug değişiyorsa benzersizlik kontrolü
        if (isset($updateData['slug']) && $updateData['slug'] !== $product->getSlug()) {
            $this->ensureUniqueSlugForProduct($updateData['slug'], $product->getId());
        }

        // SKU değişiyorsa benzersizlik kontrolü
        if (isset($updateData['sku'])) {
            $newSku = is_string($updateData['sku']) ? 
                SKU::fromString($updateData['sku']) : 
                $updateData['sku'];
            
            if (!$product->getSku()->equals($newSku)) {
                $this->ensureUniqueSkuForProduct($newSku, $product->getId());
            }
        }

        // Temel bilgileri güncelle
        if (isset($updateData['name']) || isset($updateData['slug']) || isset($updateData['description'])) {
            $product->updateBasicInfo(
                $updateData['name'] ?? $product->getName(),
                $updateData['slug'] ?? $product->getSlug(),
                $updateData['description'] ?? $product->getDescription()
            );
        }

        // Fiyat güncelle
        if (isset($updateData['price'])) {
            $newPrice = is_array($updateData['price']) ? 
                Price::fromAmount($updateData['price']['amount'], $updateData['price']['currency']) :
                $updateData['price'];
            $product->updatePrice($newPrice);
        }

        // Stok güncelle
        if (isset($updateData['stock'])) {
            $newStock = is_array($updateData['stock']) ?
                Stock::fromQuantity($updateData['stock']['quantity'], $updateData['stock']['threshold'] ?? 5) :
                $updateData['stock'];
            $product->updateStock($newStock);
        }

        // İndirim ayarla
        if (isset($updateData['sale_price']) || isset($updateData['sale_starts_at']) || isset($updateData['sale_ends_at'])) {
            $salePrice = isset($updateData['sale_price']) ?
                Price::fromAmount($updateData['sale_price']['amount'], $updateData['sale_price']['currency']) :
                null;
            
            $saleStartsAt = isset($updateData['sale_starts_at']) ?
                Carbon::parse($updateData['sale_starts_at']) :
                null;
            
            $saleEndsAt = isset($updateData['sale_ends_at']) ?
                Carbon::parse($updateData['sale_ends_at']) :
                null;

            if ($salePrice) {
                $product->setSale($salePrice, $saleStartsAt, $saleEndsAt);
            }
        }

        // Durum güncelle
        if (isset($updateData['status'])) {
            if ($updateData['status']) {
                $product->activate();
            } else {
                $product->deactivate();
            }
        }

        // Öne çıkarma durumu
        if (isset($updateData['is_featured'])) {
            if ($updateData['is_featured']) {
                $product->setAsFeatured();
            } else {
                $product->unsetAsFeatured();
            }
        }

        // Fiziksel özellikler
        if (isset($updateData['weight'])) {
            $weight = Weight::fromGrams($updateData['weight']['value'], $updateData['weight']['unit'] ?? 'g');
            $product->setWeight($weight);
        }

        if (isset($updateData['dimensions'])) {
            $dimensions = Dimensions::fromCentimeters(
                $updateData['dimensions']['length'],
                $updateData['dimensions']['width'],
                $updateData['dimensions']['height'],
                $updateData['dimensions']['unit'] ?? 'cm'
            );
            $product->setDimensions($dimensions);
        }

        // SEO verileri
        if (isset($updateData['seo'])) {
            $seoData = SEOData::create(
                $updateData['seo']['meta_title'] ?? null,
                $updateData['seo']['meta_description'] ?? null,
                $updateData['seo']['meta_keywords'] ?? null
            );
            $product->setSeoData($seoData);
        }

        return $product;
    }

    /**
     * Ürün sil
     */
    public function deleteProduct(Product $product): void
    {
        // Varyantları kontrol et
        if ($product->hasVariants()) {
            throw new \DomainException('Varyantları olan ürün silinemez. Önce varyantları silin.');
        }

        // Aktif siparişlerde kullanılıp kullanılmadığını kontrol et
        if ($this->isProductInActiveOrders($product)) {
            throw new \DomainException('Aktif siparişlerde bulunan ürün silinemez.');
        }

        $product->delete();
    }

    /**
     * Ürün görüntülenme sayısını artır
     */
    public function incrementViewCount(Product $product): void
    {
        $product->incrementViewCount();
    }

    /**
     * SKU benzersizlik kontrolü
     */
    private function ensureUniqueSkuForProduct(SKU $sku, ?int $excludeProductId = null): void
    {
        $existingProduct = $this->productRepository->findBySku($sku);
        
        if ($existingProduct && ($excludeProductId === null || $existingProduct->getId() !== $excludeProductId)) {
            throw new \DomainException("SKU '{$sku->getValue()}' zaten kullanımda.");
        }
    }

    /**
     * Slug benzersizlik kontrolü
     */
    private function ensureUniqueSlugForProduct(string $slug, ?int $excludeProductId = null): void
    {
        $existingProduct = $this->productRepository->findBySlug($slug);
        
        if ($existingProduct && ($excludeProductId === null || $existingProduct->getId() !== $excludeProductId)) {
            throw new \DomainException("Slug '{$slug}' zaten kullanımda.");
        }
    }

    /**
     * Ürünün aktif siparişlerde kullanılıp kullanılmadığını kontrol et
     */
    private function isProductInActiveOrders(Product $product): bool
    {
        // Bu metod Infrastructure layer'da implement edilecek
        // Şimdilik false döndürüyoruz
        return false;
    }
}
