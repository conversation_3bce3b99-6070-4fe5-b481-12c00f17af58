<?php

namespace App\Domain\Products\Services;

use App\Domain\Products\Entities\Product;
use App\Domain\Products\Entities\ProductVariant;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Domain\Products\ValueObjects\Weight;
use App\Domain\Products\ValueObjects\Dimensions;
use App\Domain\Shared\Services\BaseDomainService;
use Carbon\Carbon;

/**
 * ProductValidationService
 * Ürün validasyon business logic'ini yönetir
 */
class ProductValidationService extends BaseDomainService
{
    /**
     * Ürün oluşturma validasyonu
     */
    public function validateProductCreation(array $productData): array
    {
        $errors = [];

        // İsim validasyonu
        if (empty($productData['name'])) {
            $errors['name'] = 'Ürün adı zorunludur.';
        } elseif (strlen($productData['name']) < 3) {
            $errors['name'] = 'Ürün adı en az 3 karakter olmalıdır.';
        } elseif (strlen($productData['name']) > 255) {
            $errors['name'] = 'Ürün adı en fazla 255 karakter olabilir.';
        }

        // Slug validasyonu
        if (empty($productData['slug'])) {
            $errors['slug'] = 'Ürün slug\'ı zorunludur.';
        } elseif (!$this->isValidSlug($productData['slug'])) {
            $errors['slug'] = 'Geçersiz slug formatı. Sadece küçük harf, rakam ve tire kullanın.';
        }

        // SKU validasyonu
        if (empty($productData['sku'])) {
            $errors['sku'] = 'SKU zorunludur.';
        } elseif (!$this->isValidSku($productData['sku'])) {
            $errors['sku'] = 'Geçersiz SKU formatı.';
        }

        // Fiyat validasyonu
        if (!isset($productData['price']) || $productData['price'] < 0) {
            $errors['price'] = 'Geçerli bir fiyat giriniz.';
        }

        // Kategori validasyonu
        if (empty($productData['category_id'])) {
            $errors['category_id'] = 'Kategori seçimi zorunludur.';
        }

        // Stok validasyonu
        if (isset($productData['stock']) && $productData['stock'] < 0) {
            $errors['stock'] = 'Stok miktarı negatif olamaz.';
        }

        // İndirim validasyonu
        if (isset($productData['sale_price'])) {
            $saleValidation = $this->validateSalePrice($productData);
            $errors = array_merge($errors, $saleValidation);
        }

        // Fiziksel özellik validasyonu
        if (isset($productData['weight'])) {
            $weightValidation = $this->validateWeight($productData['weight']);
            $errors = array_merge($errors, $weightValidation);
        }

        if (isset($productData['dimensions'])) {
            $dimensionValidation = $this->validateDimensions($productData['dimensions']);
            $errors = array_merge($errors, $dimensionValidation);
        }

        return $errors;
    }

    /**
     * Ürün güncelleme validasyonu
     */
    public function validateProductUpdate(Product $product, array $updateData): array
    {
        $errors = [];

        // Mevcut ürün verilerini güncelleme verileriyle birleştir
        $mergedData = array_merge([
            'name' => $product->getName(),
            'slug' => $product->getSlug(),
            'sku' => $product->getSku()->getValue(),
            'price' => $product->getPrice()->getAmount(),
            'category_id' => $product->getCategoryId(),
        ], $updateData);

        return $this->validateProductCreation($mergedData);
    }

    /**
     * Varyant validasyonu
     */
    public function validateVariant(array $variantData): array
    {
        $errors = [];

        // SKU validasyonu
        if (empty($variantData['sku'])) {
            $errors['sku'] = 'Varyant SKU\'su zorunludur.';
        } elseif (!$this->isValidSku($variantData['sku'])) {
            $errors['sku'] = 'Geçersiz SKU formatı.';
        }

        // Attribute values validasyonu
        if (empty($variantData['attribute_values'])) {
            $errors['attribute_values'] = 'Varyant özellikleri zorunludur.';
        } elseif (!is_array($variantData['attribute_values'])) {
            $errors['attribute_values'] = 'Varyant özellikleri dizi formatında olmalıdır.';
        }

        // Ek fiyat validasyonu
        if (isset($variantData['additional_price']) && $variantData['additional_price'] < 0) {
            $errors['additional_price'] = 'Ek fiyat negatif olamaz.';
        }

        // Stok validasyonu
        if (isset($variantData['stock']) && $variantData['stock'] < 0) {
            $errors['stock'] = 'Stok miktarı negatif olamaz.';
        }

        // Durum validasyonu
        if (isset($variantData['status']) && !$this->isValidVariantStatus($variantData['status'])) {
            $errors['status'] = 'Geçersiz varyant durumu.';
        }

        return $errors;
    }

    /**
     * İndirim fiyatı validasyonu
     */
    private function validateSalePrice(array $productData): array
    {
        $errors = [];

        if ($productData['sale_price'] < 0) {
            $errors['sale_price'] = 'İndirim fiyatı negatif olamaz.';
        }

        if (isset($productData['price']) && $productData['sale_price'] >= $productData['price']) {
            $errors['sale_price'] = 'İndirim fiyatı normal fiyattan düşük olmalıdır.';
        }

        // İndirim tarihleri validasyonu
        if (isset($productData['sale_starts_at']) && isset($productData['sale_ends_at'])) {
            $startDate = Carbon::parse($productData['sale_starts_at']);
            $endDate = Carbon::parse($productData['sale_ends_at']);

            if ($endDate->lte($startDate)) {
                $errors['sale_ends_at'] = 'İndirim bitiş tarihi başlangıç tarihinden sonra olmalıdır.';
            }
        }

        return $errors;
    }

    /**
     * Ağırlık validasyonu
     */
    private function validateWeight(array $weightData): array
    {
        $errors = [];

        if (!isset($weightData['value']) || $weightData['value'] <= 0) {
            $errors['weight.value'] = 'Geçerli bir ağırlık değeri giriniz.';
        }

        if (isset($weightData['unit']) && !$this->isValidWeightUnit($weightData['unit'])) {
            $errors['weight.unit'] = 'Geçersiz ağırlık birimi.';
        }

        return $errors;
    }

    /**
     * Boyut validasyonu
     */
    private function validateDimensions(array $dimensionData): array
    {
        $errors = [];

        $requiredFields = ['length', 'width', 'height'];
        
        foreach ($requiredFields as $field) {
            if (!isset($dimensionData[$field]) || $dimensionData[$field] <= 0) {
                $errors["dimensions.{$field}"] = ucfirst($field) . ' değeri pozitif olmalıdır.';
            }
        }

        if (isset($dimensionData['unit']) && !$this->isValidDimensionUnit($dimensionData['unit'])) {
            $errors['dimensions.unit'] = 'Geçersiz boyut birimi.';
        }

        return $errors;
    }

    /**
     * Slug format kontrolü
     */
    private function isValidSlug(string $slug): bool
    {
        return preg_match('/^[a-z0-9]+(?:-[a-z0-9]+)*$/', $slug);
    }

    /**
     * SKU format kontrolü
     */
    private function isValidSku(string $sku): bool
    {
        // SKU: 3-50 karakter, alfanumerik ve tire
        return preg_match('/^[A-Z0-9-]{3,50}$/', strtoupper($sku));
    }

    /**
     * Varyant durumu kontrolü
     */
    private function isValidVariantStatus(string $status): bool
    {
        $validStatuses = [
            ProductVariant::STATUS_IN_STOCK,
            ProductVariant::STATUS_OUT_OF_STOCK,
            ProductVariant::STATUS_COMING_SOON,
            ProductVariant::STATUS_DISCONTINUED
        ];

        return in_array($status, $validStatuses);
    }

    /**
     * Ağırlık birimi kontrolü
     */
    private function isValidWeightUnit(string $unit): bool
    {
        $validUnits = ['g', 'kg', 'lb', 'oz'];
        return in_array(strtolower($unit), $validUnits);
    }

    /**
     * Boyut birimi kontrolü
     */
    private function isValidDimensionUnit(string $unit): bool
    {
        $validUnits = ['mm', 'cm', 'm', 'in', 'ft'];
        return in_array(strtolower($unit), $validUnits);
    }

    /**
     * Toplu ürün validasyonu
     */
    public function validateBulkProducts(array $productsData): array
    {
        $errors = [];

        foreach ($productsData as $index => $productData) {
            $productErrors = $this->validateProductCreation($productData);
            
            if (!empty($productErrors)) {
                $errors["product_{$index}"] = $productErrors;
            }
        }

        return $errors;
    }

    /**
     * Ürün silme validasyonu
     */
    public function validateProductDeletion(Product $product): array
    {
        $errors = [];

        // Varyant kontrolü
        if ($product->hasVariants()) {
            $errors['variants'] = 'Varyantları olan ürün silinemez. Önce varyantları silin.';
        }

        // Aktif sipariş kontrolü (Infrastructure layer'da implement edilecek)
        // if ($this->isProductInActiveOrders($product)) {
        //     $errors['orders'] = 'Aktif siparişlerde bulunan ürün silinemez.';
        // }

        return $errors;
    }

    /**
     * Stok operasyonu validasyonu
     */
    public function validateStockOperation(Product $product, int $quantity, string $operation): array
    {
        $errors = [];

        if ($quantity <= 0) {
            $errors['quantity'] = 'Miktar pozitif olmalıdır.';
        }

        if ($operation === 'decrease') {
            $currentStock = $product->getStock()->getQuantity();
            
            if ($quantity > $currentStock) {
                $errors['insufficient_stock'] = 'Yetersiz stok. Mevcut stok: ' . $currentStock;
            }
        }

        if (!in_array($operation, ['increase', 'decrease', 'set'])) {
            $errors['operation'] = 'Geçersiz stok operasyonu.';
        }

        return $errors;
    }

    /**
     * Fiyat değişikliği validasyonu
     */
    public function validatePriceChange(Product $product, Price $newPrice): array
    {
        $errors = [];

        $currentPrice = $product->getPrice();
        
        // Aşırı fiyat değişikliği kontrolü (%50'den fazla)
        $changePercentage = abs($newPrice->getAmount() - $currentPrice->getAmount()) / $currentPrice->getAmount() * 100;
        
        if ($changePercentage > 50) {
            $errors['price_change'] = 'Fiyat değişikliği %50\'den fazla olamaz. Onay gerekebilir.';
        }

        // Para birimi kontrolü
        if ($newPrice->getCurrency() !== $currentPrice->getCurrency()) {
            $errors['currency'] = 'Para birimi değiştirilemez.';
        }

        return $errors;
    }
}
