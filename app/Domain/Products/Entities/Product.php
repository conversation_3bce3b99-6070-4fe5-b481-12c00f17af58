<?php

namespace App\Domain\Products\Entities;

use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Domain\Products\ValueObjects\Weight;
use App\Domain\Products\ValueObjects\Dimensions;
use App\Domain\Products\ValueObjects\SEOData;
use App\Domain\Products\Events\ProductCreated;
use App\Domain\Products\Events\ProductUpdated;
use App\Domain\Products\Events\StockUpdated;
use App\Domain\Products\Events\PriceChanged;
use App\Domain\Shared\Traits\AggregateRoot;
use App\Domain\Shared\Contracts\EntityInterface;
use Carbon\Carbon;

class Product implements EntityInterface
{
    use AggregateRoot;

    private ?int $id;
    private string $name;
    private string $slug;
    private ?string $description;
    private SKU $sku;
    private Price $price;
    private ?Price $salePrice;
    private Stock $stock;
    private int $categoryId;
    private bool $status;
    private bool $isFeatured;
    private bool $isOnSale;
    private ?Carbon $saleStartsAt;
    private ?Carbon $saleEndsAt;
    private int $viewCount;
    private ?Weight $weight;
    private ?Dimensions $dimensions;
    private ?SEOData $seoData;
    private array $variants = [];
    private array $attributes = [];
    private array $images = [];
    private Carbon $createdAt;
    private Carbon $updatedAt;

    public function __construct(
        string $name,
        string $slug,
        SKU $sku,
        Price $price,
        Stock $stock,
        int $categoryId,
        ?string $description = null,
        bool $status = true,
        bool $isFeatured = false
    ) {
        $this->id = null;
        $this->name = $name;
        $this->slug = $slug;
        $this->sku = $sku;
        $this->price = $price;
        $this->salePrice = null;
        $this->stock = $stock;
        $this->categoryId = $categoryId;
        $this->description = $description;
        $this->status = $status;
        $this->isFeatured = $isFeatured;
        $this->isOnSale = false;
        $this->saleStartsAt = null;
        $this->saleEndsAt = null;
        $this->viewCount = 0;
        $this->weight = null;
        $this->dimensions = null;
        $this->seoData = null;
        $this->createdAt = Carbon::now();
        $this->updatedAt = Carbon::now();

        $this->recordEvent(new ProductCreated($this));
    }

    public static function create(
        string $name,
        string $slug,
        SKU $sku,
        Price $price,
        Stock $stock,
        int $categoryId,
        ?string $description = null,
        bool $status = true,
        bool $isFeatured = false
    ): self {
        return new self(
            $name,
            $slug,
            $sku,
            $price,
            $stock,
            $categoryId,
            $description,
            $status,
            $isFeatured
        );
    }

    public function updateBasicInfo(
        string $name,
        string $slug,
        ?string $description = null
    ): void {
        $oldName = $this->name;

        $this->name = $name;
        $this->slug = $slug;
        $this->description = $description;
        $this->updatedAt = Carbon::now();

        $this->recordEvent(new ProductUpdated($this, [
            'old_name' => $oldName,
            'new_name' => $name
        ]));
    }

    public function updatePrice(Price $newPrice): void
    {
        $oldPrice = $this->price;
        $this->price = $newPrice;
        $this->updatedAt = Carbon::now();

        $this->recordEvent(new PriceChanged($this, $oldPrice, $newPrice));
    }

    public function updateStock(Stock $newStock): void
    {
        $oldStock = $this->stock;
        $this->stock = $newStock;
        $this->updatedAt = Carbon::now();

        $this->recordEvent(new StockUpdated($this, $oldStock, $newStock));
    }

    public function setSalePrice(Price $salePrice, ?Carbon $startsAt = null, ?Carbon $endsAt = null): void
    {
        $this->salePrice = $salePrice;
        $this->isOnSale = true;
        $this->saleStartsAt = $startsAt;
        $this->saleEndsAt = $endsAt;
        $this->updatedAt = Carbon::now();
    }

    public function removeSalePrice(): void
    {
        $this->salePrice = null;
        $this->isOnSale = false;
        $this->saleStartsAt = null;
        $this->saleEndsAt = null;
        $this->updatedAt = Carbon::now();
    }

    public function setFeatured(bool $featured): void
    {
        $this->isFeatured = $featured;
        $this->updatedAt = Carbon::now();
    }

    public function activate(): void
    {
        $this->status = true;
        $this->updatedAt = Carbon::now();
    }

    public function deactivate(): void
    {
        $this->status = false;
        $this->updatedAt = Carbon::now();
    }

    public function incrementViewCount(): void
    {
        $this->viewCount++;
        $this->updatedAt = Carbon::now();
    }

    public function setWeight(Weight $weight): void
    {
        $this->weight = $weight;
        $this->updatedAt = Carbon::now();
    }

    public function setDimensions(Dimensions $dimensions): void
    {
        $this->dimensions = $dimensions;
        $this->updatedAt = Carbon::now();
    }

    public function setSEOData(SEOData $seoData): void
    {
        $this->seoData = $seoData;
        $this->updatedAt = Carbon::now();
    }

    public function addVariant(ProductVariant $variant): void
    {
        $this->variants[] = $variant;
        $this->updatedAt = Carbon::now();
    }

    public function removeVariant(int $variantId): void
    {
        $this->variants = array_filter(
            $this->variants,
            fn($variant) => $variant->getId() !== $variantId
        );
        $this->updatedAt = Carbon::now();
    }

    public function addAttribute(ProductAttribute $attribute): void
    {
        $this->attributes[] = $attribute;
        $this->updatedAt = Carbon::now();
    }

    public function addImage(string $imagePath, bool $isPrimary = false): void
    {
        $this->images[] = [
            'path' => $imagePath,
            'is_primary' => $isPrimary,
            'created_at' => Carbon::now()
        ];
        $this->updatedAt = Carbon::now();
    }

    public function getCurrentPrice(): Price
    {
        if ($this->isOnSale && $this->salePrice && $this->isSaleActive()) {
            return $this->salePrice;
        }
        return $this->price;
    }

    public function isSaleActive(): bool
    {
        if (!$this->isOnSale || !$this->salePrice) {
            return false;
        }

        $now = Carbon::now();

        if ($this->saleStartsAt && $now->lt($this->saleStartsAt)) {
            return false;
        }

        if ($this->saleEndsAt && $now->gt($this->saleEndsAt)) {
            return false;
        }

        return true;
    }

    public function isInStock(): bool
    {
        return $this->stock->getQuantity() > 0;
    }

    public function isActive(): bool
    {
        return $this->status;
    }

    public function hasVariants(): bool
    {
        return !empty($this->variants);
    }

    public function getDefaultVariant(): ?ProductVariant
    {
        foreach ($this->variants as $variant) {
            if ($variant->isDefault()) {
                return $variant;
            }
        }
        return null;
    }

    public function getPrimaryImage(): ?string
    {
        foreach ($this->images as $image) {
            if ($image['is_primary']) {
                return $image['path'];
            }
        }
        return $this->images[0]['path'] ?? null;
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getName(): string { return $this->name; }
    public function getSlug(): string { return $this->slug; }
    public function getDescription(): ?string { return $this->description; }
    public function getSKU(): SKU { return $this->sku; }
    public function getPrice(): Price { return $this->price; }
    public function getSalePrice(): ?Price { return $this->salePrice; }
    public function getStock(): Stock { return $this->stock; }
    public function getCategoryId(): int { return $this->categoryId; }
    public function getStatus(): bool { return $this->status; }
    public function isFeatured(): bool { return $this->isFeatured; }
    public function isOnSale(): bool { return $this->isOnSale; }
    public function getSaleStartsAt(): ?Carbon { return $this->saleStartsAt; }
    public function getSaleEndsAt(): ?Carbon { return $this->saleEndsAt; }
    public function getViewCount(): int { return $this->viewCount; }
    public function getWeight(): ?Weight { return $this->weight; }
    public function getDimensions(): ?Dimensions { return $this->dimensions; }
    public function getSEOData(): ?SEOData { return $this->seoData; }
    public function getVariants(): array { return $this->variants; }
    public function getAttributes(): array { return $this->attributes; }
    public function getImages(): array { return $this->images; }
    public function getCreatedAt(): Carbon { return $this->createdAt; }
    public function getUpdatedAt(): Carbon { return $this->updatedAt; }

    // EntityInterface implementation
    public function equals(EntityInterface $other): bool
    {
        if (!$other instanceof static) {
            return false;
        }

        if ($this->getId() === null || $other->getId() === null) {
            return false;
        }

        return $this->getId() === $other->getId();
    }

    public function toString(): string
    {
        return static::class . ':' . ($this->getId() ?? 'new');
    }

    public function toArray(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'slug' => $this->getSlug(),
            'description' => $this->getDescription(),
            'sku' => $this->getSKU()->getValue(),
            'price' => $this->getPrice()->toArray(),
            'sale_price' => $this->getSalePrice()?->toArray(),
            'stock' => $this->getStock()->toArray(),
            'category_id' => $this->getCategoryId(),
            'status' => $this->getStatus(),
            'is_featured' => $this->isFeatured(),
            'is_on_sale' => $this->isOnSale(),
            'view_count' => $this->getViewCount(),
            'created_at' => $this->getCreatedAt()->toISOString(),
            'updated_at' => $this->getUpdatedAt()->toISOString(),
        ];
    }

    /**
     * Ürün dijital mi kontrol et
     */
    public function isDigital(): bool
    {
        // Dijital ürün kategorileri (örnek kategori ID'leri)
        $digitalCategories = [10, 11, 12]; // Software, E-books, Digital Services
        return in_array($this->categoryId, $digitalCategories);
    }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
    public function setCategoryId(int $categoryId): void
    {
        $this->categoryId = $categoryId;
        $this->updatedAt = Carbon::now();
    }
}
