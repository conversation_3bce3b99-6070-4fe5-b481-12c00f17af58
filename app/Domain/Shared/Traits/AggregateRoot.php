<?php

namespace App\Domain\Shared\Traits;

use App\Domain\Shared\Events\DomainEvent;

trait AggregateRoot
{
    private array $domainEvents = [];

    /**
     * Domain event'i kaydet
     */
    protected function recordEvent(DomainEvent $event): void
    {
        $this->domainEvents[] = $event;
    }

    /**
     * Kaydedilmiş domain event'leri al
     */
    public function getRecordedEvents(): array
    {
        return $this->domainEvents;
    }

    /**
     * Domain event'leri temizle
     */
    public function clearRecordedEvents(): void
    {
        $this->domainEvents = [];
    }

    /**
     * Domain event'lerin olup olmadığını kontrol et
     */
    public function hasRecordedEvents(): bool
    {
        return !empty($this->domainEvents);
    }

    /**
     * Belirli bir event tipinin olup olmadığını kontrol et
     */
    public function hasRecordedEvent(string $eventClass): bool
    {
        foreach ($this->domainEvents as $event) {
            if ($event instanceof $eventClass) {
                return true;
            }
        }
        return false;
    }

    /**
     * Belirli bir event tipindeki event'leri al
     */
    public function getRecordedEventsOfType(string $eventClass): array
    {
        return array_filter($this->domainEvents, fn($event) => $event instanceof $eventClass);
    }
}
