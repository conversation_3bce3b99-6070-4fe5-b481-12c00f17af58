<?php

namespace App\Domain\Shared\Contracts;

/**
 * ValueObjectInterface
 * Tüm value object'ler için temel interface
 */
interface ValueObjectInterface
{
    /**
     * Value object'in değerini getir
     */
    public function getValue();

    /**
     * Value object'in string temsilini getir
     */
    public function toString(): string;

    /**
     * Value object'in array temsilini getir
     */
    public function toArray(): array;

    /**
     * Value object'in JSON temsilini getir
     */
    public function toJson(): string;

    /**
     * Eşitlik kontrolü
     */
    public function equals(ValueObjectInterface $other): bool;

    /**
     * Value object'in hash'ini getir
     */
    public function getHash(): string;

    /**
     * Value object'in geçerli olup olmadığını kontrol et
     */
    public function isValid(): bool;

    /**
     * Value object'in boş olup olmadığını kontrol et
     */
    public function isEmpty(): bool;
}
