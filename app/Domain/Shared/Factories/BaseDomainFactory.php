<?php

namespace App\Domain\Shared\Factories;

use App\Domain\Shared\Contracts\EntityInterface;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * BaseDomainFactory
 * Domain factory'leri için temel sınıf
 */
abstract class BaseDomainFactory implements DomainFactoryInterface
{
    protected string $name;
    protected string $version = '1.0.0';
    protected array $requiredFields = [];
    protected array $optionalFields = [];
    protected array $defaultValues = [];
    protected array $validationRules = [];

    public function __construct()
    {
        $this->initialize();
    }

    /**
     * Entity oluştur
     */
    public function create(array $data): EntityInterface
    {
        try {
            // Veriyi validate et
            $validatedData = $this->validateData($data);
            
            // Varsayılan değerleri ekle
            $completeData = $this->mergeDefaultValues($validatedData);
            
            // Entity'yi oluştur
            $entity = $this->createEntity($completeData);
            
            // Post-creation işlemleri
            $this->afterCreate($entity, $completeData);
            
            $this->logOperation('create', [
                'entity_type' => $this->getEntityType(),
                'data_keys' => array_keys($data),
            ]);

            return $entity;

        } catch (\Exception $e) {
            $this->logError('create', $e, [
                'entity_type' => $this->getEntityType(),
                'data' => $data,
            ]);

            throw new \DomainException(
                "Failed to create {$this->getEntityType()}: " . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * Birden fazla entity oluştur
     */
    public function createBatch(array $dataArray): array
    {
        $entities = [];
        $errors = [];

        foreach ($dataArray as $index => $data) {
            try {
                $entities[] = $this->create($data);
            } catch (\Exception $e) {
                $errors[$index] = $e->getMessage();
            }
        }

        if (!empty($errors)) {
            $this->logError('create_batch', new \Exception('Batch creation had errors'), [
                'entity_type' => $this->getEntityType(),
                'total_items' => count($dataArray),
                'successful' => count($entities),
                'errors' => $errors,
            ]);
        }

        $this->logOperation('create_batch', [
            'entity_type' => $this->getEntityType(),
            'total_items' => count($dataArray),
            'successful' => count($entities),
            'failed' => count($errors),
        ]);

        return $entities;
    }

    /**
     * Entity'yi yeniden oluştur (reconstruction)
     */
    public function reconstruct(array $data): EntityInterface
    {
        try {
            // Reconstruction için özel validation
            $validatedData = $this->validateReconstructionData($data);
            
            // Entity'yi yeniden oluştur
            $entity = $this->reconstructEntity($validatedData);
            
            // Post-reconstruction işlemleri
            $this->afterReconstruct($entity, $validatedData);
            
            $this->logOperation('reconstruct', [
                'entity_type' => $this->getEntityType(),
                'entity_id' => $data['id'] ?? 'unknown',
            ]);

            return $entity;

        } catch (\Exception $e) {
            $this->logError('reconstruct', $e, [
                'entity_type' => $this->getEntityType(),
                'data' => $data,
            ]);

            throw new \DomainException(
                "Failed to reconstruct {$this->getEntityType()}: " . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * Factory'nin adını getir
     */
    public function getName(): string
    {
        return $this->name ?? $this->getDefaultName();
    }

    /**
     * Factory'nin versiyonunu getir
     */
    public function getVersion(): string
    {
        return $this->version;
    }

    /**
     * Gerekli alanları getir
     */
    public function getRequiredFields(): array
    {
        return $this->requiredFields;
    }

    /**
     * Opsiyonel alanları getir
     */
    public function getOptionalFields(): array
    {
        return $this->optionalFields;
    }

    /**
     * Varsayılan değerleri getir
     */
    public function getDefaultValues(): array
    {
        return $this->defaultValues;
    }

    /**
     * Veriyi validate et
     */
    public function validateData(array $data): array
    {
        // Gerekli alanları kontrol et
        $this->validateRequiredFields($data);
        
        // Laravel validator kullan
        if (!empty($this->validationRules)) {
            $validator = Validator::make($data, $this->validationRules);
            
            if ($validator->fails()) {
                throw new \InvalidArgumentException(
                    'Validation failed: ' . implode(', ', $validator->errors()->all())
                );
            }
            
            $data = $validator->validated();
        }
        
        // Özel validation
        $this->performCustomValidation($data);
        
        return $data;
    }

    /**
     * Factory'nin sağlık durumunu kontrol et
     */
    public function healthCheck(): bool
    {
        try {
            $this->performHealthCheck();
            return true;
        } catch (\Exception $e) {
            Log::error('Domain factory health check failed', [
                'factory' => $this->getName(),
                'entity_type' => $this->getEntityType(),
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Factory başlatma işlemleri
     */
    protected function initialize(): void
    {
        // Alt sınıflar tarafından override edilebilir
    }

    /**
     * Entity oluşturma - alt sınıflar tarafından implement edilmeli
     */
    abstract protected function createEntity(array $data): EntityInterface;

    /**
     * Entity reconstruction - alt sınıflar tarafından implement edilmeli
     */
    abstract protected function reconstructEntity(array $data): EntityInterface;

    /**
     * Entity oluşturulduktan sonra çağrılır
     */
    protected function afterCreate(EntityInterface $entity, array $data): void
    {
        // Alt sınıflar tarafından override edilebilir
    }

    /**
     * Entity reconstruct edildikten sonra çağrılır
     */
    protected function afterReconstruct(EntityInterface $entity, array $data): void
    {
        // Alt sınıflar tarafından override edilebilir
    }

    /**
     * Varsayılan değerleri birleştir
     */
    protected function mergeDefaultValues(array $data): array
    {
        return array_merge($this->getDefaultValues(), $data);
    }

    /**
     * Gerekli alanları validate et
     */
    protected function validateRequiredFields(array $data): void
    {
        $missingFields = [];
        
        foreach ($this->getRequiredFields() as $field) {
            if (!array_key_exists($field, $data) || $data[$field] === null) {
                $missingFields[] = $field;
            }
        }
        
        if (!empty($missingFields)) {
            throw new \InvalidArgumentException(
                'Missing required fields: ' . implode(', ', $missingFields)
            );
        }
    }

    /**
     * Reconstruction data validate et
     */
    protected function validateReconstructionData(array $data): array
    {
        // ID alanı reconstruction için gerekli
        if (!isset($data['id'])) {
            throw new \InvalidArgumentException('ID field is required for reconstruction');
        }
        
        return $this->validateData($data);
    }

    /**
     * Özel validation - alt sınıflar tarafından override edilebilir
     */
    protected function performCustomValidation(array $data): void
    {
        // Alt sınıflar tarafından implement edilebilir
    }

    /**
     * Sağlık kontrolü - alt sınıflar tarafından override edilebilir
     */
    protected function performHealthCheck(): void
    {
        // Temel kontroller
        if (empty($this->getEntityType())) {
            throw new \RuntimeException('Entity type not defined');
        }
        
        if (empty($this->getRequiredFields()) && empty($this->getOptionalFields())) {
            throw new \RuntimeException('No fields defined');
        }
    }

    /**
     * Varsayılan factory adını getir
     */
    protected function getDefaultName(): string
    {
        $className = static::class;
        $parts = explode('\\', $className);
        $shortName = end($parts);
        
        // Remove "Factory" suffix if exists
        if (str_ends_with($shortName, 'Factory')) {
            $shortName = substr($shortName, 0, -7);
        }
        
        return strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $shortName));
    }

    /**
     * Operasyon logla
     */
    protected function logOperation(string $operation, array $context = []): void
    {
        Log::info('Domain factory operation executed', [
            'factory' => $this->getName(),
            'operation' => $operation,
            'context' => $context,
        ]);
    }

    /**
     * Hata logla
     */
    protected function logError(string $operation, \Exception $exception, array $context = []): void
    {
        Log::error('Domain factory operation failed', [
            'factory' => $this->getName(),
            'operation' => $operation,
            'error' => $exception->getMessage(),
            'context' => $context,
        ]);
    }

    /**
     * Factory'nin debug bilgilerini getir
     */
    public function getDebugInfo(): array
    {
        return [
            'name' => $this->getName(),
            'version' => $this->getVersion(),
            'entity_type' => $this->getEntityType(),
            'required_fields' => $this->getRequiredFields(),
            'optional_fields' => $this->getOptionalFields(),
            'default_values' => $this->getDefaultValues(),
            'validation_rules' => $this->validationRules,
            'is_healthy' => $this->healthCheck(),
            'class' => static::class,
        ];
    }

    /**
     * Magic method for string conversion
     */
    public function __toString(): string
    {
        return $this->getName() . ' v' . $this->getVersion();
    }
}
