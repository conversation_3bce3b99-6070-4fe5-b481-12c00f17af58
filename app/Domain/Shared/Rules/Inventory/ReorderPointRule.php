<?php

namespace App\Domain\Shared\Rules\Inventory;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Products\Entities\Product;

/**
 * ReorderPointRule
 * Yeniden sipariş noktası kuralı - stok seviyesi belirli bir noktaya düştüğünde uyarı verir
 */
class ReorderPointRule implements InventoryRuleInterface
{
    private int $priority;
    private int $defaultReorderPoint;
    private int $defaultReorderQuantity;
    private bool $autoReorderEnabled;

    public function __construct(
        int $priority = 200,
        int $defaultReorderPoint = 10,
        int $defaultReorderQuantity = 50,
        bool $autoReorderEnabled = false
    ) {
        $this->priority = $priority;
        $this->defaultReorderPoint = $defaultReorderPoint;
        $this->defaultReorderQuantity = $defaultReorderQuantity;
        $this->autoReorderEnabled = $autoReorderEnabled;
    }

    public function applyRule(EntityInterface $entity, array $context = []): InventoryRuleResult
    {
        if (!$entity instanceof Product) {
            return InventoryRuleResult::available(
                $this->getName(),
                0,
                0,
                $context['quantity'] ?? 1,
                'Entity is not a product - reorder rule not applicable'
            );
        }

        $requestedQuantity = $context['quantity'] ?? 1;
        $currentStock = $entity->getStock()->getQuantity();
        $reservedQuantity = $entity->getStock()->getReservedQuantity();
        $availableStock = $currentStock - $reservedQuantity;
        
        // Ürün için özel reorder point varsa onu kullan, yoksa default'u kullan
        $reorderPoint = $entity->getStock()->getReorderPoint() ?? $this->defaultReorderPoint;
        $reorderQuantity = $entity->getStock()->getReorderQuantity() ?? $this->defaultReorderQuantity;

        $stockAfterRequest = $availableStock - $requestedQuantity;
        $warnings = [];
        $metadata = [
            'current_stock' => $currentStock,
            'reorder_point' => $reorderPoint,
            'reorder_quantity' => $reorderQuantity,
            'stock_after_request' => $stockAfterRequest
        ];

        // Mevcut stok zaten reorder point'in altındaysa
        if ($availableStock <= $reorderPoint) {
            $warnings[] = "Stock is at or below reorder point ({$reorderPoint}). Current stock: {$availableStock}";
            
            if ($this->autoReorderEnabled) {
                $warnings[] = "Auto-reorder triggered for {$reorderQuantity} units";
                $metadata['auto_reorder_triggered'] = true;
                $metadata['auto_reorder_quantity'] = $reorderQuantity;
            }
        }

        // İstek sonrası stok reorder point'in altına düşecekse
        if ($stockAfterRequest <= $reorderPoint && $availableStock > $reorderPoint) {
            $warnings[] = "Stock will fall to reorder point after this request. Stock after request: {$stockAfterRequest}";
            
            if ($this->autoReorderEnabled) {
                $warnings[] = "Auto-reorder will be triggered for {$reorderQuantity} units";
                $metadata['auto_reorder_will_trigger'] = true;
                $metadata['auto_reorder_quantity'] = $reorderQuantity;
            }
        }

        // Stok kritik seviyede mi kontrol et (reorder point'in %50'si)
        $criticalLevel = (int) ($reorderPoint * 0.5);
        if ($stockAfterRequest <= $criticalLevel) {
            $warnings[] = "Critical stock level reached. Immediate attention required.";
            $metadata['critical_level_reached'] = true;
            $metadata['critical_level'] = $criticalLevel;
        }

        $result = InventoryRuleResult::available(
            $this->getName(),
            $availableStock,
            $reservedQuantity,
            $requestedQuantity,
            'Reorder point analysis completed',
            $metadata
        );

        // Warnings ekle
        foreach ($warnings as $warning) {
            $result = $result->withWarning($warning);
        }

        return $result;
    }

    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        if (!$entity instanceof Product) {
            return false;
        }

        // Dijital ürünler için reorder gerekli değil
        if ($entity->isDigital()) {
            return false;
        }

        // Stok takibi yapılmayan ürünler için geçerli değil
        if (!$entity->getStock()->isTrackingEnabled()) {
            return false;
        }

        return true;
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getName(): string
    {
        return 'reorder_point';
    }

    public function getDescription(): string
    {
        return 'Monitors stock levels and triggers reorder warnings when stock falls below reorder point';
    }

    /**
     * Default reorder point'i ayarla
     */
    public function setDefaultReorderPoint(int $reorderPoint): self
    {
        $this->defaultReorderPoint = $reorderPoint;
        return $this;
    }

    /**
     * Default reorder quantity'yi ayarla
     */
    public function setDefaultReorderQuantity(int $reorderQuantity): self
    {
        $this->defaultReorderQuantity = $reorderQuantity;
        return $this;
    }

    /**
     * Auto reorder'ı etkinleştir/devre dışı bırak
     */
    public function setAutoReorderEnabled(bool $enabled): self
    {
        $this->autoReorderEnabled = $enabled;
        return $this;
    }

    /**
     * Ürün için reorder gerekli mi kontrol et
     */
    public function isReorderRequired(Product $product): bool
    {
        $availableStock = $product->getStock()->getQuantity() - $product->getStock()->getReservedQuantity();
        $reorderPoint = $product->getStock()->getReorderPoint() ?? $this->defaultReorderPoint;
        
        return $availableStock <= $reorderPoint;
    }

    /**
     * Önerilen reorder quantity'yi hesapla
     */
    public function calculateReorderQuantity(Product $product): int
    {
        $reorderQuantity = $product->getStock()->getReorderQuantity() ?? $this->defaultReorderQuantity;
        
        // Eğer stok çok düşükse, normal reorder quantity'nin 1.5 katını öner
        $availableStock = $product->getStock()->getQuantity() - $product->getStock()->getReservedQuantity();
        $reorderPoint = $product->getStock()->getReorderPoint() ?? $this->defaultReorderPoint;
        
        if ($availableStock <= ($reorderPoint * 0.5)) {
            return (int) ($reorderQuantity * 1.5);
        }
        
        return $reorderQuantity;
    }

    /**
     * Reorder bilgilerini getir
     */
    public function getReorderInfo(Product $product): array
    {
        $availableStock = $product->getStock()->getQuantity() - $product->getStock()->getReservedQuantity();
        $reorderPoint = $product->getStock()->getReorderPoint() ?? $this->defaultReorderPoint;
        $reorderQuantity = $this->calculateReorderQuantity($product);
        
        return [
            'current_stock' => $availableStock,
            'reorder_point' => $reorderPoint,
            'reorder_required' => $this->isReorderRequired($product),
            'recommended_quantity' => $reorderQuantity,
            'auto_reorder_enabled' => $this->autoReorderEnabled,
            'days_until_stockout' => $this->estimateDaysUntilStockout($product),
        ];
    }

    /**
     * Stok tükenmesine kadar kalan gün sayısını tahmin et
     */
    private function estimateDaysUntilStockout(Product $product): ?int
    {
        $availableStock = $product->getStock()->getQuantity() - $product->getStock()->getReservedQuantity();
        
        if ($availableStock <= 0) {
            return 0;
        }

        // Günlük ortalama satış (örnek: son 30 günün ortalaması)
        // Bu değer gerçek implementasyonda veritabanından alınmalı
        $averageDailySales = 2; // Örnek değer
        
        if ($averageDailySales <= 0) {
            return null; // Satış verisi yok
        }
        
        return (int) ceil($availableStock / $averageDailySales);
    }
}
