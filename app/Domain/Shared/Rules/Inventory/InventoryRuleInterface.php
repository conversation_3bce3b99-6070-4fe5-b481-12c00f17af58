<?php

namespace App\Domain\Shared\Rules\Inventory;

use App\Domain\Shared\Contracts\EntityInterface;

// BusinessRuleResult sınıfı BusinessRule.php dosyasında tanımlı
require_once __DIR__ . '/../BusinessRule.php';

/**
 * InventoryRuleInterface
 * Stok kuralları için interface
 */
interface InventoryRuleInterface
{
    /**
     * Stok kuralını uygula
     */
    public function applyRule(EntityInterface $entity, array $context = []): InventoryRuleResult;

    /**
     * Kural bu entity için geçerli mi
     */
    public function isApplicable(EntityInterface $entity, array $context = []): bool;

    /**
     * Kuralın önceliğini getir
     */
    public function getPriority(): int;

    /**
     * Kuralın adını getir
     */
    public function getName(): string;

    /**
     * Kuralın açıklamasını getir
     */
    public function getDescription(): string;
}

/**
 * InventoryRuleResult
 * Stok kuralı sonucu
 */
class InventoryRuleResult extends \App\Domain\Shared\Rules\BusinessRuleResult
{
    private int $availableQuantity;
    private int $reservedQuantity;
    private int $requestedQuantity;
    private bool $stockAvailable;
    private bool $backorderAllowed;
    private ?string $reason = null;

    public function __construct(
        bool $valid,
        string $ruleName,
        int $availableQuantity,
        int $reservedQuantity,
        int $requestedQuantity,
        bool $stockAvailable,
        bool $backorderAllowed = false,
        ?string $reason = null,
        array $errors = [],
        array $warnings = [],
        array $metadata = []
    ) {
        parent::__construct($valid, $ruleName, $errors, $warnings, $metadata);

        $this->availableQuantity = $availableQuantity;
        $this->reservedQuantity = $reservedQuantity;
        $this->requestedQuantity = $requestedQuantity;
        $this->stockAvailable = $stockAvailable;
        $this->backorderAllowed = $backorderAllowed;
        $this->reason = $reason;
    }

    public static function available(
        string $ruleName,
        int $availableQuantity,
        int $reservedQuantity,
        int $requestedQuantity,
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            $availableQuantity,
            $reservedQuantity,
            $requestedQuantity,
            true,
            false,
            $reason,
            [],
            [],
            $metadata
        );
    }

    public static function unavailable(
        string $ruleName,
        int $availableQuantity,
        int $reservedQuantity,
        int $requestedQuantity,
        bool $backorderAllowed = false,
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            false,
            $ruleName,
            $availableQuantity,
            $reservedQuantity,
            $requestedQuantity,
            false,
            $backorderAllowed,
            $reason,
            ['Insufficient stock available'],
            [],
            $metadata
        );
    }

    public static function invalidEntity(
        string $ruleName,
        string $errorMessage,
        int $availableQuantity = 0,
        int $reservedQuantity = 0,
        int $requestedQuantity = 1,
        array $metadata = []
    ): self {
        return new self(
            false,
            $ruleName,
            $availableQuantity,
            $reservedQuantity,
            $requestedQuantity,
            false,
            false,
            $errorMessage,
            [$errorMessage],
            [],
            $metadata
        );
    }

    public static function backorder(
        string $ruleName,
        int $availableQuantity,
        int $reservedQuantity,
        int $requestedQuantity,
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            $availableQuantity,
            $reservedQuantity,
            $requestedQuantity,
            false,
            true,
            $reason,
            [],
            ['Item will be backordered'],
            $metadata
        );
    }

    public function getAvailableQuantity(): int
    {
        return $this->availableQuantity;
    }

    public function getReservedQuantity(): int
    {
        return $this->reservedQuantity;
    }

    public function getRequestedQuantity(): int
    {
        return $this->requestedQuantity;
    }

    public function isStockAvailable(): bool
    {
        return $this->stockAvailable;
    }

    public function isBackorderAllowed(): bool
    {
        return $this->backorderAllowed;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }

    public function getShortfall(): int
    {
        return max(0, $this->requestedQuantity - $this->availableQuantity);
    }

    public function canFulfill(): bool
    {
        return $this->stockAvailable || $this->backorderAllowed;
    }

    public function withWarnings(array $warnings): self
    {
        $result = clone $this;
        $result->warnings = array_merge($result->warnings, $warnings);
        return $result;
    }

    public function withWarning(string $warning): self
    {
        $result = clone $this;
        $result->warnings[] = $warning;
        return $result;
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'available_quantity' => $this->availableQuantity,
            'reserved_quantity' => $this->reservedQuantity,
            'requested_quantity' => $this->requestedQuantity,
            'stock_available' => $this->stockAvailable,
            'backorder_allowed' => $this->backorderAllowed,
            'reason' => $this->reason,
            'shortfall' => $this->getShortfall(),
            'can_fulfill' => $this->canFulfill(),
        ]);
    }
}
