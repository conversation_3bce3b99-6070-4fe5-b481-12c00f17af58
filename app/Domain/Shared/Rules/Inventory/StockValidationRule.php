<?php

namespace App\Domain\Shared\Rules\Inventory;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Products\Entities\Product;

/**
 * StockValidationRule
 * Stok doğrulama kuralı - ürünün stok durumunu kontrol eder
 */
class StockValidationRule implements InventoryRuleInterface
{
    private int $priority;
    private bool $allowBackorder;
    private int $minimumStockLevel;

    public function __construct(
        int $priority = 100,
        bool $allowBackorder = false,
        int $minimumStockLevel = 0
    ) {
        $this->priority = $priority;
        $this->allowBackorder = $allowBackorder;
        $this->minimumStockLevel = $minimumStockLevel;
    }

    public function applyRule(EntityInterface $entity, array $context = []): InventoryRuleResult
    {
        if (!$entity instanceof Product) {
            return InventoryRuleResult::unavailable(
                $this->getName(),
                0,
                0,
                $context['quantity'] ?? 1,
                false,
                'Entity is not a product'
            );
        }

        $requestedQuantity = $context['quantity'] ?? 1;
        $availableQuantity = $entity->getStock()->getQuantity();
        $reservedQuantity = $entity->getStock()->getReservedQuantity();
        $actualAvailable = $availableQuantity - $reservedQuantity;

        // Stok durumunu kontrol et
        if ($actualAvailable >= $requestedQuantity) {
            return InventoryRuleResult::available(
                $this->getName(),
                $actualAvailable,
                $reservedQuantity,
                $requestedQuantity,
                'Stock available',
                [
                    'stock_level' => $availableQuantity,
                    'after_reservation' => $actualAvailable - $requestedQuantity
                ]
            );
        }

        // Stok yetersiz - backorder kontrolü
        if ($this->allowBackorder && $entity->getStock()->isBackorderAllowed()) {
            return InventoryRuleResult::backorder(
                $this->getName(),
                $actualAvailable,
                $reservedQuantity,
                $requestedQuantity,
                'Insufficient stock, backorder allowed',
                [
                    'stock_level' => $availableQuantity,
                    'shortfall' => $requestedQuantity - $actualAvailable,
                    'backorder_quantity' => $requestedQuantity - $actualAvailable
                ]
            );
        }

        // Stok yetersiz ve backorder izin verilmiyor
        return InventoryRuleResult::unavailable(
            $this->getName(),
            $actualAvailable,
            $reservedQuantity,
            $requestedQuantity,
            false,
            'Insufficient stock and backorder not allowed',
            [
                'stock_level' => $availableQuantity,
                'shortfall' => $requestedQuantity - $actualAvailable
            ]
        );
    }

    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        if (!$entity instanceof Product) {
            return false;
        }

        // Dijital ürünler için stok kontrolü gerekli değil
        if ($entity->isDigital()) {
            return false;
        }

        // Stok takibi yapılmayan ürünler için geçerli değil
        if (!$entity->getStock()->isTrackingEnabled()) {
            return false;
        }

        return true;
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getName(): string
    {
        return 'stock_validation';
    }

    public function getDescription(): string
    {
        return 'Validates product stock availability and handles backorder scenarios';
    }

    /**
     * Backorder izin verme durumunu ayarla
     */
    public function setAllowBackorder(bool $allowBackorder): self
    {
        $this->allowBackorder = $allowBackorder;
        return $this;
    }

    /**
     * Minimum stok seviyesini ayarla
     */
    public function setMinimumStockLevel(int $minimumStockLevel): self
    {
        $this->minimumStockLevel = $minimumStockLevel;
        return $this;
    }

    /**
     * Backorder izin verilip verilmediğini kontrol et
     */
    public function isBackorderAllowed(): bool
    {
        return $this->allowBackorder;
    }

    /**
     * Minimum stok seviyesini getir
     */
    public function getMinimumStockLevel(): int
    {
        return $this->minimumStockLevel;
    }

    /**
     * Stok seviyesi kritik mi kontrol et
     */
    public function isStockCritical(Product $product): bool
    {
        $availableStock = $product->getStock()->getQuantity() - $product->getStock()->getReservedQuantity();
        return $availableStock <= $this->minimumStockLevel;
    }

    /**
     * Stok uyarı mesajı oluştur
     */
    public function getStockWarning(Product $product): ?string
    {
        if ($this->isStockCritical($product)) {
            $availableStock = $product->getStock()->getQuantity() - $product->getStock()->getReservedQuantity();
            return "Low stock warning: Only {$availableStock} items remaining";
        }

        return null;
    }
}
