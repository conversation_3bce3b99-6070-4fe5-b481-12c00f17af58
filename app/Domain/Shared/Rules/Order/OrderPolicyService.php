<?php

namespace App\Domain\Shared\Rules\Order;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Orders\Entities\Order;
use App\Domain\Shared\Rules\BusinessRuleEngine;

/**
 * OrderPolicyService
 * Sipariş politikalarını yöneten servis
 */
class OrderPolicyService
{
    private BusinessRuleEngine $ruleEngine;
    private array $registeredRules = [];

    public function __construct()
    {
        $this->ruleEngine = new BusinessRuleEngine();
        $this->loadStandardRules();
    }

    /**
     * Sipariş doğrulaması yap
     */
    public function validateOrder(Order $order, array $context = []): OrderPolicyResult
    {
        $context = array_merge($context, [
            'action' => 'validate'
        ]);
        
        $results = $this->ruleEngine->executeRules($order, $context);
        
        return new OrderPolicyResult(
            $order,
            'validate',
            $results
        );
    }

    /**
     * Sipariş durum geçişini doğrula
     */
    public function validateStatusTransition(Order $order, string $targetStatus, array $context = []): OrderPolicyResult
    {
        $context = array_merge($context, [
            'action' => 'status_transition',
            'target_status' => $targetStatus
        ]);
        
        $results = $this->ruleEngine->executeRules($order, $context);
        
        return new OrderPolicyResult(
            $order,
            'status_transition',
            $results,
            $targetStatus
        );
    }

    /**
     * Sipariş iptalini doğrula
     */
    public function validateCancellation(Order $order, string $reason = null, array $context = []): OrderPolicyResult
    {
        $context = array_merge($context, [
            'action' => 'cancel',
            'cancellation_reason' => $reason
        ]);

        $results = $this->ruleEngine->executeRules($order, $context);

        return new OrderPolicyResult(
            $order,
            'cancel',
            $results
        );
    }

    /**
     * Sipariş iadesini doğrula
     */
    public function validateRefund(Order $order, $refundAmount = null, string $reason = null, array $context = []): OrderPolicyResult
    {
        $context = array_merge($context, [
            'action' => 'refund',
            'refund_amount' => $refundAmount,
            'refund_reason' => $reason
        ]);

        $results = $this->ruleEngine->executeRules($order, $context);

        return new OrderPolicyResult(
            $order,
            'refund',
            $results
        );
    }

    /**
     * Sipariş değişikliğini doğrula
     */
    public function validateModification(Order $order, string $modificationType, array $modificationData = [], array $context = []): OrderPolicyResult
    {
        $context = array_merge($context, [
            'action' => 'modify',
            'modification_type' => $modificationType,
            'modification_data' => $modificationData
        ]);

        $results = $this->ruleEngine->executeRules($order, $context);

        return new OrderPolicyResult(
            $order,
            'modify',
            $results
        );
    }

    /**
     * Kural kaydet
     */
    public function registerRule(OrderRuleInterface $rule): void
    {
        $this->ruleEngine->registerRule($rule);
        $this->registeredRules[$rule->getName()] = $rule;
    }

    /**
     * Kural kaldır
     */
    public function unregisterRule(string $ruleName): void
    {
        $this->ruleEngine->unregisterRule($ruleName);
        unset($this->registeredRules[$ruleName]);
    }

    /**
     * Tüm kuralları getir
     */
    public function getRules(): array
    {
        return $this->registeredRules;
    }

    /**
     * Önceliğe göre sıralı kuralları getir
     */
    public function getRulesByPriority(): array
    {
        $rules = $this->registeredRules;
        
        uasort($rules, function ($a, $b) {
            return $b->getPriority() <=> $a->getPriority();
        });
        
        return $rules;
    }

    /**
     * Belirli bir kuralı getir
     */
    public function getRule(string $ruleName): ?OrderRuleInterface
    {
        return $this->registeredRules[$ruleName] ?? null;
    }

    /**
     * Kural var mı kontrol et
     */
    public function hasRule(string $ruleName): bool
    {
        return isset($this->registeredRules[$ruleName]);
    }

    /**
     * Standart sipariş kurallarını yükle
     */
    private function loadStandardRules(): void
    {
        // Sipariş doğrulama kuralı
        $this->registerRule(OrderValidationRule::standard());

        // Durum geçiş kuralı
        $this->registerRule(OrderStatusTransitionRule::standard());

        // İptal kuralı
        $this->registerRule(OrderCancellationRule::standard());

        // İade kuralı
        $this->registerRule(OrderRefundRule::standard());

        // Değişiklik kuralı
        $this->registerRule(OrderModificationRule::standard());
    }

    /**
     * Tüm kuralları temizle
     */
    public function clearRules(): void
    {
        $this->registeredRules = [];
        $this->ruleEngine = new BusinessRuleEngine();
    }

    /**
     * Standart kuralları yeniden yükle
     */
    public function reloadStandardRules(): void
    {
        $this->clearRules();
        $this->loadStandardRules();
    }
}

/**
 * OrderPolicyResult
 * Sipariş politikası sonucu
 */
class OrderPolicyResult
{
    private Order $order;
    private string $action;
    private array $ruleResults;
    private ?string $targetStatus;

    public function __construct(
        Order $order,
        string $action,
        array $ruleResults,
        ?string $targetStatus = null
    ) {
        $this->order = $order;
        $this->action = $action;
        $this->ruleResults = $ruleResults;
        $this->targetStatus = $targetStatus;
    }

    /**
     * Tüm kurallar geçti mi
     */
    public function isValid(): bool
    {
        foreach ($this->ruleResults as $result) {
            if (!$result->isValid()) {
                return false;
            }
        }
        return true;
    }

    /**
     * İşlem izin verildi mi
     */
    public function isAllowed(): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        foreach ($this->ruleResults as $result) {
            if ($result instanceof OrderRuleResult && !$result->isOrderAllowed()) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Ek eylem gerekiyor mu
     */
    public function requiresActions(): bool
    {
        foreach ($this->ruleResults as $result) {
            if ($result instanceof OrderRuleResult && $result->hasRequiredActions()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Gerekli eylemleri getir
     */
    public function getRequiredActions(): array
    {
        $actions = [];
        
        foreach ($this->ruleResults as $result) {
            if ($result instanceof OrderRuleResult) {
                $actions = array_merge($actions, $result->getRequiredActions());
            }
        }
        
        return array_unique($actions);
    }

    /**
     * Tüm hataları getir
     */
    public function getErrors(): array
    {
        $errors = [];
        
        foreach ($this->ruleResults as $result) {
            $errors = array_merge($errors, $result->getErrors());
        }
        
        return $errors;
    }

    /**
     * Tüm uyarıları getir
     */
    public function getWarnings(): array
    {
        $warnings = [];
        
        foreach ($this->ruleResults as $result) {
            $warnings = array_merge($warnings, $result->getWarnings());
        }
        
        return $warnings;
    }

    /**
     * Sipariş nesnesini getir
     */
    public function getOrder(): Order
    {
        return $this->order;
    }

    /**
     * Eylemi getir
     */
    public function getAction(): string
    {
        return $this->action;
    }

    /**
     * Hedef durumu getir
     */
    public function getTargetStatus(): ?string
    {
        return $this->targetStatus;
    }

    /**
     * Kural sonuçlarını getir
     */
    public function getRuleResults(): array
    {
        return $this->ruleResults;
    }

    /**
     * Belirli bir kuralın sonucunu getir
     */
    public function getRuleResult(string $ruleName): ?OrderRuleResult
    {
        foreach ($this->ruleResults as $result) {
            if ($result instanceof OrderRuleResult && $result->getRuleName() === $ruleName) {
                return $result;
            }
        }
        return null;
    }

    /**
     * Sonucu array'e çevir
     */
    public function toArray(): array
    {
        return [
            'order_id' => $this->order->getId(),
            'action' => $this->action,
            'target_status' => $this->targetStatus,
            'is_valid' => $this->isValid(),
            'is_allowed' => $this->isAllowed(),
            'requires_actions' => $this->requiresActions(),
            'required_actions' => $this->getRequiredActions(),
            'errors' => $this->getErrors(),
            'warnings' => $this->getWarnings(),
            'rule_results' => array_map(function ($result) {
                return $result->toArray();
            }, $this->ruleResults)
        ];
    }
}
