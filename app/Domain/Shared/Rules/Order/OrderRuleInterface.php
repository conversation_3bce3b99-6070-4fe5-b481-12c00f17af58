<?php

namespace App\Domain\Shared\Rules\Order;

use App\Domain\Shared\Contracts\EntityInterface;

// BusinessRuleResult sınıfı BusinessRule.php dosyasında tanımlı
require_once __DIR__ . '/../BusinessRule.php';

/**
 * OrderRuleInterface
 * Sipariş kuralları için interface
 */
interface OrderRuleInterface
{
    /**
     * Sipariş kuralını uygula
     */
    public function applyRule(EntityInterface $entity, array $context = []): OrderRuleResult;

    /**
     * Kural bu entity için geçerli mi
     */
    public function isApplicable(EntityInterface $entity, array $context = []): bool;

    /**
     * Kuralın önceliğini getir
     */
    public function getPriority(): int;

    /**
     * Kuralın adını getir
     */
    public function getName(): string;

    /**
     * Kuralın açıklamasını getir
     */
    public function getDescription(): string;
}

/**
 * OrderRuleResult
 * <PERSON>par<PERSON><PERSON> kuralı sonucu
 */
class OrderRuleResult extends \App\Domain\Shared\Rules\BusinessRuleResult
{
    private bool $orderAllowed;
    private ?string $newStatus = null;
    private array $allowedTransitions = [];
    private array $requiredActions = [];
    private ?string $reason = null;

    public function __construct(
        bool $valid,
        string $ruleName,
        bool $orderAllowed,
        ?string $newStatus = null,
        array $allowedTransitions = [],
        array $requiredActions = [],
        ?string $reason = null,
        array $errors = [],
        array $warnings = [],
        array $metadata = []
    ) {
        parent::__construct($valid, $ruleName, $errors, $warnings, $metadata);

        $this->orderAllowed = $orderAllowed;
        $this->newStatus = $newStatus;
        $this->allowedTransitions = $allowedTransitions;
        $this->requiredActions = $requiredActions;
        $this->reason = $reason;
    }

    public static function allowed(
        string $ruleName,
        ?string $newStatus = null,
        array $allowedTransitions = [],
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            true,
            $newStatus,
            $allowedTransitions,
            [],
            $reason,
            [],
            [],
            $metadata
        );
    }

    public static function denied(
        string $ruleName,
        ?string $reason = null,
        array $metadata = []
    ): self {
        // Metadata'dan errors'ı çıkar
        $errors = $metadata['errors'] ?? ['Order action denied'];

        return new self(
            false,
            $ruleName,
            false,
            null,
            [],
            [],
            $reason,
            $errors,
            [],
            $metadata
        );
    }

    public static function requiresAction(
        string $ruleName,
        array $requiredActions,
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            true,
            null,
            [],
            $requiredActions,
            $reason,
            [],
            ['Additional actions required before proceeding'],
            $metadata
        );
    }

    public function isOrderAllowed(): bool
    {
        return $this->orderAllowed;
    }

    public function getNewStatus(): ?string
    {
        return $this->newStatus;
    }

    public function getAllowedTransitions(): array
    {
        return $this->allowedTransitions;
    }

    public function getRequiredActions(): array
    {
        return $this->requiredActions;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }

    public function hasRequiredActions(): bool
    {
        return !empty($this->requiredActions);
    }

    public function isTransitionAllowed(string $fromStatus, string $toStatus): bool
    {
        if (empty($this->allowedTransitions)) {
            return true; // Eğer kısıtlama yoksa tüm geçişlere izin ver
        }

        $transitionKey = "{$fromStatus}->{$toStatus}";
        return in_array($transitionKey, $this->allowedTransitions);
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'order_allowed' => $this->orderAllowed,
            'new_status' => $this->newStatus,
            'allowed_transitions' => $this->allowedTransitions,
            'required_actions' => $this->requiredActions,
            'reason' => $this->reason,
            'has_required_actions' => $this->hasRequiredActions(),
        ]);
    }
}
