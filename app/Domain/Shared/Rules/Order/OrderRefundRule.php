<?php

namespace App\Domain\Shared\Rules\Order;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;
use Carbon\Carbon;

/**
 * OrderRefundRule
 * Sipariş iade kuralı
 */
class OrderRefundRule implements OrderRuleInterface
{
    private array $refundableStatuses;
    private array $nonRefundableStatuses;
    private int $refundPeriodDays;
    private Money $maxRefundAmount;
    private float $refundFeePercentage;
    private int $priority;
    private bool $partialRefundAllowed;
    private bool $requireApproval;
    private array $approvalRequiredConditions;

    public function __construct(
        array $refundableStatuses = ['delivered'],
        array $nonRefundableStatuses = ['cancelled', 'refunded'],
        int $refundPeriodDays = 30,
        Money $maxRefundAmount = null,
        float $refundFeePercentage = 0.0,
        int $priority = 400,
        bool $partialRefundAllowed = true,
        bool $requireApproval = false,
        array $approvalRequiredConditions = []
    ) {
        $this->refundableStatuses = $refundableStatuses;
        $this->nonRefundableStatuses = $nonRefundableStatuses;
        $this->refundPeriodDays = $refundPeriodDays;
        $this->maxRefundAmount = $maxRefundAmount ?? Money::fromAmount(50000, 'TRY');
        $this->refundFeePercentage = $refundFeePercentage;
        $this->priority = $priority;
        $this->partialRefundAllowed = $partialRefundAllowed;
        $this->requireApproval = $requireApproval;
        $this->approvalRequiredConditions = $approvalRequiredConditions ?: [
            'high_amount' => Money::fromAmount(5000, 'TRY'),
            'late_refund_days' => 21, // 21 gün sonrası geç iade
            'multiple_refunds' => 3 // 3'ten fazla iade talebi
        ];
    }

    public function applyRule(EntityInterface $entity, array $context = []): OrderRuleResult
    {
        if (!$entity instanceof Order) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Entity is not an order',
                ['entity_type' => get_class($entity)]
            );
        }

        $currentStatus = $entity->getStatus();
        $refundAmount = $context['refund_amount'] ?? $entity->getTotalAmount();
        $refundReason = $context['refund_reason'] ?? null;
        $isPartialRefund = $context['partial'] ?? false;
        
        $errors = [];
        $warnings = [];
        $requiredActions = [];
        $metadata = [];

        // İade edilemez durum kontrolü
        if (in_array($currentStatus, $this->nonRefundableStatuses)) {
            return OrderRuleResult::denied(
                $this->getName(),
                "Order cannot be refunded in '{$currentStatus}' status",
                [
                    'current_status' => $currentStatus,
                    'non_refundable_statuses' => $this->nonRefundableStatuses
                ]
            );
        }

        // İade edilebilir durum kontrolü
        if (!in_array($currentStatus, $this->refundableStatuses)) {
            $errors[] = "Order status '{$currentStatus}' is not eligible for refund";
        }

        // Ödeme durumu kontrolü
        if (!$entity->isPaid()) {
            $errors[] = 'Order must be paid before refund can be processed';
        }

        // Zaman sınırı kontrolü
        $timeCheck = $this->checkRefundTimeLimit($entity);
        if (!$timeCheck['allowed']) {
            if ($timeCheck['require_approval']) {
                $requiredActions[] = 'late_refund_approval';
                $warnings[] = $timeCheck['message'];
            } else {
                $errors[] = $timeCheck['message'];
            }
        }

        // Tutar kontrolü
        if ($refundAmount instanceof Money) {
            if ($refundAmount->isGreaterThan($entity->getTotalAmount())) {
                $errors[] = 'Refund amount cannot exceed order total amount';
            }

            if ($refundAmount->isGreaterThan($this->maxRefundAmount)) {
                $errors[] = "Refund amount exceeds maximum allowed: {$this->maxRefundAmount->getAmountInMajorUnit()} {$this->maxRefundAmount->getCurrency()}";
            }
        }

        // Kısmi iade kontrolü
        if ($isPartialRefund && !$this->partialRefundAllowed) {
            $errors[] = 'Partial refunds are not allowed';
        }

        // İade nedeni kontrolü
        if (empty($refundReason)) {
            $requiredActions[] = 'refund_reason';
            $warnings[] = 'Refund reason is required';
        }

        // Onay gerektiren durumlar kontrolü
        $approvalChecks = $this->checkApprovalRequirements($entity, $refundAmount, $context);
        if (!empty($approvalChecks['required_approvals'])) {
            $requiredActions = array_merge($requiredActions, $approvalChecks['required_approvals']);
            $warnings = array_merge($warnings, $approvalChecks['warnings']);
        }

        // İade ücreti hesaplama
        $refundFee = $this->calculateRefundFee($entity, $refundAmount, $context);
        if ($refundFee->isGreaterThan(Money::fromAmount(0, $refundFee->getCurrency()))) {
            $warnings[] = "Refund fee will be deducted: {$refundFee->getAmountInMajorUnit()} {$refundFee->getCurrency()}";
            $metadata['refund_fee'] = $refundFee->toArray();
        }

        // Net iade tutarı hesaplama
        if ($refundAmount instanceof Money) {
            $netRefundAmount = $refundAmount->subtract($refundFee);
            $metadata['net_refund_amount'] = $netRefundAmount->toArray();
            $metadata['gross_refund_amount'] = $refundAmount->toArray();
        }

        // Teslimat tarihi kontrolü (sadece delivered siparişler için)
        if ($currentStatus === 'delivered') {
            $deliveryDate = $entity->getDeliveredAt();
            if ($deliveryDate) {
                $daysSinceDelivery = $deliveryDate->diffInDays(Carbon::now());
                $metadata['days_since_delivery'] = $daysSinceDelivery;
                
                if ($daysSinceDelivery > $this->refundPeriodDays) {
                    $errors[] = "Refund period expired. {$daysSinceDelivery} days since delivery, maximum allowed is {$this->refundPeriodDays} days";
                }
            }
        }

        // Sonuç değerlendirmesi
        if (!empty($errors)) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Order refund failed: ' . implode(', ', $errors),
                array_merge($metadata, [
                    'current_status' => $currentStatus,
                    'errors' => $errors,
                    'warnings' => $warnings
                ])
            );
        }

        if (!empty($requiredActions)) {
            return OrderRuleResult::requiresAction(
                $this->getName(),
                $requiredActions,
                'Order refund requires additional actions',
                array_merge($metadata, [
                    'current_status' => $currentStatus,
                    'warnings' => $warnings,
                    'refund_reason' => $refundReason,
                    'is_partial_refund' => $isPartialRefund
                ])
            );
        }

        return OrderRuleResult::allowed(
            $this->getName(),
            'refunded',
            [],
            'Order refund allowed',
            array_merge($metadata, [
                'current_status' => $currentStatus,
                'new_status' => 'refunded',
                'refund_reason' => $refundReason,
                'is_partial_refund' => $isPartialRefund,
                'refunded_at' => Carbon::now()->toISOString()
            ])
        );
    }

    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        return $entity instanceof Order && 
               (isset($context['action']) && $context['action'] === 'refund');
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getName(): string
    {
        return 'order_refund';
    }

    public function getDescription(): string
    {
        return 'Validates order refund requests including time limits, fees, and business rules';
    }

    /**
     * İade zaman sınırını kontrol et
     */
    private function checkRefundTimeLimit(Order $order): array
    {
        $orderDate = $order->getCreatedAt();
        $daysSinceOrder = $orderDate->diffInDays(Carbon::now());

        // Teslimat tarihi varsa onu kullan
        $deliveryDate = $order->getDeliveredAt();
        if ($deliveryDate) {
            $daysSinceDelivery = $deliveryDate->diffInDays(Carbon::now());
            
            if ($daysSinceDelivery <= $this->refundPeriodDays) {
                return [
                    'allowed' => true,
                    'message' => 'Within refund time limit'
                ];
            }

            // Geç iade - onay ile mümkün
            if ($daysSinceDelivery <= ($this->refundPeriodDays * 1.5)) {
                return [
                    'allowed' => false,
                    'require_approval' => true,
                    'message' => "Late refund request ({$daysSinceDelivery} days since delivery), approval required"
                ];
            }

            return [
                'allowed' => false,
                'require_approval' => false,
                'message' => "Refund period expired ({$daysSinceDelivery} days since delivery, maximum {$this->refundPeriodDays} days)"
            ];
        }

        // Teslimat tarihi yoksa sipariş tarihini kullan
        if ($daysSinceOrder <= $this->refundPeriodDays) {
            return [
                'allowed' => true,
                'message' => 'Within refund time limit'
            ];
        }

        return [
            'allowed' => false,
            'require_approval' => false,
            'message' => "Refund period expired ({$daysSinceOrder} days since order, maximum {$this->refundPeriodDays} days)"
        ];
    }

    /**
     * Onay gereksinimlerini kontrol et
     */
    private function checkApprovalRequirements(Order $order, $refundAmount, array $context): array
    {
        $requiredApprovals = [];
        $warnings = [];

        // Yüksek tutar kontrolü
        if ($refundAmount instanceof Money && 
            $refundAmount->isGreaterThan($this->approvalRequiredConditions['high_amount'])) {
            $requiredApprovals[] = 'high_amount_approval';
            $warnings[] = 'High amount refund requires manager approval';
        }

        // Geç iade kontrolü
        $deliveryDate = $order->getDeliveredAt();
        if ($deliveryDate) {
            $daysSinceDelivery = $deliveryDate->diffInDays(Carbon::now());
            if ($daysSinceDelivery > $this->approvalRequiredConditions['late_refund_days']) {
                $requiredApprovals[] = 'late_refund_approval';
                $warnings[] = 'Late refund request requires approval';
            }
        }

        // Çoklu iade kontrolü (context'ten geçmiş iade sayısı)
        $previousRefundCount = $context['previous_refund_count'] ?? 0;
        if ($previousRefundCount >= $this->approvalRequiredConditions['multiple_refunds']) {
            $requiredApprovals[] = 'multiple_refunds_approval';
            $warnings[] = 'Multiple refund requests require approval';
        }

        // Genel onay gereksinimi
        if ($this->requireApproval && (!isset($context['approved']) || !$context['approved'])) {
            $requiredApprovals[] = 'manager_approval';
            $warnings[] = 'Manager approval required for refund';
        }

        return [
            'required_approvals' => array_unique($requiredApprovals),
            'warnings' => $warnings
        ];
    }

    /**
     * İade ücretini hesapla
     */
    private function calculateRefundFee(Order $order, $refundAmount, array $context): Money
    {
        if (!$refundAmount instanceof Money) {
            return Money::fromAmount(0, 'TRY');
        }

        // İade ücreti yüzdesi 0 ise ücretsiz
        if ($this->refundFeePercentage <= 0) {
            return Money::fromAmount(0, $refundAmount->getCurrency());
        }

        // Durum bazlı ücret hesaplama
        $feePercentage = $this->refundFeePercentage;
        
        // Müşteri hatası değilse ücret alma
        $refundReason = $context['refund_reason'] ?? '';
        if (in_array(strtolower($refundReason), ['defective', 'wrong_item', 'damaged', 'not_as_described'])) {
            return Money::fromAmount(0, $refundAmount->getCurrency());
        }

        $feeAmount = $refundAmount->multiply($feePercentage / 100);
        
        // Maksimum ücret sınırı (iade tutarının %10'u)
        $maxFee = $refundAmount->multiply(0.10);
        
        return $feeAmount->isGreaterThan($maxFee) ? $maxFee : $feeAmount;
    }

    /**
     * İade edilebilir durumları ayarla
     */
    public function setRefundableStatuses(array $statuses): self
    {
        $this->refundableStatuses = $statuses;
        return $this;
    }

    /**
     * İade süresini ayarla
     */
    public function setRefundPeriodDays(int $days): self
    {
        $this->refundPeriodDays = $days;
        return $this;
    }

    /**
     * Maksimum iade tutarını ayarla
     */
    public function setMaxRefundAmount(Money $amount): self
    {
        $this->maxRefundAmount = $amount;
        return $this;
    }

    /**
     * İade ücreti yüzdesini ayarla
     */
    public function setRefundFeePercentage(float $percentage): self
    {
        $this->refundFeePercentage = $percentage;
        return $this;
    }

    /**
     * Standart iade kuralı
     */
    public static function standard(): self
    {
        return new self(
            ['delivered'],
            ['cancelled', 'refunded'],
            30, // 30 gün
            Money::fromAmount(50000, 'TRY'),
            2.0, // %2 iade ücreti
            400,
            true, // Kısmi iade izinli
            false
        );
    }

    /**
     * Sıkı iade kuralı
     */
    public static function strict(): self
    {
        return new self(
            ['delivered'],
            ['cancelled', 'refunded', 'shipped'],
            14, // 14 gün
            Money::fromAmount(10000, 'TRY'),
            5.0, // %5 iade ücreti
            400,
            false, // Kısmi iade yasak
            true // Onay gerekli
        );
    }

    /**
     * Esnek iade kuralı
     */
    public static function flexible(): self
    {
        return new self(
            ['delivered', 'shipped'],
            ['cancelled', 'refunded'],
            60, // 60 gün
            Money::fromAmount(100000, 'TRY'),
            0.0, // Ücretsiz iade
            400,
            true, // Kısmi iade izinli
            false
        );
    }
}
