<?php

namespace App\Domain\Shared\Rules\Order;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;
use Carbon\Carbon;

/**
 * OrderModificationRule
 * Sipariş değişiklik kuralı
 */
class OrderModificationRule implements OrderRuleInterface
{
    private array $modifiableStatuses;
    private array $nonModifiableStatuses;
    private int $modificationTimeLimit; // dakika cinsinden
    private array $allowedModifications;
    private Money $modificationFeeThreshold;
    private float $modificationFeePercentage;
    private int $priority;
    private bool $requireApproval;
    private array $approvalRequiredModifications;

    public function __construct(
        array $modifiableStatuses = ['pending', 'confirmed'],
        array $nonModifiableStatuses = ['shipped', 'delivered', 'completed', 'cancelled'],
        int $modificationTimeLimit = 120, // 2 saat
        array $allowedModifications = [],
        Money $modificationFeeThreshold = null,
        float $modificationFeePercentage = 0.0,
        int $priority = 250,
        bool $requireApproval = false,
        array $approvalRequiredModifications = []
    ) {
        $this->modifiableStatuses = $modifiableStatuses;
        $this->nonModifiableStatuses = $nonModifiableStatuses;
        $this->modificationTimeLimit = $modificationTimeLimit;
        $this->allowedModifications = $allowedModifications ?: [
            'add_item', 'remove_item', 'change_quantity', 
            'change_address', 'change_payment_method', 'change_shipping_method'
        ];
        $this->modificationFeeThreshold = $modificationFeeThreshold ?? Money::fromAmount(1000, 'TRY');
        $this->modificationFeePercentage = $modificationFeePercentage;
        $this->priority = $priority;
        $this->requireApproval = $requireApproval;
        $this->approvalRequiredModifications = $approvalRequiredModifications ?: [
            'change_payment_method', 'major_amount_change'
        ];
    }

    public function applyRule(EntityInterface $entity, array $context = []): OrderRuleResult
    {
        if (!$entity instanceof Order) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Entity is not an order',
                ['entity_type' => get_class($entity)]
            );
        }

        $currentStatus = $entity->getStatus();
        $modificationType = $context['modification_type'] ?? null;
        $modificationData = $context['modification_data'] ?? [];
        
        $errors = [];
        $warnings = [];
        $requiredActions = [];
        $metadata = [];

        // Değişiklik tipi kontrolü
        if (!$modificationType) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Modification type is required',
                ['current_status' => $currentStatus]
            );
        }

        // Değiştirilemez durum kontrolü
        if (in_array($currentStatus, $this->nonModifiableStatuses)) {
            return OrderRuleResult::denied(
                $this->getName(),
                "Order cannot be modified in '{$currentStatus}' status",
                [
                    'current_status' => $currentStatus,
                    'non_modifiable_statuses' => $this->nonModifiableStatuses
                ]
            );
        }

        // Değiştirilebilir durum kontrolü
        if (!in_array($currentStatus, $this->modifiableStatuses)) {
            $errors[] = "Order status '{$currentStatus}' does not allow modifications";
        }

        // İzin verilen değişiklik tipi kontrolü
        if (!in_array($modificationType, $this->allowedModifications)) {
            $errors[] = "Modification type '{$modificationType}' is not allowed";
        }

        // Zaman sınırı kontrolü
        $timeCheck = $this->checkModificationTimeLimit($entity);
        if (!$timeCheck['allowed']) {
            if ($timeCheck['require_approval']) {
                $requiredActions[] = 'time_limit_approval';
                $warnings[] = $timeCheck['message'];
            } else {
                $errors[] = $timeCheck['message'];
            }
        }

        // Değişiklik özel kontrolleri
        $modificationCheck = $this->validateModification($entity, $modificationType, $modificationData);
        if (!empty($modificationCheck['errors'])) {
            $errors = array_merge($errors, $modificationCheck['errors']);
        }
        if (!empty($modificationCheck['warnings'])) {
            $warnings = array_merge($warnings, $modificationCheck['warnings']);
        }
        if (!empty($modificationCheck['required_actions'])) {
            $requiredActions = array_merge($requiredActions, $modificationCheck['required_actions']);
        }

        // Onay gerektiren değişiklik kontrolü
        if (in_array($modificationType, $this->approvalRequiredModifications)) {
            if (!isset($context['approved']) || !$context['approved']) {
                $requiredActions[] = 'modification_approval';
                $warnings[] = "Modification type '{$modificationType}' requires approval";
            }
        }

        // Genel onay gereksinimi
        if ($this->requireApproval && (!isset($context['approved']) || !$context['approved'])) {
            $requiredActions[] = 'manager_approval';
            $warnings[] = 'Manager approval required for order modification';
        }

        // Değişiklik ücreti hesaplama
        $modificationFee = $this->calculateModificationFee($entity, $modificationType, $modificationData);
        if ($modificationFee->isGreaterThan(Money::fromAmount(0, $modificationFee->getCurrency()))) {
            $warnings[] = "Modification fee will be applied: {$modificationFee->getAmountInMajorUnit()} {$modificationFee->getCurrency()}";
            $metadata['modification_fee'] = $modificationFee->toArray();
        }

        // Stok kontrolü (ürün ekleme/değiştirme için)
        if (in_array($modificationType, ['add_item', 'change_quantity'])) {
            $stockCheck = $this->checkStockAvailability($modificationData);
            if (!$stockCheck['available']) {
                $errors[] = $stockCheck['message'];
            }
        }

        // Ödeme durumu kontrolü (tutar değişikliği için)
        if ($this->isAmountChangingModification($modificationType, $modificationData)) {
            if ($entity->isPaid()) {
                $amountDifference = $this->calculateAmountDifference($entity, $modificationData);
                if ($amountDifference->isGreaterThan(Money::fromAmount(0, $amountDifference->getCurrency()))) {
                    $requiredActions[] = 'additional_payment';
                    $warnings[] = "Additional payment required: {$amountDifference->getAmountInMajorUnit()} {$amountDifference->getCurrency()}";
                    $metadata['additional_payment'] = $amountDifference->toArray();
                } elseif ($amountDifference->isLessThan(Money::fromAmount(0, $amountDifference->getCurrency()))) {
                    $requiredActions[] = 'process_refund';
                    $refundAmount = $amountDifference->multiply(-1);
                    $warnings[] = "Refund will be processed: {$refundAmount->getAmountInMajorUnit()} {$refundAmount->getCurrency()}";
                    $metadata['refund_amount'] = $refundAmount->toArray();
                }
            }
        }

        // Sonuç değerlendirmesi
        if (!empty($errors)) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Order modification failed: ' . implode(', ', $errors),
                array_merge($metadata, [
                    'current_status' => $currentStatus,
                    'modification_type' => $modificationType,
                    'errors' => $errors,
                    'warnings' => $warnings
                ])
            );
        }

        if (!empty($requiredActions)) {
            return OrderRuleResult::requiresAction(
                $this->getName(),
                $requiredActions,
                'Order modification requires additional actions',
                array_merge($metadata, [
                    'current_status' => $currentStatus,
                    'modification_type' => $modificationType,
                    'warnings' => $warnings,
                    'modification_data' => $modificationData
                ])
            );
        }

        return OrderRuleResult::allowed(
            $this->getName(),
            $currentStatus, // Durum değişmez, sadece içerik değişir
            [],
            'Order modification allowed',
            array_merge($metadata, [
                'current_status' => $currentStatus,
                'modification_type' => $modificationType,
                'modification_data' => $modificationData,
                'modified_at' => Carbon::now()->toISOString()
            ])
        );
    }

    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        return $entity instanceof Order && 
               (isset($context['action']) && $context['action'] === 'modify');
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getName(): string
    {
        return 'order_modification';
    }

    public function getDescription(): string
    {
        return 'Validates order modification requests including time limits, fees, and business rules';
    }

    /**
     * Değişiklik zaman sınırını kontrol et
     */
    private function checkModificationTimeLimit(Order $order): array
    {
        $createdAt = $order->getCreatedAt();
        $minutesSinceCreation = $createdAt->diffInMinutes(Carbon::now());

        if ($minutesSinceCreation <= $this->modificationTimeLimit) {
            return [
                'allowed' => true,
                'message' => 'Within modification time limit'
            ];
        }

        // Zaman sınırı aşıldı, ama bazı durumlarda onay ile değiştirilebilir
        if ($order->getStatus() === 'confirmed' && $minutesSinceCreation <= ($this->modificationTimeLimit * 2)) {
            return [
                'allowed' => false,
                'require_approval' => true,
                'message' => "Modification time limit exceeded ({$this->modificationTimeLimit} minutes), approval required"
            ];
        }

        return [
            'allowed' => false,
            'require_approval' => false,
            'message' => "Modification time limit exceeded ({$this->modificationTimeLimit} minutes)"
        ];
    }

    /**
     * Değişiklik validasyonu
     */
    private function validateModification(Order $order, string $modificationType, array $modificationData): array
    {
        $errors = [];
        $warnings = [];
        $requiredActions = [];

        switch ($modificationType) {
            case 'add_item':
                if (empty($modificationData['product_id']) || empty($modificationData['quantity'])) {
                    $errors[] = 'Product ID and quantity are required for adding item';
                }
                break;

            case 'remove_item':
                if (empty($modificationData['item_id'])) {
                    $errors[] = 'Item ID is required for removing item';
                }
                if (count($order->getItems()) <= 1) {
                    $errors[] = 'Cannot remove item - order must contain at least one item';
                }
                break;

            case 'change_quantity':
                if (empty($modificationData['item_id']) || !isset($modificationData['new_quantity'])) {
                    $errors[] = 'Item ID and new quantity are required for quantity change';
                }
                if (isset($modificationData['new_quantity']) && $modificationData['new_quantity'] <= 0) {
                    $errors[] = 'New quantity must be greater than zero';
                }
                break;

            case 'change_address':
                if (empty($modificationData['address_type']) || empty($modificationData['new_address'])) {
                    $errors[] = 'Address type and new address are required for address change';
                }
                if ($order->getStatus() === 'processing') {
                    $warnings[] = 'Address change in processing status may delay shipment';
                }
                break;

            case 'change_payment_method':
                if (empty($modificationData['new_payment_method'])) {
                    $errors[] = 'New payment method is required';
                }
                if ($order->isPaid()) {
                    $requiredActions[] = 'payment_method_change_approval';
                    $warnings[] = 'Changing payment method for paid order requires special handling';
                }
                break;

            case 'change_shipping_method':
                if (empty($modificationData['new_shipping_method'])) {
                    $errors[] = 'New shipping method is required';
                }
                break;
        }

        return [
            'errors' => $errors,
            'warnings' => $warnings,
            'required_actions' => $requiredActions
        ];
    }

    /**
     * Stok durumunu kontrol et
     */
    private function checkStockAvailability(array $modificationData): array
    {
        // Bu method gerçek implementasyonda inventory service'i kullanacak
        // Şimdilik basit bir kontrol yapıyoruz
        
        if (isset($modificationData['product_id']) && isset($modificationData['quantity'])) {
            // Gerçek implementasyonda burada product repository'den stok kontrolü yapılacak
            return [
                'available' => true,
                'message' => 'Stock available'
            ];
        }

        return [
            'available' => true,
            'message' => 'No stock check required'
        ];
    }

    /**
     * Tutar değiştiren değişiklik mi kontrol et
     */
    private function isAmountChangingModification(string $modificationType, array $modificationData): bool
    {
        return in_array($modificationType, [
            'add_item', 'remove_item', 'change_quantity', 'change_shipping_method'
        ]);
    }

    /**
     * Tutar farkını hesapla
     */
    private function calculateAmountDifference(Order $order, array $modificationData): Money
    {
        // Bu method gerçek implementasyonda sipariş tutarını yeniden hesaplayacak
        // Şimdilik örnek bir hesaplama yapıyoruz
        
        return Money::fromAmount(0, $order->getTotalAmount()->getCurrency());
    }

    /**
     * Değişiklik ücretini hesapla
     */
    private function calculateModificationFee(Order $order, string $modificationType, array $modificationData): Money
    {
        $orderAmount = $order->getTotalAmount();
        
        // Eşik tutarın altındaysa ücretsiz değişiklik
        if ($orderAmount->isLessThan($this->modificationFeeThreshold)) {
            return Money::fromAmount(0, $orderAmount->getCurrency());
        }

        // Değişiklik ücreti yüzdesi 0 ise ücretsiz
        if ($this->modificationFeePercentage <= 0) {
            return Money::fromAmount(0, $orderAmount->getCurrency());
        }

        // Değişiklik tipine göre ücret hesaplama
        $feePercentage = $this->modificationFeePercentage;
        
        // Bazı değişiklikler için ek ücret
        if (in_array($modificationType, ['change_payment_method', 'change_shipping_method'])) {
            $feePercentage *= 1.5;
        }

        $feeAmount = $orderAmount->multiply($feePercentage / 100);
        
        // Maksimum ücret sınırı (sipariş tutarının %5'i)
        $maxFee = $orderAmount->multiply(0.05);
        
        return $feeAmount->isGreaterThan($maxFee) ? $maxFee : $feeAmount;
    }

    /**
     * Değiştirilebilir durumları ayarla
     */
    public function setModifiableStatuses(array $statuses): self
    {
        $this->modifiableStatuses = $statuses;
        return $this;
    }

    /**
     * Değişiklik zaman sınırını ayarla
     */
    public function setModificationTimeLimit(int $minutes): self
    {
        $this->modificationTimeLimit = $minutes;
        return $this;
    }

    /**
     * İzin verilen değişiklikleri ayarla
     */
    public function setAllowedModifications(array $modifications): self
    {
        $this->allowedModifications = $modifications;
        return $this;
    }

    /**
     * Standart değişiklik kuralı
     */
    public static function standard(): self
    {
        return new self(
            ['pending', 'confirmed'],
            ['shipped', 'delivered', 'completed', 'cancelled'],
            120, // 2 saat
            ['add_item', 'remove_item', 'change_quantity', 'change_address', 'change_payment_method', 'change_shipping_method'],
            Money::fromAmount(1000, 'TRY'),
            1.0, // %1 değişiklik ücreti
            250,
            false
        );
    }

    /**
     * Sıkı değişiklik kuralı
     */
    public static function strict(): self
    {
        return new self(
            ['pending'],
            ['confirmed', 'processing', 'shipped', 'delivered', 'completed', 'cancelled'],
            60, // 1 saat
            ['change_address', 'change_shipping_method'],
            Money::fromAmount(500, 'TRY'),
            3.0, // %3 değişiklik ücreti
            250,
            true // Onay gerekli
        );
    }

    /**
     * Esnek değişiklik kuralı
     */
    public static function flexible(): self
    {
        return new self(
            ['pending', 'confirmed', 'processing'],
            ['shipped', 'delivered', 'completed', 'cancelled'],
            240, // 4 saat
            ['add_item', 'remove_item', 'change_quantity', 'change_address', 'change_payment_method', 'change_shipping_method'],
            Money::fromAmount(2000, 'TRY'),
            0.0, // Ücretsiz değişiklik
            250,
            false
        );
    }
}
