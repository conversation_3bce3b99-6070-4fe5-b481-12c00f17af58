<?php

namespace App\Domain\Shared\Rules\Payment;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Shared\Rules\BusinessRuleEngine;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;

/**
 * PaymentPolicyService
 * Ödeme politikalarını yöneten servis
 */
class PaymentPolicyService
{
    private BusinessRuleEngine $ruleEngine;
    private array $registeredRules = [];

    public function __construct()
    {
        $this->ruleEngine = new BusinessRuleEngine();
        $this->loadStandardRules();
    }

    /**
     * Ödeme işlemini doğrula
     */
    public function validatePayment(Order $order, Money $amount, string $method, array $context = []): PaymentPolicyResult
    {
        $context = array_merge($context, [
            'amount' => $amount,
            'method' => $method,
            'action' => 'payment'
        ]);
        
        $results = $this->ruleEngine->executeRules($order, $context);
        
        return new PaymentPolicyResult(
            $order,
            $amount,
            $method,
            'payment',
            $results
        );
    }

    /**
     * İade işlemini doğrula
     */
    public function validateRefund(Order $order, Money $refundAmount, array $context = []): PaymentPolicyResult
    {
        $context = array_merge($context, [
            'refund_amount' => $refundAmount,
            'action' => 'refund'
        ]);
        
        $results = $this->ruleEngine->executeRules($order, $context);
        
        return new PaymentPolicyResult(
            $order,
            $refundAmount,
            'refund',
            'refund',
            $results
        );
    }

    /**
     * Mevcut ödeme yöntemlerini getir
     */
    public function getAvailablePaymentMethods(Order $order, array $context = []): array
    {
        $methodRule = $this->getRule('payment_method');
        
        if ($methodRule instanceof PaymentMethodRule) {
            return $methodRule->getAvailableMethodsForOrderPublic($order, $context);
        }
        
        return [];
    }

    /**
     * Ödeme limitlerini getir
     */
    public function getPaymentLimits(): array
    {
        $validationRule = $this->getRule('payment_validation');
        
        if ($validationRule instanceof PaymentValidationRule) {
            return $validationRule->getPaymentLimits();
        }
        
        return [
            'min_amount' => Money::fromAmount(1, 'TRY')->toArray(),
            'max_amount' => Money::fromAmount(50000, 'TRY')->toArray(),
            'allowed_methods' => ['credit_card', 'bank_transfer', 'paypal'],
        ];
    }

    /**
     * İade bilgilerini getir
     */
    public function getRefundInfo(Order $order): array
    {
        $refundRule = $this->getRule('refund');
        
        if ($refundRule instanceof RefundRule) {
            return $refundRule->getRefundInfo($order);
        }
        
        return [
            'refund_eligible' => false,
            'reason' => 'Refund rule not configured'
        ];
    }

    /**
     * Ödeme güvenlik kontrolü
     */
    public function performSecurityCheck(Order $order, array $context = []): array
    {
        $securityIssues = [];
        $riskLevel = 'low';

        // Yüksek tutar kontrolü
        $amount = $context['amount'] ?? $order->getTotalAmount();
        if ($amount->isGreaterThan(Money::fromAmount(10000, $amount->getCurrency()))) {
            $securityIssues[] = 'High amount transaction';
            $riskLevel = 'high';
        }

        // Uluslararası ödeme kontrolü
        if ($context['international'] ?? false) {
            $securityIssues[] = 'International payment';
            $riskLevel = $riskLevel === 'high' ? 'high' : 'medium';
        }

        // Hızlı ardışık işlem kontrolü
        if ($context['rapid_transactions'] ?? false) {
            $securityIssues[] = 'Rapid successive transactions detected';
            $riskLevel = 'high';
        }

        return [
            'risk_level' => $riskLevel,
            'security_issues' => $securityIssues,
            'requires_additional_verification' => $riskLevel === 'high',
            'recommended_actions' => $this->getRecommendedSecurityActions($riskLevel, $securityIssues),
        ];
    }

    /**
     * Ödeme kuralı kaydet
     */
    public function registerRule(PaymentRuleInterface $rule): self
    {
        $this->ruleEngine->addRule($rule);
        $this->registeredRules[$rule->getName()] = $rule;
        return $this;
    }

    /**
     * Ödeme kuralını kaldır
     */
    public function unregisterRule(string $ruleName): self
    {
        if (isset($this->registeredRules[$ruleName])) {
            $this->ruleEngine->removeRule($this->registeredRules[$ruleName]);
            unset($this->registeredRules[$ruleName]);
        }
        return $this;
    }

    /**
     * Belirli bir kuralı getir
     */
    public function getRule(string $ruleName): ?PaymentRuleInterface
    {
        return $this->registeredRules[$ruleName] ?? null;
    }

    /**
     * Tüm kuralları getir
     */
    public function getRules(): array
    {
        return $this->registeredRules;
    }

    /**
     * Servis istatistiklerini getir
     */
    public function getServiceStatistics(): array
    {
        return [
            'total_rules' => count($this->registeredRules),
            'rule_names' => array_keys($this->registeredRules),
            'engine_stats' => $this->ruleEngine->getStatistics(),
        ];
    }

    /**
     * Servis sağlık kontrolü
     */
    public function healthCheck(): array
    {
        $issues = [];
        
        if (empty($this->registeredRules)) {
            $issues[] = 'No payment rules registered';
        }
        
        // Temel kuralların varlığını kontrol et
        $requiredRules = ['payment_validation', 'refund', 'payment_method'];
        foreach ($requiredRules as $ruleName) {
            if (!isset($this->registeredRules[$ruleName])) {
                $issues[] = "Required rule '{$ruleName}' is not registered";
            }
        }
        
        return [
            'healthy' => empty($issues),
            'issues' => $issues,
            'total_rules' => count($this->registeredRules),
        ];
    }

    /**
     * Standart kuralları yükle
     */
    private function loadStandardRules(): void
    {
        $this->registerRule(new PaymentValidationRule());
        $this->registerRule(new RefundRule());
        $this->registerRule(new PaymentMethodRule());
    }

    /**
     * Güvenlik seviyesine göre önerilen aksiyonları getir
     */
    private function getRecommendedSecurityActions(string $riskLevel, array $issues): array
    {
        $actions = [];

        switch ($riskLevel) {
            case 'high':
                $actions[] = 'Require identity verification';
                $actions[] = 'Manual review required';
                $actions[] = 'Additional authentication';
                break;
            case 'medium':
                $actions[] = 'Enhanced verification';
                $actions[] = 'Monitor transaction';
                break;
            case 'low':
                $actions[] = 'Standard processing';
                break;
        }

        if (in_array('International payment', $issues)) {
            $actions[] = 'Verify international payment details';
        }

        if (in_array('High amount transaction', $issues)) {
            $actions[] = 'Manager approval required';
        }

        return $actions;
    }
}

/**
 * PaymentPolicyResult
 * Ödeme politikası sonucu
 */
class PaymentPolicyResult
{
    private Order $order;
    private Money $amount;
    private string $method;
    private string $action;
    private array $ruleResults;

    public function __construct(Order $order, Money $amount, string $method, string $action, array $ruleResults)
    {
        $this->order = $order;
        $this->amount = $amount;
        $this->method = $method;
        $this->action = $action;
        $this->ruleResults = $ruleResults;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function getAmount(): Money
    {
        return $this->amount;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function getAction(): string
    {
        return $this->action;
    }

    public function getRuleResults(): array
    {
        return $this->ruleResults;
    }

    public function isValid(): bool
    {
        foreach ($this->ruleResults as $result) {
            if (!$result->isValid()) {
                return false;
            }
        }
        return true;
    }

    public function isPaymentAllowed(): bool
    {
        foreach ($this->ruleResults as $result) {
            if ($result instanceof PaymentRuleResult) {
                if (!$result->isPaymentAllowed()) {
                    return false;
                }
            }
        }
        return true;
    }

    public function getErrors(): array
    {
        $errors = [];
        foreach ($this->ruleResults as $result) {
            $errors = array_merge($errors, $result->getErrors());
        }
        return $errors;
    }

    public function getWarnings(): array
    {
        $warnings = [];
        foreach ($this->ruleResults as $result) {
            $warnings = array_merge($warnings, $result->getWarnings());
        }
        return $warnings;
    }

    public function getRequiredVerifications(): array
    {
        $verifications = [];
        foreach ($this->ruleResults as $result) {
            if ($result instanceof PaymentRuleResult) {
                $verifications = array_merge($verifications, $result->getRequiredVerifications());
            }
        }
        return array_unique($verifications);
    }

    public function toArray(): array
    {
        return [
            'order_id' => $this->order->getId(),
            'amount' => $this->amount->toArray(),
            'method' => $this->method,
            'action' => $this->action,
            'valid' => $this->isValid(),
            'payment_allowed' => $this->isPaymentAllowed(),
            'errors' => $this->getErrors(),
            'warnings' => $this->getWarnings(),
            'required_verifications' => $this->getRequiredVerifications(),
            'rule_results' => array_map(fn($result) => $result->toArray(), $this->ruleResults),
        ];
    }
}
