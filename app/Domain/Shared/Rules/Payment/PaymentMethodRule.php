<?php

namespace App\Domain\Shared\Rules\Payment;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;

/**
 * PaymentMethodRule
 * Ödeme yöntemi kuralı - hangi ödeme yöntemlerinin kullanılabileceğini belirler
 */
class PaymentMethodRule implements PaymentRuleInterface
{
    private int $priority;
    private array $methodConfigurations;
    private array $countryRestrictions;
    private array $categoryRestrictions;

    public function __construct(
        int $priority = 300,
        array $methodConfigurations = [],
        array $countryRestrictions = [],
        array $categoryRestrictions = []
    ) {
        $this->priority = $priority;
        $this->methodConfigurations = $methodConfigurations ?: $this->getDefaultMethodConfigurations();
        $this->countryRestrictions = $countryRestrictions;
        $this->categoryRestrictions = $categoryRestrictions;
    }

    public function applyRule(EntityInterface $entity, array $context = []): PaymentRuleResult
    {
        if (!$entity instanceof Order) {
            return PaymentRuleResult::denied(
                $this->getName(),
                'Entity is not an order',
                ['entity_type' => get_class($entity)]
            );
        }

        $requestedMethod = $context['method'] ?? null;
        $paymentAmount = $context['amount'] ?? $entity->getTotalAmount();
        $customerCountry = $context['customer_country'] ?? 'TR';
        $isInternational = $context['international'] ?? false;

        $availableMethods = $this->getAvailableMethodsForOrder($entity, $paymentAmount, $customerCountry, $isInternational);
        
        if (empty($availableMethods)) {
            return PaymentRuleResult::denied(
                $this->getName(),
                'No payment methods available for this order',
                [
                    'order_amount' => $paymentAmount->toArray(),
                    'customer_country' => $customerCountry,
                    'is_international' => $isInternational
                ]
            );
        }

        // Belirli bir yöntem talep edildiyse kontrol et
        if ($requestedMethod) {
            if (!isset($availableMethods[$requestedMethod])) {
                return PaymentRuleResult::denied(
                    $this->getName(),
                    "Payment method '{$requestedMethod}' is not available for this order",
                    [
                        'requested_method' => $requestedMethod,
                        'available_methods' => array_keys($availableMethods)
                    ]
                );
            }

            $methodConfig = $availableMethods[$requestedMethod];
            
            // Yöntem özel limitlerini kontrol et
            if (isset($methodConfig['min_amount']) && $paymentAmount->isLessThan($methodConfig['min_amount'])) {
                return PaymentRuleResult::denied(
                    $this->getName(),
                    "Payment amount is below minimum for {$requestedMethod}",
                    [
                        'payment_amount' => $paymentAmount->toArray(),
                        'method_min_amount' => $methodConfig['min_amount']->toArray()
                    ]
                );
            }

            if (isset($methodConfig['max_amount']) && $paymentAmount->isGreaterThan($methodConfig['max_amount'])) {
                return PaymentRuleResult::denied(
                    $this->getName(),
                    "Payment amount exceeds maximum for {$requestedMethod}",
                    [
                        'payment_amount' => $paymentAmount->toArray(),
                        'method_max_amount' => $methodConfig['max_amount']->toArray()
                    ]
                );
            }
        }

        $warnings = [];
        $requiredVerifications = [];

        // Yüksek riskli yöntemler için uyarı
        foreach ($availableMethods as $method => $config) {
            if ($config['risk_level'] === 'high') {
                $warnings[] = "Payment method '{$method}' has high risk level";
                $requiredVerifications[] = 'high_risk_verification';
            }
        }

        // Uluslararası ödeme uyarısı
        if ($isInternational) {
            $warnings[] = 'International payment may incur additional fees';
            $requiredVerifications[] = 'international_verification';
        }

        return PaymentRuleResult::allowed(
            $this->getName(),
            $paymentAmount,
            array_keys($availableMethods),
            'Payment methods determined',
            [
                'available_methods' => $availableMethods,
                'customer_country' => $customerCountry,
                'is_international' => $isInternational,
                'required_verifications' => $requiredVerifications
            ]
        )->withWarnings($warnings);
    }

    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        return $entity instanceof Order;
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getName(): string
    {
        return 'payment_method';
    }

    public function getDescription(): string
    {
        return 'Determines available payment methods based on order characteristics and restrictions';
    }

    /**
     * Sipariş için mevcut ödeme yöntemlerini getir
     */
    private function getAvailableMethodsForOrder(Order $order, Money $amount, string $country, bool $isInternational): array
    {
        $availableMethods = [];

        foreach ($this->methodConfigurations as $method => $config) {
            // Aktif mi kontrol et
            if (!$config['enabled']) {
                continue;
            }

            // Tutar limitleri kontrol et
            if (isset($config['min_amount']) && $amount->isLessThan($config['min_amount'])) {
                continue;
            }

            if (isset($config['max_amount']) && $amount->isGreaterThan($config['max_amount'])) {
                continue;
            }

            // Ülke kısıtlamaları kontrol et
            if (isset($config['allowed_countries']) && !in_array($country, $config['allowed_countries'])) {
                continue;
            }

            if (isset($config['blocked_countries']) && in_array($country, $config['blocked_countries'])) {
                continue;
            }

            // Uluslararası ödeme desteği kontrol et
            if ($isInternational && !($config['supports_international'] ?? false)) {
                continue;
            }

            // Kategori kısıtlamaları kontrol et
            $hasRestrictedCategory = false;
            foreach ($order->getItems() as $item) {
                $categoryId = $item->getProduct()->getCategoryId();
                if (isset($config['blocked_categories']) && in_array($categoryId, $config['blocked_categories'])) {
                    $hasRestrictedCategory = true;
                    break;
                }
            }

            if ($hasRestrictedCategory) {
                continue;
            }

            $availableMethods[$method] = $config;
        }

        return $availableMethods;
    }

    /**
     * Varsayılan ödeme yöntemi konfigürasyonları
     */
    private function getDefaultMethodConfigurations(): array
    {
        return [
            'credit_card' => [
                'enabled' => true,
                'min_amount' => Money::fromAmount(1, 'TRY'),
                'max_amount' => Money::fromAmount(50000, 'TRY'),
                'supports_international' => true,
                'risk_level' => 'medium',
                'processing_fee_percentage' => 2.5,
                'allowed_countries' => ['TR', 'US', 'GB', 'DE', 'FR'],
            ],
            'bank_transfer' => [
                'enabled' => true,
                'min_amount' => Money::fromAmount(100, 'TRY'),
                'max_amount' => Money::fromAmount(100000, 'TRY'),
                'supports_international' => false,
                'risk_level' => 'low',
                'processing_fee_percentage' => 0,
                'allowed_countries' => ['TR'],
            ],
            'paypal' => [
                'enabled' => true,
                'min_amount' => Money::fromAmount(5, 'TRY'),
                'max_amount' => Money::fromAmount(25000, 'TRY'),
                'supports_international' => true,
                'risk_level' => 'medium',
                'processing_fee_percentage' => 3.0,
                'blocked_countries' => ['CN', 'RU'],
            ],
            'cash_on_delivery' => [
                'enabled' => true,
                'min_amount' => Money::fromAmount(10, 'TRY'),
                'max_amount' => Money::fromAmount(2000, 'TRY'),
                'supports_international' => false,
                'risk_level' => 'high',
                'processing_fee_percentage' => 0,
                'allowed_countries' => ['TR'],
                'blocked_categories' => [10, 11], // Dijital ürünler
            ],
            'crypto' => [
                'enabled' => false,
                'min_amount' => Money::fromAmount(50, 'TRY'),
                'max_amount' => Money::fromAmount(10000, 'TRY'),
                'supports_international' => true,
                'risk_level' => 'high',
                'processing_fee_percentage' => 1.0,
            ],
        ];
    }

    /**
     * Ödeme yöntemi konfigürasyonunu güncelle
     */
    public function updateMethodConfiguration(string $method, array $config): self
    {
        $this->methodConfigurations[$method] = array_merge(
            $this->methodConfigurations[$method] ?? [],
            $config
        );
        return $this;
    }

    /**
     * Ödeme yöntemini etkinleştir/devre dışı bırak
     */
    public function setMethodEnabled(string $method, bool $enabled): self
    {
        if (isset($this->methodConfigurations[$method])) {
            $this->methodConfigurations[$method]['enabled'] = $enabled;
        }
        return $this;
    }

    /**
     * Belirli bir sipariş için mevcut ödeme yöntemlerini getir
     */
    public function getAvailableMethodsForOrderPublic(Order $order, array $context = []): array
    {
        $amount = $context['amount'] ?? $order->getTotalAmount();
        $country = $context['customer_country'] ?? 'TR';
        $isInternational = $context['international'] ?? false;

        return $this->getAvailableMethodsForOrder($order, $amount, $country, $isInternational);
    }
}
