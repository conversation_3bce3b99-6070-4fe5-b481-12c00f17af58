<?php

namespace App\Domain\Shared\Rules\Payment;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Shared\Rules\BusinessRuleResult;
use App\Core\Domain\ValueObjects\Money;

/**
 * PaymentRuleInterface
 * Ödeme kuralları için interface
 */
interface PaymentRuleInterface
{
    /**
     * Ödeme kuralını uygula
     */
    public function applyRule(EntityInterface $entity, array $context = []): PaymentRuleResult;

    /**
     * Kural bu entity için geçerli mi
     */
    public function isApplicable(EntityInterface $entity, array $context = []): bool;

    /**
     * Kuralın önceliğini getir
     */
    public function getPriority(): int;

    /**
     * Kuralın adını getir
     */
    public function getName(): string;

    /**
     * Kuralın açıklamasını getir
     */
    public function getDescription(): string;
}

/**
 * PaymentRuleResult
 * Ödeme kuralı sonucu
 */
class PaymentRuleResult extends BusinessRuleResult
{
    private bool $paymentAllowed;
    private ?Money $authorizedAmount = null;
    private ?Money $maxAmount = null;
    private ?Money $minAmount = null;
    private array $allowedMethods = [];
    private array $requiredVerifications = [];
    private ?string $reason = null;

    public function __construct(
        bool $valid,
        string $ruleName,
        bool $paymentAllowed,
        ?Money $authorizedAmount = null,
        ?Money $maxAmount = null,
        ?Money $minAmount = null,
        array $allowedMethods = [],
        array $requiredVerifications = [],
        ?string $reason = null,
        array $errors = [],
        array $warnings = [],
        array $metadata = []
    ) {
        parent::__construct($valid, $ruleName, $errors, $warnings, $metadata);

        $this->paymentAllowed = $paymentAllowed;
        $this->authorizedAmount = $authorizedAmount;
        $this->maxAmount = $maxAmount;
        $this->minAmount = $minAmount;
        $this->allowedMethods = $allowedMethods;
        $this->requiredVerifications = $requiredVerifications;
        $this->reason = $reason;
    }

    public static function allowed(
        string $ruleName,
        ?Money $authorizedAmount = null,
        array $allowedMethods = [],
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            true,
            $authorizedAmount,
            null,
            null,
            $allowedMethods,
            [],
            $reason,
            [],
            [],
            $metadata
        );
    }

    public static function denied(
        string $ruleName,
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            false,
            $ruleName,
            false,
            null,
            null,
            null,
            [],
            [],
            $reason,
            ['Payment denied'],
            [],
            $metadata
        );
    }

    public static function restricted(
        string $ruleName,
        ?Money $maxAmount = null,
        ?Money $minAmount = null,
        array $allowedMethods = [],
        array $requiredVerifications = [],
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            true,
            null,
            $maxAmount,
            $minAmount,
            $allowedMethods,
            $requiredVerifications,
            $reason,
            [],
            ['Payment restrictions applied'],
            $metadata
        );
    }

    public function isPaymentAllowed(): bool
    {
        return $this->paymentAllowed;
    }

    public function getAuthorizedAmount(): ?Money
    {
        return $this->authorizedAmount;
    }

    public function getMaxAmount(): ?Money
    {
        return $this->maxAmount;
    }

    public function getMinAmount(): ?Money
    {
        return $this->minAmount;
    }

    public function getAllowedMethods(): array
    {
        return $this->allowedMethods;
    }

    public function getRequiredVerifications(): array
    {
        return $this->requiredVerifications;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }

    public function isMethodAllowed(string $method): bool
    {
        return empty($this->allowedMethods) || in_array($method, $this->allowedMethods);
    }

    public function isAmountWithinLimits(Money $amount): bool
    {
        if ($this->minAmount && $amount->isLessThan($this->minAmount)) {
            return false;
        }

        if ($this->maxAmount && $amount->isGreaterThan($this->maxAmount)) {
            return false;
        }

        return true;
    }

    public function hasVerificationRequirements(): bool
    {
        return !empty($this->requiredVerifications);
    }

    public function withWarnings(array $warnings): self
    {
        $result = clone $this;
        $result->warnings = array_merge($result->warnings, $warnings);
        return $result;
    }

    public function withWarning(string $warning): self
    {
        $result = clone $this;
        $result->warnings[] = $warning;
        return $result;
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'payment_allowed' => $this->paymentAllowed,
            'authorized_amount' => $this->authorizedAmount?->toArray(),
            'max_amount' => $this->maxAmount?->toArray(),
            'min_amount' => $this->minAmount?->toArray(),
            'allowed_methods' => $this->allowedMethods,
            'required_verifications' => $this->requiredVerifications,
            'reason' => $this->reason,
        ]);
    }
}
