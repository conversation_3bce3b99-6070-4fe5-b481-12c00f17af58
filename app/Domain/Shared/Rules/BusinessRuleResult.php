<?php

namespace App\Domain\Shared\Rules;

/**
 * BusinessRuleResult
 * Business rule sonucu için base sınıf
 */
class BusinessRuleResult
{
    protected bool $valid;
    protected string $ruleName;
    protected array $errors = [];
    protected array $warnings = [];
    protected array $metadata = [];

    public function __construct(
        bool $valid,
        string $ruleName,
        array $errors = [],
        array $warnings = [],
        array $metadata = []
    ) {
        $this->valid = $valid;
        $this->ruleName = $ruleName;
        $this->errors = $errors;
        $this->warnings = $warnings;
        $this->metadata = $metadata;
    }

    public static function valid(string $ruleName, array $metadata = []): self
    {
        return new self(true, $ruleName, [], [], $metadata);
    }

    public static function invalid(string $ruleName, array $errors = [], array $metadata = []): self
    {
        return new self(false, $ruleName, $errors, [], $metadata);
    }

    public static function error(string $ruleName, string $error, array $metadata = []): self
    {
        return new self(false, $ruleName, [$error], [], $metadata);
    }

    public static function warning(string $ruleName, string $warning, array $metadata = []): self
    {
        return new self(true, $ruleName, [], [$warning], $metadata);
    }

    public function isValid(): bool
    {
        return $this->valid;
    }

    public function getRuleName(): string
    {
        return $this->ruleName;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getWarnings(): array
    {
        return $this->warnings;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    public function hasWarnings(): bool
    {
        return !empty($this->warnings);
    }

    public function addError(string $error): self
    {
        $this->errors[] = $error;
        $this->valid = false;
        return $this;
    }

    public function addWarning(string $warning): self
    {
        $this->warnings[] = $warning;
        return $this;
    }

    public function addMetadata(string $key, $value): self
    {
        $this->metadata[$key] = $value;
        return $this;
    }

    public function withErrors(array $errors): self
    {
        $result = clone $this;
        $result->errors = array_merge($result->errors, $errors);
        if (!empty($errors)) {
            $result->valid = false;
        }
        return $result;
    }

    public function withWarnings(array $warnings): self
    {
        $result = clone $this;
        $result->warnings = array_merge($result->warnings, $warnings);
        return $result;
    }

    public function withMetadata(array $metadata): self
    {
        $result = clone $this;
        $result->metadata = array_merge($result->metadata, $metadata);
        return $result;
    }

    public function toArray(): array
    {
        return [
            'valid' => $this->valid,
            'rule_name' => $this->ruleName,
            'errors' => $this->errors,
            'warnings' => $this->warnings,
            'metadata' => $this->metadata,
            'has_errors' => $this->hasErrors(),
            'has_warnings' => $this->hasWarnings(),
        ];
    }

    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT);
    }

    public function __toString(): string
    {
        $status = $this->valid ? 'VALID' : 'INVALID';
        $errorCount = count($this->errors);
        $warningCount = count($this->warnings);
        
        return "BusinessRuleResult[{$this->ruleName}]: {$status} (Errors: {$errorCount}, Warnings: {$warningCount})";
    }
}
