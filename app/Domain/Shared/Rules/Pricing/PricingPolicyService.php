<?php

namespace App\Domain\Shared\Rules\Pricing;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Shared\Services\BaseDomainService;
use App\Core\Domain\ValueObjects\Money;
use Illuminate\Support\Facades\Log;

/**
 * PricingCalculationResult
 * Fiyat hesaplama sonucu
 */
class PricingCalculationResult
{
    private Money $basePrice;
    private Money $finalPrice;
    private array $ruleResults;
    private float $calculationTime;

    public function __construct(Money $basePrice, Money $finalPrice, array $ruleResults, float $calculationTime)
    {
        $this->basePrice = $basePrice;
        $this->finalPrice = $finalPrice;
        $this->ruleResults = $ruleResults;
        $this->calculationTime = $calculationTime;
    }

    public function getBasePrice(): Money
    {
        return $this->basePrice;
    }

    public function getFinalPrice(): Money
    {
        return $this->finalPrice;
    }

    public function getRuleResults(): array
    {
        return $this->ruleResults;
    }

    public function getCalculationTime(): float
    {
        return $this->calculationTime;
    }

    public function getTotalAdjustment(): Money
    {
        return $this->finalPrice->subtract($this->basePrice);
    }

    public function getTotalDiscount(): Money
    {
        $totalDiscount = Money::zero();

        foreach ($this->ruleResults as $result) {
            if ($result instanceof PricingRuleResult && $result->isDiscount()) {
                $totalDiscount = $totalDiscount->add($result->getDiscountAmount());
            }
        }

        return $totalDiscount;
    }

    public function getTotalTax(): Money
    {
        $totalTax = Money::zero();

        foreach ($this->ruleResults as $result) {
            if ($result instanceof PricingRuleResult && $result->isTax()) {
                $totalTax = $totalTax->add($result->getAdjustment());
            }
        }

        return $totalTax;
    }

    public function hasDiscounts(): bool
    {
        return !$this->getTotalDiscount()->isZero();
    }

    public function hasTaxes(): bool
    {
        return !$this->getTotalTax()->isZero();
    }

    public function toArray(): array
    {
        return [
            'base_price' => $this->basePrice->toArray(),
            'final_price' => $this->finalPrice->toArray(),
            'total_adjustment' => $this->getTotalAdjustment()->toArray(),
            'total_discount' => $this->getTotalDiscount()->toArray(),
            'total_tax' => $this->getTotalTax()->toArray(),
            'calculation_time' => $this->calculationTime,
            'rule_results' => array_map(fn($result) => $result->toArray(), $this->ruleResults),
        ];
    }
}

/**
 * PricingValidationResult
 * Fiyat doğrulama sonucu
 */
class PricingValidationResult
{
    private bool $isValid;
    private Money $proposedPrice;
    private Money $calculatedPrice;
    private array $errors;
    private array $warnings;
    private PricingCalculationResult $calculationResult;

    public function __construct(
        bool $isValid,
        Money $proposedPrice,
        Money $calculatedPrice,
        array $errors,
        array $warnings,
        PricingCalculationResult $calculationResult
    ) {
        $this->isValid = $isValid;
        $this->proposedPrice = $proposedPrice;
        $this->calculatedPrice = $calculatedPrice;
        $this->errors = $errors;
        $this->warnings = $warnings;
        $this->calculationResult = $calculationResult;
    }

    public function isValid(): bool
    {
        return $this->isValid;
    }

    public function getProposedPrice(): Money
    {
        return $this->proposedPrice;
    }

    public function getCalculatedPrice(): Money
    {
        return $this->calculatedPrice;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getWarnings(): array
    {
        return $this->warnings;
    }

    public function getCalculationResult(): PricingCalculationResult
    {
        return $this->calculationResult;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    public function hasWarnings(): bool
    {
        return !empty($this->warnings);
    }

    public function getPriceDifference(): Money
    {
        return $this->proposedPrice->subtract($this->calculatedPrice);
    }

    public function getPriceDifferencePercentage(): float
    {
        if ($this->proposedPrice->isZero()) {
            return 0.0;
        }

        $difference = $this->getPriceDifference();
        return abs($difference->getAmountInMajorUnit() / $this->proposedPrice->getAmountInMajorUnit()) * 100;
    }

    public function toArray(): array
    {
        return [
            'is_valid' => $this->isValid,
            'proposed_price' => $this->proposedPrice->toArray(),
            'calculated_price' => $this->calculatedPrice->toArray(),
            'price_difference' => $this->getPriceDifference()->toArray(),
            'price_difference_percentage' => $this->getPriceDifferencePercentage(),
            'errors' => $this->errors,
            'warnings' => $this->warnings,
            'has_errors' => $this->hasErrors(),
            'has_warnings' => $this->hasWarnings(),
            'calculation_result' => $this->calculationResult->toArray(),
        ];
    }
}

/**
 * PricingPolicyService
 * Fiyatlandırma politikalarını yöneten domain service
 */
class PricingPolicyService extends BaseDomainService
{
    private array $pricingRules = [];
    private array $rulesByPriority = [];

    protected function initialize(): void
    {
        $this->name = 'pricing_policy_service';
        $this->version = '1.0.0';
        $this->description = 'Pricing policies and rules management service';
        $this->supportedOperations = [
            'register_rule',
            'unregister_rule',
            'calculate_price',
            'apply_rules',
            'get_applicable_rules',
            'validate_pricing',
        ];
    }

    /**
     * Fiyatlandırma kuralı kaydet
     */
    public function registerRule(PricingRuleInterface $rule): void
    {
        $ruleId = $this->generateRuleId($rule);
        $this->pricingRules[$ruleId] = $rule;

        // Öncelik sırasına göre yeniden düzenle
        $this->rebuildPriorityIndex();

        $this->logOperation('register_rule', [
            'rule_id' => $ruleId,
            'rule_name' => $rule->getName(),
            'priority' => $rule->getPriority(),
        ]);
    }

    /**
     * Fiyatlandırma kuralını kaldır
     */
    public function unregisterRule(string $ruleId): bool
    {
        if (!isset($this->pricingRules[$ruleId])) {
            return false;
        }

        unset($this->pricingRules[$ruleId]);
        $this->rebuildPriorityIndex();

        $this->logOperation('unregister_rule', [
            'rule_id' => $ruleId,
        ]);

        return true;
    }

    /**
     * Entity için fiyat hesapla
     */
    public function calculatePrice(EntityInterface $entity, Money $basePrice, array $context = []): PricingCalculationResult
    {
        $startTime = microtime(true);

        try {
            $applicableRules = $this->getApplicableRules($entity, $context);
            $results = [];
            $currentPrice = $basePrice;

            foreach ($applicableRules as $ruleId => $rule) {
                $ruleResult = $rule->applyRule($entity, $currentPrice, $context);
                $results[$ruleId] = $ruleResult;

                if ($ruleResult->isValid() && $ruleResult->hasAdjustment()) {
                    $currentPrice = $ruleResult->getAdjustedPrice();
                }
            }

            $duration = microtime(true) - $startTime;

            $calculationResult = new PricingCalculationResult(
                $basePrice,
                $currentPrice,
                $results,
                $duration
            );

            $this->logOperation('calculate_price', [
                'entity_type' => get_class($entity),
                'entity_id' => method_exists($entity, 'getId') ? $entity->getId() : null,
                'base_price' => $basePrice->toArray(),
                'final_price' => $currentPrice->toArray(),
                'rules_applied' => count($results),
                'duration_ms' => round($duration * 1000, 2),
            ]);

            return $calculationResult;

        } catch (\Exception $e) {
            $this->logError('calculate_price', $e, [
                'entity_type' => get_class($entity),
                'base_price' => $basePrice->toArray(),
            ]);

            throw $e;
        }
    }

    /**
     * Entity için geçerli kuralları bul
     */
    public function getApplicableRules(EntityInterface $entity, array $context = []): array
    {
        $applicableRules = [];

        foreach ($this->rulesByPriority as $ruleId => $rule) {
            if ($rule->isApplicable($entity, $context)) {
                $applicableRules[$ruleId] = $rule;
            }
        }

        return $applicableRules;
    }

    /**
     * Fiyatlandırma doğrulaması yap
     */
    public function validatePricing(EntityInterface $entity, Money $proposedPrice, array $context = []): PricingValidationResult
    {
        $calculationResult = $this->calculatePrice($entity, $proposedPrice, $context);

        $isValid = true;
        $errors = [];
        $warnings = [];

        // Hesaplanan fiyat ile önerilen fiyat arasında büyük fark var mı
        $priceDifference = $proposedPrice->subtract($calculationResult->getFinalPrice());
        $differencePercentage = $proposedPrice->isZero() ? 0 :
            abs($priceDifference->getAmountInMajorUnit() / $proposedPrice->getAmountInMajorUnit()) * 100;

        if ($differencePercentage > 10) { // %10'dan fazla fark
            $warnings[] = "Proposed price differs significantly from calculated price ({$differencePercentage}%)";
        }

        // Negatif fiyat kontrolü
        if ($calculationResult->getFinalPrice()->isNegative()) {
            $isValid = false;
            $errors[] = 'Final price cannot be negative';
        }

        return new PricingValidationResult(
            $isValid,
            $proposedPrice,
            $calculationResult->getFinalPrice(),
            $errors,
            $warnings,
            $calculationResult
        );
    }

    /**
     * Standart fiyatlandırma kurallarını yükle
     */
    public function loadStandardRules(): void
    {
        // Miktar indirimi kuralı
        $this->registerRule(QuantityDiscountRule::standard());

        // Türkiye KDV kuralı
        $this->registerRule(TaxCalculationRule::turkishVAT());

        $this->logOperation('load_standard_rules', [
            'rules_loaded' => 2,
        ]);
    }

    /**
     * Tüm kuralları getir
     */
    public function getAllRules(): array
    {
        return $this->pricingRules;
    }

    /**
     * Öncelik sırasına göre kuralları getir
     */
    public function getRulesByPriority(): array
    {
        return $this->rulesByPriority;
    }

    /**
     * Kural var mı kontrol et
     */
    public function hasRule(string $ruleId): bool
    {
        return isset($this->pricingRules[$ruleId]);
    }

    /**
     * Kuralı getir
     */
    public function getRule(string $ruleId): ?PricingRuleInterface
    {
        return $this->pricingRules[$ruleId] ?? null;
    }

    /**
     * Kural ID'si oluştur
     */
    private function generateRuleId(PricingRuleInterface $rule): string
    {
        return md5($rule->getName() . $rule->getDescription() . $rule->getPriority());
    }

    /**
     * Öncelik indeksini yeniden oluştur
     */
    private function rebuildPriorityIndex(): void
    {
        $this->rulesByPriority = $this->pricingRules;

        uasort($this->rulesByPriority, function ($a, $b) {
            return $a->getPriority() <=> $b->getPriority();
        });
    }

    /**
     * Service sağlık kontrolü
     */
    protected function performHealthCheck(): void
    {
        // Kuralların yüklenip yüklenmediğini kontrol et
        if (empty($this->pricingRules)) {
            throw new \Exception('No pricing rules loaded');
        }

        // Her kuralın geçerli olduğunu kontrol et
        foreach ($this->pricingRules as $ruleId => $rule) {
            if (!$rule instanceof PricingRuleInterface) {
                throw new \Exception("Invalid pricing rule: {$ruleId}");
            }
        }
    }

    /**
     * Service istatistiklerini getir
     */
    public function getStatistics(): array
    {
        return array_merge(parent::getStatistics(), [
            'total_rules' => count($this->pricingRules),
            'rules_by_priority' => array_map(
                fn($rule) => [
                    'name' => $rule->getName(),
                    'priority' => $rule->getPriority(),
                ],
                $this->rulesByPriority
            ),
        ]);
    }
}
