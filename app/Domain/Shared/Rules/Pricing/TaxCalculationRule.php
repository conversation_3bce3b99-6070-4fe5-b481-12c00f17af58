<?php

namespace App\Domain\Shared\Rules\Pricing;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Shared\Rules\BusinessRule;
use App\Domain\Shared\Rules\BusinessRuleResult;
use App\Core\Domain\ValueObjects\Money;

/**
 * TaxCalculationRule
 * Vergi hesaplama kuralı
 */
class TaxCalculationRule extends BusinessRule implements PricingRuleInterface
{
    private float $taxRate;
    private string $taxType;
    private array $exemptCategories;

    public function __construct(
        float $taxRate = 18.0,
        string $taxType = 'VAT',
        array $exemptCategories = [],
        int $priority = 200,
        string $name = 'tax_calculation',
        string $description = 'Tax calculation rule',
        string $group = 'pricing'
    ) {
        parent::__construct($name, $description, $group, $priority);

        $this->taxRate = max(0, min(100, $taxRate)); // 0-100% arası
        $this->taxType = $taxType;
        $this->exemptCategories = $exemptCategories;

        $this->setMetadataValue('tax_rate', $this->taxRate);
        $this->setMetadataValue('tax_type', $this->taxType);
        $this->setMetadataValue('exempt_categories', $this->exemptCategories);
    }

    /**
     * Türkiye KDV kuralı
     */
    public static function turkishVAT(): self
    {
        return new self(
            18.0,
            'KDV',
            ['books', 'education', 'health'],
            200,
            'turkish_vat',
            'Turkish VAT calculation rule'
        );
    }

    /**
     * Düşük KDV kuralı
     */
    public static function reducedVAT(): self
    {
        return new self(
            8.0,
            'KDV',
            [],
            200,
            'reduced_vat',
            'Reduced VAT calculation rule'
        );
    }

    /**
     * Özel vergi kuralı
     */
    public static function custom(float $taxRate, string $taxType, array $exemptCategories = []): self
    {
        return new self(
            $taxRate,
            $taxType,
            $exemptCategories,
            200,
            'custom_tax',
            "Custom {$taxType} calculation rule"
        );
    }

    public function applyRule(EntityInterface $entity, Money $basePrice, array $context = []): PricingRuleResult
    {
        // Vergi muafiyeti kontrolü
        if ($this->isExempt($entity, $context)) {
            return PricingRuleResult::noChange(
                $this->name,
                $basePrice,
                'Tax exempt item'
            );
        }

        $taxAmount = $basePrice->multiply($this->taxRate / 100);
        $priceWithTax = $basePrice->add($taxAmount);

        return PricingRuleResult::tax(
            $this->name,
            $basePrice,
            $priceWithTax,
            "{$this->taxType} {$this->taxRate}% applied",
            [
                'tax_rate' => $this->taxRate,
                'tax_type' => $this->taxType,
                'tax_amount' => $taxAmount->toArray(),
                'base_price' => $basePrice->toArray(),
                'price_with_tax' => $priceWithTax->toArray(),
            ]
        );
    }

    public function getPriority(): int
    {
        return parent::getPriority();
    }

    protected function checkApplicability(EntityInterface $entity, array $context = []): bool
    {
        // Vergi oranı 0'dan büyük olmalı
        return $this->taxRate > 0;
    }

    protected function performEvaluation(EntityInterface $entity, array $context = []): BusinessRuleResult
    {
        $basePrice = $this->extractBasePrice($context);

        if (!$basePrice) {
            return BusinessRuleResult::error(
                $this->name,
                'Base price is required for tax calculation'
            );
        }

        $result = $this->applyRule($entity, $basePrice, $context);

        return BusinessRuleResult::valid($this->name, [
            'pricing_result' => $result->toArray(),
        ]);
    }

    /**
     * Vergi muafiyeti kontrolü
     */
    private function isExempt(EntityInterface $entity, array $context): bool
    {
        // Entity'den kategori bilgisini al
        $category = $this->extractCategory($entity, $context);

        if ($category && in_array($category, $this->exemptCategories)) {
            return true;
        }

        // Context'te tax_exempt flag'i var mı
        if (isset($context['tax_exempt']) && $context['tax_exempt'] === true) {
            return true;
        }

        return false;
    }

    /**
     * Entity'den kategori bilgisini çıkar
     */
    private function extractCategory(EntityInterface $entity, array $context): ?string
    {
        // Context'ten kategori
        if (isset($context['category'])) {
            return $context['category'];
        }

        // Entity'de getCategory metodu var mı
        if (method_exists($entity, 'getCategory')) {
            return $entity->getCategory();
        }

        // Entity'de getCategoryCode metodu var mı
        if (method_exists($entity, 'getCategoryCode')) {
            return $entity->getCategoryCode();
        }

        return null;
    }

    /**
     * Context'ten base price bilgisini çıkar
     */
    private function extractBasePrice(array $context): ?Money
    {
        if (isset($context['base_price']) && $context['base_price'] instanceof Money) {
            return $context['base_price'];
        }

        if (isset($context['price']) && $context['price'] instanceof Money) {
            return $context['price'];
        }

        return null;
    }

    /**
     * Vergi oranını getir
     */
    public function getTaxRate(): float
    {
        return $this->taxRate;
    }

    /**
     * Vergi tipini getir
     */
    public function getTaxType(): string
    {
        return $this->taxType;
    }

    /**
     * Muaf kategorileri getir
     */
    public function getExemptCategories(): array
    {
        return $this->exemptCategories;
    }

    /**
     * Vergi oranını güncelle
     */
    public function updateTaxRate(float $taxRate): void
    {
        $this->taxRate = max(0, min(100, $taxRate));
        $this->setMetadataValue('tax_rate', $this->taxRate);
    }

    /**
     * Muaf kategori ekle
     */
    public function addExemptCategory(string $category): void
    {
        if (!in_array($category, $this->exemptCategories)) {
            $this->exemptCategories[] = $category;
            $this->setMetadataValue('exempt_categories', $this->exemptCategories);
        }
    }

    /**
     * Muaf kategori kaldır
     */
    public function removeExemptCategory(string $category): void
    {
        $this->exemptCategories = array_filter(
            $this->exemptCategories,
            fn($cat) => $cat !== $category
        );
        $this->setMetadataValue('exempt_categories', $this->exemptCategories);
    }

    /**
     * Belirli fiyat için vergi tutarını hesapla
     */
    public function calculateTaxAmount(Money $basePrice): Money
    {
        return $basePrice->multiply($this->taxRate / 100);
    }

    /**
     * Vergi dahil fiyattan vergi tutarını çıkar
     */
    public function extractTaxFromInclusivePrice(Money $inclusivePrice): Money
    {
        $divisor = 1 + ($this->taxRate / 100);
        return $inclusivePrice->divide($divisor)->multiply($this->taxRate / 100);
    }
}
