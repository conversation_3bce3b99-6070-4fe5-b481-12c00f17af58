<?php

namespace App\Domain\Shared\ValueObjects;

use App\Domain\Shared\Contracts\ValueObjectInterface;

/**
 * BaseValueObject
 * Tüm value object'ler için temel sınıf
 */
abstract class BaseValueObject implements ValueObjectInterface
{
    /**
     * Value object'in string temsilini getir
     */
    public function toString(): string
    {
        $value = $this->getValue();
        
        if (is_scalar($value)) {
            return (string) $value;
        }
        
        if (is_array($value)) {
            return json_encode($value);
        }
        
        if (is_object($value) && method_exists($value, '__toString')) {
            return (string) $value;
        }
        
        return serialize($value);
    }

    /**
     * Value object'in array temsilini getir
     */
    public function toArray(): array
    {
        $value = $this->getValue();
        
        if (is_array($value)) {
            return $value;
        }
        
        return ['value' => $value];
    }

    /**
     * Value object'in JSON temsilini getir
     */
    public function toJson(): string
    {
        return json_encode($this->toArray());
    }

    /**
     * Eşitlik kontrolü
     */
    public function equals(ValueObjectInterface $other): bool
    {
        if (!$other instanceof static) {
            return false;
        }

        return $this->getValue() === $other->getValue();
    }

    /**
     * Value object'in hash'ini getir
     */
    public function getHash(): string
    {
        return md5($this->toString());
    }

    /**
     * Value object'in geçerli olup olmadığını kontrol et
     */
    public function isValid(): bool
    {
        try {
            $this->validate();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Value object'in boş olup olmadığını kontrol et
     */
    public function isEmpty(): bool
    {
        $value = $this->getValue();
        
        if ($value === null) {
            return true;
        }
        
        if (is_string($value)) {
            return trim($value) === '';
        }
        
        if (is_array($value)) {
            return empty($value);
        }
        
        if (is_numeric($value)) {
            return false; // Numeric values are never considered empty
        }
        
        return empty($value);
    }

    /**
     * Value object'in null olup olmadığını kontrol et
     */
    public function isNull(): bool
    {
        return $this->getValue() === null;
    }

    /**
     * Value object'in sıfır olup olmadığını kontrol et
     */
    public function isZero(): bool
    {
        $value = $this->getValue();
        
        if (is_numeric($value)) {
            return (float) $value === 0.0;
        }
        
        return false;
    }

    /**
     * Value object'in pozitif olup olmadığını kontrol et
     */
    public function isPositive(): bool
    {
        $value = $this->getValue();
        
        if (is_numeric($value)) {
            return (float) $value > 0;
        }
        
        return false;
    }

    /**
     * Value object'in negatif olup olmadığını kontrol et
     */
    public function isNegative(): bool
    {
        $value = $this->getValue();
        
        if (is_numeric($value)) {
            return (float) $value < 0;
        }
        
        return false;
    }

    /**
     * Value object'in boyutunu getir
     */
    public function getSize(): int
    {
        $value = $this->getValue();
        
        if (is_string($value)) {
            return strlen($value);
        }
        
        if (is_array($value)) {
            return count($value);
        }
        
        if (is_countable($value)) {
            return count($value);
        }
        
        return 1;
    }

    /**
     * Value object'in tipini getir
     */
    public function getType(): string
    {
        return gettype($this->getValue());
    }

    /**
     * Value object'in sınıf adını getir
     */
    public function getClassName(): string
    {
        return static::class;
    }

    /**
     * Value object'in kısa sınıf adını getir
     */
    public function getShortClassName(): string
    {
        $className = static::class;
        $parts = explode('\\', $className);
        return end($parts);
    }

    /**
     * Validation logic - alt sınıflar tarafından override edilmeli
     */
    protected function validate(): void
    {
        // Default implementation - no validation
    }

    /**
     * Value object'in debug bilgilerini getir
     */
    public function getDebugInfo(): array
    {
        return [
            'class' => static::class,
            'value' => $this->getValue(),
            'type' => $this->getType(),
            'size' => $this->getSize(),
            'is_valid' => $this->isValid(),
            'is_empty' => $this->isEmpty(),
            'is_null' => $this->isNull(),
            'hash' => $this->getHash(),
        ];
    }

    /**
     * Magic method for string conversion
     */
    public function __toString(): string
    {
        return $this->toString();
    }

    /**
     * Magic method for serialization
     */
    public function __serialize(): array
    {
        return $this->toArray();
    }

    /**
     * Magic method for unserialization
     */
    public function __unserialize(array $data): void
    {
        // Alt sınıflar tarafından implement edilmeli
    }
}
