<?php

namespace App\Domain\Customers\Entities;

use App\Domain\Shared\Contracts\EntityInterface;
use Carbon\Carbon;

/**
 * Customer Entity
 * Müşteri entity'si
 */
class Customer implements EntityInterface
{
    private ?int $id = null;
    private string $name;
    private string $email;
    private bool $emailVerified = false;
    private bool $phoneVerified = false;
    private ?Carbon $createdAt = null;
    private ?Carbon $updatedAt = null;

    public function __construct()
    {
        // Empty constructor for test purposes
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getName(): string
    {
        return $this->name ?? '';
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getEmail(): string
    {
        return $this->email ?? '';
    }

    public function setEmail(string $email): void
    {
        $this->email = $email;
    }

    public function isEmailVerified(): bool
    {
        return $this->emailVerified;
    }

    public function setEmailVerified(bool $verified): void
    {
        $this->emailVerified = $verified;
    }

    public function isPhoneVerified(): bool
    {
        return $this->phoneVerified;
    }

    public function setPhoneVerified(bool $verified): void
    {
        $this->phoneVerified = $verified;
    }

    public function getCreatedAt(): Carbon
    {
        return $this->createdAt ?? Carbon::now();
    }

    public function setCreatedAt(?Carbon $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getUpdatedAt(): Carbon
    {
        return $this->updatedAt ?? Carbon::now();
    }

    public function setUpdatedAt(?Carbon $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function equals(EntityInterface $other): bool
    {
        if (!$other instanceof self) {
            return false;
        }

        return $this->getId() === $other->getId();
    }

    public function toArray(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'email' => $this->getEmail(),
            'email_verified' => $this->isEmailVerified(),
            'phone_verified' => $this->isPhoneVerified(),
            'created_at' => $this->getCreatedAt()?->format('Y-m-d H:i:s'),
            'updated_at' => $this->getUpdatedAt()?->format('Y-m-d H:i:s'),
        ];
    }

    public function isValid(): bool
    {
        return !empty($this->name) && !empty($this->email) && filter_var($this->email, FILTER_VALIDATE_EMAIL);
    }

    public function toString(): string
    {
        return sprintf('Customer[%d]: %s <%s>', $this->getId(), $this->getName(), $this->getEmail());
    }
}
