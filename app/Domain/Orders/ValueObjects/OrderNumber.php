<?php

namespace App\Domain\Orders\ValueObjects;

use InvalidArgumentException;

class OrderNumber
{
    private string $value;

    public function __construct(string $value)
    {
        $this->validate($value);
        $this->value = $value;
    }

    public static function generate(): self
    {
        $value = 'ORD-' . strtoupper(substr(md5(uniqid()), 0, 8));
        return new self($value);
    }

    public static function fromString(string $value): self
    {
        return new self($value);
    }

    private function validate(string $value): void
    {
        if (empty($value)) {
            throw new InvalidArgumentException('Order number cannot be empty');
        }

        if (strlen($value) < 3) {
            throw new InvalidArgumentException('Order number must be at least 3 characters long');
        }

        if (strlen($value) > 50) {
            throw new InvalidArgumentException('Order number cannot be longer than 50 characters');
        }

        // <PERSON><PERSON><PERSON> alfan<PERSON><PERSON> karak<PERSON>ler, tire ve alt çizgi kabul et
        if (!preg_match('/^[A-Za-z0-9\-_]+$/', $value)) {
            throw new InvalidArgumentException('Order number can only contain alphanumeric characters, hyphens and underscores');
        }
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function equals(OrderNumber $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
