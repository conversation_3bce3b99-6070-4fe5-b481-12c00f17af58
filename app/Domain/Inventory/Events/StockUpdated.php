<?php

namespace App\Domain\Inventory\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Inventory\Entities\Stock;
use App\Domain\Inventory\ValueObjects\StockLevel;
use Carbon\Carbon;

/**
 * StockUpdated Domain Event
 * Stok seviyesi güncellendiğinde tetiklenir
 */
class StockUpdated implements DomainEvent
{
    private Stock $stock;
    private string $reason;
    private StockLevel $previousQuantity;
    private StockLevel $newQuantity;
    private Carbon $occurredOn;

    public function __construct(
        Stock $stock,
        string $reason,
        StockLevel $previousQuantity,
        StockLevel $newQuantity
    ) {
        $this->stock = $stock;
        $this->reason = $reason;
        $this->previousQuantity = $previousQuantity;
        $this->newQuantity = $newQuantity;
        $this->occurredOn = Carbon::now();
    }

    /**
     * Stock entity'sini getir
     */
    public function getStock(): Stock
    {
        return $this->stock;
    }

    /**
     * Güncelleme nedenini getir
     */
    public function getReason(): string
    {
        return $this->reason;
    }

    /**
     * <PERSON>nce<PERSON> miktarı getir
     */
    public function getPreviousQuantity(): StockLevel
    {
        return $this->previousQuantity;
    }

    /**
     * Yeni miktarı getir
     */
    public function getNewQuantity(): StockLevel
    {
        return $this->newQuantity;
    }

    /**
     * Miktar değişimini getir
     */
    public function getQuantityChange(): StockLevel
    {
        if ($this->newQuantity->isGreaterThan($this->previousQuantity)) {
            return $this->newQuantity->subtract($this->previousQuantity);
        } else {
            return $this->previousQuantity->subtract($this->newQuantity);
        }
    }

    /**
     * Artış mı azalış mı kontrol et
     */
    public function isIncrease(): bool
    {
        return $this->newQuantity->isGreaterThan($this->previousQuantity);
    }

    /**
     * Azalış mı kontrol et
     */
    public function isDecrease(): bool
    {
        return $this->newQuantity->isLessThan($this->previousQuantity);
    }

    /**
     * Değişiklik yok mu kontrol et
     */
    public function isNoChange(): bool
    {
        return $this->newQuantity->equals($this->previousQuantity);
    }

    /**
     * Event'in gerçekleşme zamanı
     */
    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    /**
     * Event adı
     */
    public function getEventName(): string
    {
        return 'inventory.stock_updated';
    }

    /**
     * Event verisi
     */
    public function getEventData(): array
    {
        return [
            'stock_id' => $this->stock->getId(),
            'product_id' => $this->stock->getProductId(),
            'product_variant_id' => $this->stock->getProductVariantId(),
            'reason' => $this->reason,
            'previous_quantity' => $this->previousQuantity->getValue(),
            'new_quantity' => $this->newQuantity->getValue(),
            'quantity_change' => $this->getQuantityChange()->getValue(),
            'change_type' => $this->isIncrease() ? 'increase' : ($this->isDecrease() ? 'decrease' : 'no_change'),
            'location' => $this->stock->getLocation()?->toArray(),
            'is_low_stock' => $this->stock->isLowStock(),
            'needs_reorder' => $this->stock->needsReorder(),
            'updated_at' => $this->occurredOn->toISOString(),
        ];
    }

    /**
     * Aggregate ID
     */
    public function getAggregateId(): string
    {
        return (string) $this->stock->getId();
    }
}
