<?php

namespace App\Console\Commands;

use App\Events\VariantStockUpdated;
use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TestDefaultVariantUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-default-variant-update {product_id? : Ürün ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Varsayılan varyantın stoğunu sıfırlayarak otomatik güncelleme sistemini test eder';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $productId = $this->argument('product_id');

        if (!$productId) {
            // Varyantları olan bir ürün bul
            $product = Product::whereHas('variants', function($query) {
                $query->where('stock', '>', 0);
            })->first();

            if (!$product) {
                $this->error('Stokta varyantı olan bir ürün bulunamadı!');
                return 1;
            }

            $productId = $product->id;
        } else {
            $product = Product::find($productId);
            if (!$product) {
                $this->error('Belirtilen ID ile ürün bulunamadı!');
                return 1;
            }

            if (!$product->variants()->count()) {
                $this->error('Bu ürünün varyantları bulunmuyor!');
                return 1;
            }
        }

        $this->info("Test ediliyor: Ürün #{$productId} - {$product->name}");

        // Tüm varyantları listele
        $variants = ProductVariant::where('product_id', $productId)->get();
        $this->info("\nÜrünün varyantları:");
        foreach ($variants as $variant) {
            $defaultStatus = $variant->is_default ? '[VARSAYILAN]' : '';
            $stockStatus = $variant->stock > 0 ? 'Stokta' : 'Stok Dışı';
            $this->line("#{$variant->id} - {$variant->sku} - Stok: {$variant->stock} - Durum: {$variant->status} - {$stockStatus} {$defaultStatus}");
        }

        // Varsayılan varyantı bul
        $defaultVariant = ProductVariant::where('product_id', $productId)
            ->where('is_default', true)
            ->first();

        if (!$defaultVariant) {
            $this->warn("\nBu ürün için varsayılan varyant bulunamadı. İlk varyantı varsayılan olarak ayarlıyorum...");

            try {
                DB::beginTransaction();

                $firstVariant = ProductVariant::where('product_id', $productId)->first();
                $firstVariant->is_default = true;
                $firstVariant->save();

                $defaultVariant = $firstVariant;

                DB::commit();
                $this->info("Varsayılan varyant ayarlandı: #{$defaultVariant->id} - {$defaultVariant->sku}");
            } catch (\Exception $e) {
                DB::rollBack();
                $this->error('Varsayılan varyant ayarlanırken hata oluştu: ' . $e->getMessage());
                return 1;
            }
        }

        $this->info("\nMevcut varsayılan varyant: #{$defaultVariant->id} - {$defaultVariant->sku}");
        $this->info("Mevcut stok: {$defaultVariant->stock}");
        $this->info("Mevcut durum: {$defaultVariant->status}");

        // Stokta olan başka varyant var mı kontrol et
        $otherInStockVariants = ProductVariant::where('product_id', $productId)
            ->where('id', '!=', $defaultVariant->id)
            ->where('stock', '>', 0)
            ->where('status', 'in_stock')
            ->count();

        if ($otherInStockVariants == 0) {
            $this->warn("\nDikkat: Stokta başka varyant bulunmuyor. Varsayılan varyant değişmeyebilir.");

            // Stokta olmayan bir varyantı stoklu hale getir
            $otherVariant = ProductVariant::where('product_id', $productId)
                ->where('id', '!=', $defaultVariant->id)
                ->where(function($query) {
                    $query->where('stock', '=', 0)
                        ->orWhere('status', '!=', 'in_stock');
                })
                ->first();

            if ($otherVariant) {
                $this->info("\nTest için başka bir varyantı stoklu hale getiriyorum: #{$otherVariant->id} - {$otherVariant->sku}");
                $otherVariant->stock = 10;
                $otherVariant->status = 'in_stock';
                $otherVariant->save();
            }
        }

        // Varsayılan varyantın stoğunu sıfırla
        try {
            $this->info("\nVarsayılan varyantın stoğunu sıfırlıyorum...");
            $oldStock = $defaultVariant->stock;
            $defaultVariant->stock = 0;
            $defaultVariant->status = 'out_of_stock'; // Durumu da güncelle
            $defaultVariant->save();

            $this->info("Varsayılan varyantın stoğu sıfırlandı ve durumu 'out_of_stock' olarak güncellendi.");

            // Yeni varsayılan varyantı kontrol et
            $this->info("\nEvent'in işlenmesi için 3 saniye bekliyorum...");
            sleep(3); // Event'in işlenmesi için biraz bekle

            $newDefaultVariant = ProductVariant::where('product_id', $productId)
                ->where('is_default', true)
                ->first();

            if (!$newDefaultVariant) {
                $this->error('Yeni varsayılan varyant bulunamadı!');
                return 1;
            }

            if ($newDefaultVariant->id === $defaultVariant->id) {
                $this->warn("\nVarsayılan varyant değişmedi. Stokta başka varyant olmayabilir veya event işlenmemiş olabilir.");

                // Queue worker'a bir event gönder
                $this->info("\nQueue worker'a bir event gönderiyorum...");
                event(new VariantStockUpdated($defaultVariant, $oldStock));

                $this->info("Event gönderildi. 3 saniye daha bekliyorum...");
                sleep(3);

                // Tekrar kontrol et
                $newDefaultVariant = ProductVariant::where('product_id', $productId)
                    ->where('is_default', true)
                    ->first();

                if ($newDefaultVariant->id === $defaultVariant->id) {
                    $this->warn("\nVarsayılan varyant hala değişmedi. Lütfen queue worker'ın çalıştığından emin olun.");
                    $this->info("Queue worker'ı çalıştırmak için: php artisan queue:work");
                } else {
                    $this->info("\nYeni varsayılan varyant: #{$newDefaultVariant->id} - {$newDefaultVariant->sku}");
                    $this->info("Yeni varsayılan varyantın stoğu: {$newDefaultVariant->stock}");
                    $this->info("Yeni varsayılan varyantın durumu: {$newDefaultVariant->status}");
                    $this->info('Varsayılan varyant başarıyla güncellendi!');
                }
            } else {
                $this->info("\nYeni varsayılan varyant: #{$newDefaultVariant->id} - {$newDefaultVariant->sku}");
                $this->info("Yeni varsayılan varyantın stoğu: {$newDefaultVariant->stock}");
                $this->info("Yeni varsayılan varyantın durumu: {$newDefaultVariant->status}");
                $this->info('Varsayılan varyant başarıyla güncellendi!');
            }

            // Eski varsayılan varyantın stoğunu geri yükle
            if ($oldStock > 0) {
                $this->info("\nEski varsayılan varyantın stoğunu geri yükleme işlemi yapılıyor...");
                $defaultVariant->refresh();
                $defaultVariant->stock = $oldStock;
                $defaultVariant->status = 'in_stock'; // Durumu da güncelle
                $defaultVariant->save();
                $this->info("Eski varsayılan varyantın stoğu geri yüklendi: {$oldStock} ve durumu 'in_stock' olarak güncellendi.");
            }

            return 0;
        } catch (\Exception $e) {
            $this->error('Test sırasında hata oluştu: ' . $e->getMessage());
            Log::error('Default variant test error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }
}
