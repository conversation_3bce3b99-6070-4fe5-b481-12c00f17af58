<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Domain\Products\Services\ProductDomainService;
use App\Domain\Products\Services\PricingDomainService;
use App\Domain\Products\Services\VariantDomainService;
use App\Domain\Products\Services\ProductValidationService;
use App\Domain\Products\Repositories\ProductRepositoryInterface;

/**
 * ProductServiceProvider
 * Products modülü için service provider
 */
class ProductServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Domain Services'leri kaydet
        $this->app->singleton(ProductDomainService::class, function ($app) {
            return new ProductDomainService(
                $app->make(ProductRepositoryInterface::class)
            );
        });

        $this->app->singleton(PricingDomainService::class, function ($app) {
            return new PricingDomainService();
        });

        $this->app->singleton(VariantDomainService::class, function ($app) {
            return new VariantDomainService(
                $app->make(ProductRepositoryInterface::class)
            );
        });

        $this->app->singleton(ProductValidationService::class, function ($app) {
            return new ProductValidationService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            ProductDomainService::class,
            PricingDomainService::class,
            VariantDomainService::class,
            ProductValidationService::class,
        ];
    }
}
