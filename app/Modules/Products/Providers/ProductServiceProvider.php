<?php

namespace App\Modules\Products\Providers;

use Illuminate\Support\ServiceProvider;
use App\Modules\Products\Domain\Interfaces\ProductRepositoryInterface;
use App\Modules\Products\Infrastructure\Repositories\EloquentProductRepository;

/**
 * Product Service Provider
 * Products modülünün servis sağlayıcısı
 *
 * @deprecated Bu provider yerine App\Infrastructure\Products\Providers\ProductServiceProvider kullanılmalı
 */
class ProductServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        // Infrastructure katmanındaki provider'ı kaydet
        $this->app->register(\App\Infrastructure\Products\Providers\ProductServiceProvider::class);

        // Backward compatibility için eski binding'i koru
        $this->app->bind(ProductRepositoryInterface::class, EloquentProductRepository::class);
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        // Rotalar<PERSON> yükle
        $this->loadRoutesFrom(__DIR__ . '/../Routes/api.php');

        // Migrasyonları yükleme - birleştirilmiş migration dosyaları kullanılıyor
        // $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');
    }
}
