<?php

namespace App\Application\Products\Commands;

class CreateProductCommand
{
    public function __construct(
        public readonly string $name,
        public readonly string $slug,
        public readonly string $sku,
        public readonly float $price,
        public readonly int $stockQuantity,
        public readonly int $categoryId,
        public readonly ?string $description = null,
        public readonly string $currency = 'TRY',
        public readonly bool $status = true,
        public readonly bool $isFeatured = false,
        public readonly ?float $salePrice = null,
        public readonly ?string $saleStartsAt = null,
        public readonly ?string $saleEndsAt = null,
        public readonly ?float $weight = null,
        public readonly string $weightUnit = 'g',
        public readonly ?float $length = null,
        public readonly ?float $width = null,
        public readonly ?float $height = null,
        public readonly string $dimensionUnit = 'cm',
        public readonly ?string $metaTitle = null,
        public readonly ?string $metaDescription = null,
        public readonly ?string $metaKeywords = null,
        public readonly array $images = [],
        public readonly array $attributes = [],
        public readonly int $lowStockThreshold = 5,
        public readonly bool $trackQuantity = true,
        public readonly bool $allowBackorders = false
    ) {}

    public function getName(): string
    {
        return $this->name;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getSku(): string
    {
        return $this->sku;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function getStockQuantity(): int
    {
        return $this->stockQuantity;
    }

    public function getCategoryId(): int
    {
        return $this->categoryId;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function getStatus(): bool
    {
        return $this->status;
    }

    public function isFeatured(): bool
    {
        return $this->isFeatured;
    }

    public function getSalePrice(): ?float
    {
        return $this->salePrice;
    }

    public function getSaleStartsAt(): ?string
    {
        return $this->saleStartsAt;
    }

    public function getSaleEndsAt(): ?string
    {
        return $this->saleEndsAt;
    }

    public function getWeight(): ?float
    {
        return $this->weight;
    }

    public function getWeightUnit(): string
    {
        return $this->weightUnit;
    }

    public function getLength(): ?float
    {
        return $this->length;
    }

    public function getWidth(): ?float
    {
        return $this->width;
    }

    public function getHeight(): ?float
    {
        return $this->height;
    }

    public function getDimensionUnit(): string
    {
        return $this->dimensionUnit;
    }

    public function getMetaTitle(): ?string
    {
        return $this->metaTitle;
    }

    public function getMetaDescription(): ?string
    {
        return $this->metaDescription;
    }

    public function getMetaKeywords(): ?string
    {
        return $this->metaKeywords;
    }

    public function getImages(): array
    {
        return $this->images;
    }

    public function getAttributes(): array
    {
        return $this->attributes;
    }

    public function getLowStockThreshold(): int
    {
        return $this->lowStockThreshold;
    }

    public function isTrackQuantity(): bool
    {
        return $this->trackQuantity;
    }

    public function isAllowBackorders(): bool
    {
        return $this->allowBackorders;
    }

    public function hasSalePrice(): bool
    {
        return $this->salePrice !== null && $this->salePrice > 0;
    }

    public function hasWeight(): bool
    {
        return $this->weight !== null && $this->weight > 0;
    }

    public function hasDimensions(): bool
    {
        return $this->length !== null && $this->width !== null && $this->height !== null &&
               $this->length > 0 && $this->width > 0 && $this->height > 0;
    }

    public function hasSEOData(): bool
    {
        return $this->metaTitle !== null || $this->metaDescription !== null;
    }
}
