<?php

namespace App\Application\Users\Services;

use App\Application\Users\Commands\RegisterUserCommand;
use App\Application\Users\Commands\UpdateUserCommand;
use App\Application\Users\Commands\ChangePasswordCommand;
use App\Application\Users\Commands\VerifyEmailCommand;
use App\Application\Users\Queries\GetUserQuery;
use App\Application\Users\Queries\GetUsersQuery;
use App\Application\Users\Handlers\RegisterUserHandler;
use App\Application\Users\Handlers\UpdateUserHandler;
use App\Application\Users\Handlers\ChangePasswordHandler;
use App\Application\Users\Handlers\VerifyEmailHandler;
use App\Application\Users\Handlers\GetUserHandler;
use App\Application\Users\Handlers\GetUsersHandler;
use App\Application\Users\DTOs\UserDTO;

/**
 * UserApplicationService
 * Kullanıcı işlemlerini koordine eden ana servis
 */
class UserApplicationService
{
    public function __construct(
        private RegisterUserHandler $registerUserHandler,
        private UpdateUserHandler $updateUserHandler,
        private ChangePasswordHandler $changePasswordHandler,
        private VerifyEmailHandler $verifyEmailHandler,
        private GetUserHandler $getUserHandler,
        private GetUsersHandler $getUsersHandler
    ) {}

    /**
     * Yeni kullanıcı kaydet
     */
    public function registerUser(
        string $name,
        string $email,
        string $password,
        ?string $phone = null,
        bool $isActive = true,
        array $roles = ['customer'],
        array $profile = []
    ): UserDTO {
        $command = new RegisterUserCommand(
            name: $name,
            email: $email,
            password: $password,
            phone: $phone,
            isActive: $isActive,
            roles: $roles,
            profile: $profile
        );

        return $this->registerUserHandler->handle($command);
    }

    /**
     * Kullanıcı bilgilerini güncelle
     */
    public function updateUser(
        int $userId,
        ?string $name = null,
        ?string $phone = null,
        ?array $profile = null,
        ?array $preferences = null
    ): UserDTO {
        $command = new UpdateUserCommand(
            userId: $userId,
            name: $name,
            phone: $phone,
            profile: $profile,
            preferences: $preferences
        );

        return $this->updateUserHandler->handle($command);
    }

    /**
     * Kullanıcı şifresini değiştir
     */
    public function changePassword(
        int $userId,
        string $currentPassword,
        string $newPassword
    ): UserDTO {
        $command = new ChangePasswordCommand(
            userId: $userId,
            currentPassword: $currentPassword,
            newPassword: $newPassword
        );

        return $this->changePasswordHandler->handle($command);
    }

    /**
     * Email adresini doğrula
     */
    public function verifyEmail(
        int $userId,
        string $verificationToken
    ): UserDTO {
        $command = new VerifyEmailCommand(
            userId: $userId,
            verificationToken: $verificationToken
        );

        return $this->verifyEmailHandler->handle($command);
    }

    /**
     * Kullanıcı detayını al
     */
    public function getUser(
        ?int $id = null,
        ?string $email = null,
        ?string $rememberToken = null,
        bool $includeRoles = false,
        bool $includePermissions = false,
        bool $includeAddresses = false,
        bool $includeProfile = false
    ): UserDTO {
        $query = new GetUserQuery(
            id: $id,
            email: $email,
            rememberToken: $rememberToken,
            includeRoles: $includeRoles,
            includePermissions: $includePermissions,
            includeAddresses: $includeAddresses,
            includeProfile: $includeProfile
        );

        return $this->getUserHandler->handle($query);
    }

    /**
     * Kullanıcıları listele
     */
    public function getUsers(
        ?bool $isActive = null,
        ?bool $isEmailVerified = null,
        ?string $role = null,
        ?string $search = null,
        ?string $sortBy = 'created_at',
        string $sortDirection = 'desc',
        int $limit = 50,
        int $offset = 0,
        bool $includeRoles = false,
        bool $includePermissions = false,
        bool $includeAddresses = false,
        bool $includeProfile = false
    ): array {
        $query = new GetUsersQuery(
            isActive: $isActive,
            isEmailVerified: $isEmailVerified,
            role: $role,
            search: $search,
            sortBy: $sortBy,
            sortDirection: $sortDirection,
            limit: $limit,
            offset: $offset,
            includeRoles: $includeRoles,
            includePermissions: $includePermissions,
            includeAddresses: $includeAddresses,
            includeProfile: $includeProfile
        );

        return $this->getUsersHandler->handle($query);
    }

    /**
     * Aktif kullanıcıları al
     */
    public function getActiveUsers(
        int $limit = 50,
        int $offset = 0,
        bool $includeRoles = false
    ): array {
        return $this->getUsers(
            isActive: true,
            limit: $limit,
            offset: $offset,
            includeRoles: $includeRoles
        );
    }

    /**
     * Email doğrulanmış kullanıcıları al
     */
    public function getVerifiedUsers(
        int $limit = 50,
        int $offset = 0,
        bool $includeRoles = false
    ): array {
        return $this->getUsers(
            isEmailVerified: true,
            limit: $limit,
            offset: $offset,
            includeRoles: $includeRoles
        );
    }

    /**
     * Role göre kullanıcıları al
     */
    public function getUsersByRole(
        string $role,
        int $limit = 50,
        int $offset = 0,
        bool $includePermissions = false
    ): array {
        return $this->getUsers(
            role: $role,
            limit: $limit,
            offset: $offset,
            includeRoles: true,
            includePermissions: $includePermissions
        );
    }

    /**
     * Kullanıcı arama
     */
    public function searchUsers(
        string $search,
        int $limit = 50,
        int $offset = 0,
        bool $includeRoles = false
    ): array {
        return $this->getUsers(
            search: $search,
            limit: $limit,
            offset: $offset,
            includeRoles: $includeRoles
        );
    }

    // ==================== AUTHENTICATION METHODS ====================

    /**
     * Email ve şifre ile kullanıcı doğrula
     */
    public function authenticateUser(string $email, string $password): ?UserDTO
    {
        try {
            $user = $this->getUser(email: $email);

            if ($user && $this->verifyUserPassword($user->id, $password)) {
                return $user;
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Kullanıcı şifresini doğrula
     */
    public function verifyUserPassword(int $userId, string $password): bool
    {
        try {
            $user = $this->getUserHandler->handle(new GetUserQuery(id: $userId));
            // Bu metod domain entity'de implement edilmeli
            // Şimdilik basit bir kontrol yapalım
            return password_verify($password, $user->password ?? '');
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Remember token oluştur
     */
    public function generateRememberToken(int $userId): string
    {
        $token = bin2hex(random_bytes(32));

        // Token'ı kullanıcıya kaydet
        // Bu işlem domain entity'de yapılmalı

        return $token;
    }

    /**
     * Email doğrulama token'ı oluştur
     */
    public function generateEmailVerificationToken(int $userId): string
    {
        $token = bin2hex(random_bytes(32));

        // Token'ı kullanıcıya kaydet ve email gönder
        // Bu işlem domain entity'de yapılmalı

        return $token;
    }

    /**
     * Şifre sıfırlama token'ı oluştur
     */
    public function generatePasswordResetToken(string $email): ?string
    {
        try {
            $user = $this->getUser(email: $email);

            if (!$user) {
                return null;
            }

            $token = bin2hex(random_bytes(32));

            // Token'ı kullanıcıya kaydet ve email gönder
            // Bu işlem domain entity'de yapılmalı

            return $token;
        } catch (\Exception $e) {
            return null;
        }
    }

    // ==================== AUTHORIZATION HELPERS ====================

    /**
     * Kullanıcının belirli bir role sahip olup olmadığını kontrol et
     */
    public function userHasRole(int $userId, string $roleName): bool
    {
        try {
            $user = $this->getUser(id: $userId, includeRoles: true);

            foreach ($user->roles ?? [] as $role) {
                if ($role['name'] === $roleName) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Kullanıcının belirli bir permission'a sahip olup olmadığını kontrol et
     */
    public function userHasPermission(int $userId, string $permissionName): bool
    {
        try {
            $user = $this->getUser(id: $userId, includePermissions: true);

            foreach ($user->permissions ?? [] as $permission) {
                if ($permission['name'] === $permissionName) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Kullanıcının admin olup olmadığını kontrol et
     */
    public function isAdmin(int $userId): bool
    {
        return $this->userHasRole($userId, 'admin');
    }

    /**
     * Kullanıcının customer olup olmadığını kontrol et
     */
    public function isCustomer(int $userId): bool
    {
        return $this->userHasRole($userId, 'customer');
    }

    /**
     * Kullanıcının aktif olup olmadığını kontrol et
     */
    public function isUserActive(int $userId): bool
    {
        try {
            $user = $this->getUser(id: $userId);
            return $user->is_active ?? false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Kullanıcının email'inin doğrulanmış olup olmadığını kontrol et
     */
    public function isEmailVerified(int $userId): bool
    {
        try {
            $user = $this->getUser(id: $userId);
            return !empty($user->email_verified_at);
        } catch (\Exception $e) {
            return false;
        }
    }
}
