<?php

namespace App\Application\Categories\Queries;

class GetCategoryQuery
{
    public function __construct(
        public readonly ?int $id = null,
        public readonly ?string $slug = null,
        public readonly bool $includeChildren = false,
        public readonly bool $includeAttributes = false,
        public readonly bool $includeProductCount = false,
        public readonly int $maxDepth = 3
    ) {}

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function shouldIncludeChildren(): bool
    {
        return $this->includeChildren;
    }

    public function shouldIncludeAttributes(): bool
    {
        return $this->includeAttributes;
    }

    public function shouldIncludeProductCount(): bool
    {
        return $this->includeProductCount;
    }

    public function getMaxDepth(): int
    {
        return $this->maxDepth;
    }

    public function hasIdentifier(): bool
    {
        return $this->id !== null || $this->slug !== null;
    }
}
