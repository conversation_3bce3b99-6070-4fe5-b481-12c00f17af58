<?php

namespace App\Application\Orders\Handlers;

use App\Application\Orders\Queries\GetOrderQuery;
use App\Application\Orders\DTOs\OrderDTO;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\Exceptions\OrderNotFoundException;
use Illuminate\Support\Facades\Log;

class GetOrderHandler
{
    public function __construct(
        private OrderRepositoryInterface $orderRepository
    ) {}

    public function handle(GetOrderQuery $query): OrderDTO
    {
        try {
            // Siparişi bul
            $order = $this->orderRepository->findById($query->getOrderId());
            
            if (!$order) {
                throw new OrderNotFoundException("Order with ID {$query->getOrderId()} not found");
            }

            // Kullanıcı kontrolü (eğer belirtilmişse)
            if ($query->getUserId() !== null && $order->getUserId() !== $query->getUserId()) {
                throw new OrderNotFoundException("Order not found for this user");
            }

            return OrderDTO::fromEntity($order);

        } catch (\Exception $e) {
            Log::error('Failed to get order', [
                'order_id' => $query->getOrderId(),
                'user_id' => $query->getUserId(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
