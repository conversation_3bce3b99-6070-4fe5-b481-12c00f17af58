<?php

namespace App\Events;

use App\Models\ProductVariant;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class VariantStockUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The variant instance.
     *
     * @var \App\Models\ProductVariant
     */
    public $variant;

    /**
     * The previous stock value.
     *
     * @var int
     */
    public $oldStock;

    /**
     * Create a new event instance.
     *
     * @param  \App\Models\ProductVariant  $variant
     * @param  int  $oldStock
     * @return void
     */
    public function __construct(ProductVariant $variant, int $oldStock)
    {
        $this->variant = $variant;
        $this->oldStock = $oldStock;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('variant-stock'),
        ];
    }
}
