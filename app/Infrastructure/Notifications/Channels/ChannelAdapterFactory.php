<?php

namespace App\Infrastructure\Notifications\Channels;

use App\Domain\Notifications\ValueObjects\NotificationChannel;
use Illuminate\Support\Facades\Log;

/**
 * ChannelAdapterFactory
 * Kanal adapter'larını oluşturan factory
 */
class ChannelAdapterFactory
{
    private array $adapters = [];
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = $config ?: config('notifications.channels', []);
        $this->registerDefaultAdapters();
    }

    /**
     * Kanal için adapter oluştur
     */
    public function create(NotificationChannel $channel): ?ChannelAdapterInterface
    {
        $channelName = $channel->getValue();

        try {
            // Cache'den kontrol et
            if (isset($this->adapters[$channelName])) {
                return $this->adapters[$channelName];
            }

            // Adapter class'ını bul
            $adapterClass = $this->getAdapterClass($channelName);
            
            if (!$adapterClass || !class_exists($adapterClass)) {
                Log::warning('Channel adapter class not found', [
                    'channel' => $channelName,
                    'adapter_class' => $adapterClass,
                ]);
                return null;
            }

            // Adapter'ı oluştur
            $adapter = new $adapterClass();
            
            if (!$adapter instanceof ChannelAdapterInterface) {
                Log::error('Invalid channel adapter', [
                    'channel' => $channelName,
                    'adapter_class' => $adapterClass,
                ]);
                return null;
            }

            // Konfigürasyonu set et
            $channelConfig = $this->getChannelConfig($channelName);
            $adapter->setConfiguration($channelConfig);

            // Cache'e ekle
            $this->adapters[$channelName] = $adapter;

            Log::debug('Channel adapter created', [
                'channel' => $channelName,
                'adapter_class' => $adapterClass,
            ]);

            return $adapter;

        } catch (\Exception $e) {
            Log::error('Failed to create channel adapter', [
                'channel' => $channelName,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Tüm desteklenen kanalları getir
     */
    public function getSupportedChannels(): array
    {
        return array_keys($this->getChannelMappings());
    }

    /**
     * Kanal destekleniyor mu kontrol et
     */
    public function isChannelSupported(string $channelName): bool
    {
        return in_array($channelName, $this->getSupportedChannels());
    }

    /**
     * Tüm adapter'ları getir
     */
    public function getAllAdapters(): array
    {
        $adapters = [];
        
        foreach ($this->getSupportedChannels() as $channelName) {
            $channel = NotificationChannel::fromString($channelName);
            $adapter = $this->create($channel);
            
            if ($adapter) {
                $adapters[$channelName] = $adapter;
            }
        }

        return $adapters;
    }

    /**
     * Sağlıklı adapter'ları getir
     */
    public function getHealthyAdapters(): array
    {
        $healthyAdapters = [];
        
        foreach ($this->getAllAdapters() as $channelName => $adapter) {
            $health = $adapter->healthCheck();
            
            if ($health->isHealthy()) {
                $healthyAdapters[$channelName] = $adapter;
            }
        }

        return $healthyAdapters;
    }

    /**
     * Adapter'ı manuel olarak kaydet
     */
    public function register(string $channelName, ChannelAdapterInterface $adapter): void
    {
        $this->adapters[$channelName] = $adapter;
        
        Log::info('Channel adapter registered', [
            'channel' => $channelName,
            'adapter_class' => get_class($adapter),
        ]);
    }

    /**
     * Adapter'ı kaldır
     */
    public function unregister(string $channelName): void
    {
        unset($this->adapters[$channelName]);
        
        Log::info('Channel adapter unregistered', [
            'channel' => $channelName,
        ]);
    }

    /**
     * Factory'yi temizle
     */
    public function clear(): void
    {
        $this->adapters = [];
        Log::info('Channel adapter factory cleared');
    }

    /**
     * Varsayılan adapter'ları kaydet
     */
    private function registerDefaultAdapters(): void
    {
        // Varsayılan adapter mapping'leri config'den alınır
        // Bu sayede yeni adapter'lar config ile eklenebilir
    }

    /**
     * Kanal için adapter class'ını getir
     */
    private function getAdapterClass(string $channelName): ?string
    {
        $mappings = $this->getChannelMappings();
        return $mappings[$channelName] ?? null;
    }

    /**
     * Kanal mapping'lerini getir
     */
    private function getChannelMappings(): array
    {
        return [
            'email' => EmailChannelAdapter::class,
            'sms' => SmsChannelAdapter::class,
            'push' => PushChannelAdapter::class,
            'in_app' => InAppChannelAdapter::class,
            'webhook' => WebhookChannelAdapter::class,
            'slack' => SlackChannelAdapter::class,
            'discord' => DiscordChannelAdapter::class,
            'telegram' => TelegramChannelAdapter::class,
            'whatsapp' => WhatsAppChannelAdapter::class,
        ];
    }

    /**
     * Kanal konfigürasyonunu getir
     */
    private function getChannelConfig(string $channelName): array
    {
        return $this->config[$channelName] ?? [];
    }

    /**
     * Tüm kanal konfigürasyonlarını getir
     */
    public function getAllConfigurations(): array
    {
        return $this->config;
    }

    /**
     * Kanal konfigürasyonunu güncelle
     */
    public function updateChannelConfig(string $channelName, array $config): void
    {
        $this->config[$channelName] = array_merge(
            $this->config[$channelName] ?? [],
            $config
        );

        // Mevcut adapter'ı güncelle
        if (isset($this->adapters[$channelName])) {
            $this->adapters[$channelName]->setConfiguration($this->config[$channelName]);
        }

        Log::info('Channel configuration updated', [
            'channel' => $channelName,
            'config' => $config,
        ]);
    }

    /**
     * Factory istatistiklerini getir
     */
    public function getStatistics(): array
    {
        $stats = [
            'total_channels' => count($this->getSupportedChannels()),
            'loaded_adapters' => count($this->adapters),
            'healthy_adapters' => 0,
            'unhealthy_adapters' => 0,
            'channel_stats' => [],
        ];

        foreach ($this->getAllAdapters() as $channelName => $adapter) {
            $health = $adapter->healthCheck();
            $adapterStats = $adapter->getStatistics();

            if ($health->isHealthy()) {
                $stats['healthy_adapters']++;
            } else {
                $stats['unhealthy_adapters']++;
            }

            $stats['channel_stats'][$channelName] = [
                'healthy' => $health->isHealthy(),
                'configured' => $adapter->isConfigured(),
                'supported' => $adapter->isSupported(),
                'stats' => $adapterStats,
            ];
        }

        return $stats;
    }
}
