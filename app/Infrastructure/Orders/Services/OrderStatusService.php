<?php

namespace App\Infrastructure\Orders\Services;

use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\Entities\Order;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Carbon\Carbon;

/**
 * Order Status Management Service
 * Sipariş durum yönetimi için infrastructure service
 */
class OrderStatusService
{
    private OrderRepositoryInterface $repository;

    public function __construct(OrderRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Sipariş durumunu güncelle
     */
    public function updateOrderStatus(
        int $orderId,
        OrderStatus $newStatus,
        ?string $note = null,
        ?int $userId = null,
        bool $notifyCustomer = true
    ): bool {
        try {
            DB::beginTransaction();

            $order = $this->repository->findById($orderId);
            if (!$order) {
                throw new \Exception("Order not found: {$orderId}");
            }

            $oldStatus = $order->getStatus();

            // Status değişikliği geçerli mi kontrol et
            if (!$this->canChangeStatus($order, $newStatus)) {
                throw new \Exception("Cannot change order status from {$oldStatus->value} to {$newStatus->value}");
            }

            // Status'u güncelle
            $order->changeStatus($newStatus, $note);
            $this->repository->save($order);

            // Status değişikliğine göre özel işlemler
            $this->handleStatusChange($order, $oldStatus, $newStatus, $userId, $notifyCustomer);

            DB::commit();

            Log::info('Order status updated successfully', [
                'order_id' => $orderId,
                'old_status' => $oldStatus->value,
                'new_status' => $newStatus->value,
                'user_id' => $userId,
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update order status', [
                'order_id' => $orderId,
                'new_status' => $newStatus->value,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Toplu durum güncelleme
     */
    public function bulkUpdateStatus(
        array $orderIds,
        OrderStatus $newStatus,
        ?string $note = null,
        ?int $userId = null
    ): array {
        $results = [
            'updated' => [],
            'failed' => [],
            'errors' => [],
        ];

        foreach ($orderIds as $orderId) {
            try {
                $this->updateOrderStatus($orderId, $newStatus, $note, $userId, false);
                $results['updated'][] = $orderId;
            } catch (\Exception $e) {
                $results['failed'][] = $orderId;
                $results['errors'][$orderId] = $e->getMessage();
            }
        }

        Log::info('Bulk order status update completed', [
            'total' => count($orderIds),
            'updated' => count($results['updated']),
            'failed' => count($results['failed']),
            'new_status' => $newStatus->value,
        ]);

        return $results;
    }

    /**
     * Otomatik durum güncellemeleri
     */
    public function processAutomaticStatusUpdates(): void
    {
        // Ödeme onayı bekleyen siparişleri kontrol et
        $this->processPaymentConfirmations();

        // Kargo için hazır siparişleri kontrol et
        $this->processReadyToShipOrders();

        // Teslimat süresi geçen siparişleri kontrol et
        $this->processOverdueDeliveries();

        // İptal edilmesi gereken siparişleri kontrol et
        $this->processAutoCancellations();
    }

    /**
     * Sipariş durumu geçmişini al
     */
    public function getStatusHistory(int $orderId): array
    {
        $order = $this->repository->findById($orderId);
        if (!$order) {
            return [];
        }

        // Order notes'larından status change'leri filtrele
        $statusHistory = [];
        foreach ($order->getNotes() as $note) {
            if ($note->getType() === 'status_change') {
                $statusHistory[] = [
                    'date' => $note->getCreatedAt(),
                    'status' => $note->getNote(),
                    'user_id' => $note->getUserId(),
                    'note' => $note->getNote(),
                ];
            }
        }

        return $statusHistory;
    }

    /**
     * Durum istatistiklerini al
     */
    public function getStatusStatistics(): array
    {
        $statistics = [];

        foreach (OrderStatus::cases() as $status) {
            $count = $this->repository->countByStatus($status);
            $statistics[$status->value] = [
                'count' => $count,
                'label' => $status->label(),
                'percentage' => 0, // Hesaplanacak
            ];
        }

        // Yüzdeleri hesapla
        $total = array_sum(array_column($statistics, 'count'));
        if ($total > 0) {
            foreach ($statistics as $status => &$data) {
                $data['percentage'] = round(($data['count'] / $total) * 100, 2);
            }
        }

        return $statistics;
    }

    /**
     * Status değişikliği yapılabilir mi kontrol et
     */
    private function canChangeStatus(Order $order, OrderStatus $newStatus): bool
    {
        $currentStatus = $order->getStatus();

        // Aynı status'a geçiş yapılamaz
        if ($currentStatus === $newStatus) {
            return false;
        }

        // Status transition rules (migration'daki enum'lara uygun)
        $allowedTransitions = [
            'pending' => [
                OrderStatus::PROCESSING,
                OrderStatus::CANCELLED,
            ],
            'processing' => [
                OrderStatus::SHIPPED,
                OrderStatus::CANCELLED,
            ],
            'shipped' => [
                OrderStatus::DELIVERED,
                OrderStatus::REFUNDED,
            ],
            'delivered' => [
                OrderStatus::REFUNDED,
            ],
            'cancelled' => [], // Final state
            'refunded' => [], // Final state
        ];

        return in_array($newStatus, $allowedTransitions[$currentStatus->value] ?? []);
    }

    /**
     * Status değişikliğine göre özel işlemler
     */
    private function handleStatusChange(
        Order $order,
        OrderStatus $oldStatus,
        OrderStatus $newStatus,
        ?int $userId,
        bool $notifyCustomer
    ): void {
        switch ($newStatus) {
            case OrderStatus::PAYMENT_CONFIRMED:
                $this->handlePaymentConfirmed($order, $notifyCustomer);
                break;

            case OrderStatus::PROCESSING:
                $this->handleProcessing($order, $notifyCustomer);
                break;

            case OrderStatus::SHIPPED:
                $this->handleShipped($order, $notifyCustomer);
                break;

            case OrderStatus::DELIVERED:
                $this->handleDelivered($order, $notifyCustomer);
                break;

            case OrderStatus::CANCELLED:
                $this->handleCancelled($order, $notifyCustomer);
                break;

            case OrderStatus::REFUNDED:
                $this->handleRefunded($order, $notifyCustomer);
                break;
        }
    }

    /**
     * Ödeme onaylandı işlemleri
     */
    private function handlePaymentConfirmed(Order $order, bool $notifyCustomer): void
    {
        // Stok rezervasyonu
        Queue::push('reserve-order-stock', [
            'order_id' => $order->getId(),
        ]);

        // Fatura oluştur
        Queue::push('generate-invoice', [
            'order_id' => $order->getId(),
        ]);

        if ($notifyCustomer) {
            Queue::push('send-payment-confirmed-notification', [
                'order_id' => $order->getId(),
            ]);
        }
    }

    /**
     * İşleme alındı işlemleri
     */
    private function handleProcessing(Order $order, bool $notifyCustomer): void
    {
        // Depo bildirimini gönder
        Queue::push('notify-warehouse', [
            'order_id' => $order->getId(),
            'action' => 'prepare_order',
        ]);

        if ($notifyCustomer) {
            Queue::push('send-processing-notification', [
                'order_id' => $order->getId(),
            ]);
        }
    }

    /**
     * Kargoya verildi işlemleri
     */
    private function handleShipped(Order $order, bool $notifyCustomer): void
    {
        // Tracking bilgilerini güncelle
        if ($order->getTrackingNumber()) {
            Queue::push('update-tracking-info', [
                'order_id' => $order->getId(),
                'tracking_number' => $order->getTrackingNumber(),
            ]);
        }

        if ($notifyCustomer) {
            Queue::push('send-shipped-notification', [
                'order_id' => $order->getId(),
                'tracking_number' => $order->getTrackingNumber(),
            ]);
        }
    }

    /**
     * Teslim edildi işlemleri
     */
    private function handleDelivered(Order $order, bool $notifyCustomer): void
    {
        // Müşteri memnuniyeti anketi gönder
        Queue::later(now()->addDays(1), 'send-satisfaction-survey', [
            'order_id' => $order->getId(),
        ]);

        if ($notifyCustomer) {
            Queue::push('send-delivered-notification', [
                'order_id' => $order->getId(),
            ]);
        }
    }

    /**
     * İptal edildi işlemleri
     */
    private function handleCancelled(Order $order, bool $notifyCustomer): void
    {
        // Stok iadesi
        Queue::push('release-order-stock', [
            'order_id' => $order->getId(),
        ]);

        // Ödeme iadesi (eğer ödeme yapılmışsa)
        if ($order->getPaymentStatus() === PaymentStatus::PAID) {
            Queue::push('process-refund', [
                'order_id' => $order->getId(),
                'reason' => 'order_cancelled',
            ]);
        }

        if ($notifyCustomer) {
            Queue::push('send-cancelled-notification', [
                'order_id' => $order->getId(),
            ]);
        }
    }

    /**
     * İade edildi işlemleri
     */
    private function handleRefunded(Order $order, bool $notifyCustomer): void
    {
        // İade işlemini tamamla
        Queue::push('complete-refund-process', [
            'order_id' => $order->getId(),
        ]);

        if ($notifyCustomer) {
            Queue::push('send-refunded-notification', [
                'order_id' => $order->getId(),
            ]);
        }
    }

    /**
     * Ödeme onayı bekleyen siparişleri işle
     */
    private function processPaymentConfirmations(): void
    {
        // Implementation for automatic payment confirmations
    }

    /**
     * Kargo için hazır siparişleri işle
     */
    private function processReadyToShipOrders(): void
    {
        // Implementation for ready to ship processing
    }

    /**
     * Teslimat süresi geçen siparişleri işle
     */
    private function processOverdueDeliveries(): void
    {
        // Implementation for overdue delivery processing
    }

    /**
     * Otomatik iptal edilecek siparişleri işle
     */
    private function processAutoCancellations(): void
    {
        // Implementation for auto-cancellation processing
    }
}
