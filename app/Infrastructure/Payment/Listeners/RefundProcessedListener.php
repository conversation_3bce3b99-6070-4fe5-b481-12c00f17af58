<?php

namespace App\Infrastructure\Payment\Listeners;

use App\Domain\Payment\Events\RefundProcessed;
use App\Infrastructure\Payment\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * RefundProcessedListener
 * İade işlemi tamamlandığında çalışan listener
 */
class RefundProcessedListener implements ShouldQueue
{
    use InteractsWithQueue;

    public $queue = 'payments';
    public $delay = 0;
    public $tries = 3;

    public function __construct(
        private NotificationService $notificationService
    ) {}

    /**
     * Event'i handle et
     */
    public function handle(RefundProcessed $event): void
    {
        $payment = $event->getPayment();
        $refundAmount = $event->getRefundAmount();

        try {
            Log::info('Refund processed event received', [
                'payment_id' => $payment->getId(),
                'transaction_id' => $payment->getTransactionId()->getValue(),
                'order_id' => $payment->getOrderId(),
                'original_amount' => $payment->getAmount()->getAmount(),
                'refund_amount' => $refundAmount,
                'gateway' => $payment->getGateway()->getProvider()
            ]);

            // İade bildirimi gönder
            $this->sendRefundNotifications($payment, $refundAmount);

            // Order durumunu güncelle
            $this->updateOrderStatus($payment, $refundAmount);

            // Inventory'yi güncelle (eğer gerekirse)
            $this->updateInventory($payment, $refundAmount);

            // Loyalty points'i düzelt
            $this->adjustLoyaltyPoints($payment, $refundAmount);

            // Cache güncelle
            $this->updateCache($payment, $refundAmount);

            // Metrics güncelle
            $this->updateMetrics($payment, $refundAmount);

            // Finance sistemine bildir
            $this->notifyFinanceSystem($payment, $refundAmount);

            // Shipping sürecini güncelle (eğer gerekirse)
            $this->updateShippingStatus($payment, $refundAmount);

        } catch (\Exception $e) {
            Log::error('Refund processed listener failed', [
                'payment_id' => $payment->getId(),
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Kritik hata değilse devam et
            if (!$this->isCriticalError($e)) {
                return;
            }

            throw $e;
        }
    }

    /**
     * İade bildirimleri gönder
     */
    private function sendRefundNotifications($payment, float $refundAmount): void
    {
        try {
            // Kullanıcıya iade bildirimi
            if ($payment->getUserId()) {
                $this->notificationService->sendRefundNotification($payment, $refundAmount);
            }

            // SMS bildirimi (eğer aktifse)
            if ($payment->getUserId() && config('payment.notifications.channels.sms.enabled')) {
                $message = "İade işleminiz tamamlandı. Sipariş No: {$payment->getOrderId()}, İade Tutarı: {$refundAmount} {$payment->getAmount()->getCurrency()}";
                $this->notificationService->sendSmsNotification($payment->getUserId(), $message);
            }

            // Push notification
            if ($payment->getUserId() && config('payment.notifications.channels.push.enabled')) {
                $this->notificationService->sendPushNotification(
                    $payment->getUserId(),
                    'İade İşlemi Tamamlandı',
                    "Sipariş #{$payment->getOrderId()} için {$refundAmount} {$payment->getAmount()->getCurrency()} iade edildi.",
                    [
                        'type' => 'refund_processed',
                        'payment_id' => $payment->getId(),
                        'order_id' => $payment->getOrderId(),
                        'refund_amount' => $refundAmount
                    ]
                );
            }

        } catch (\Exception $e) {
            Log::warning('Refund notification failed', [
                'payment_id' => $payment->getId(),
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Order durumunu güncelle
     */
    private function updateOrderStatus($payment, float $refundAmount): void
    {
        try {
            $order = DB::table('orders')->where('id', $payment->getOrderId())->first();
            
            if (!$order) {
                Log::warning('Order not found for refund', [
                    'order_id' => $payment->getOrderId(),
                    'payment_id' => $payment->getId()
                ]);
                return;
            }

            // Tam iade mi kısmi iade mi kontrol et
            $isFullRefund = $refundAmount >= $payment->getAmount()->getAmount();
            
            $updateData = [
                'refund_amount' => ($order->refund_amount ?? 0) + $refundAmount,
                'refunded_at' => now(),
                'updated_at' => now()
            ];

            if ($isFullRefund) {
                $updateData['status'] = 'refunded';
                $updateData['refund_status'] = 'full_refund';
            } else {
                $updateData['refund_status'] = 'partial_refund';
            }

            DB::table('orders')
                ->where('id', $payment->getOrderId())
                ->update($updateData);

            Log::info('Order refund status updated', [
                'order_id' => $payment->getOrderId(),
                'payment_id' => $payment->getId(),
                'refund_amount' => $refundAmount,
                'is_full_refund' => $isFullRefund
            ]);

        } catch (\Exception $e) {
            Log::error('Order refund status update failed', [
                'order_id' => $payment->getOrderId(),
                'payment_id' => $payment->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Inventory'yi güncelle (iade durumunda stok geri ekleme)
     */
    private function updateInventory($payment, float $refundAmount): void
    {
        try {
            // Tam iade ise stokları geri ekle
            $isFullRefund = $refundAmount >= $payment->getAmount()->getAmount();
            
            if (!$isFullRefund) {
                return; // Kısmi iade için stok işlemi yapmıyoruz
            }

            // Order items'ları al
            $orderItems = DB::table('order_items')
                ->where('order_id', $payment->getOrderId())
                ->get();

            foreach ($orderItems as $item) {
                // Product stock'ını artır
                DB::table('products')
                    ->where('id', $item->product_id)
                    ->increment('stock_quantity', $item->quantity);

                // Stock movement kaydı oluştur
                DB::table('stock_movements')->insert([
                    'product_id' => $item->product_id,
                    'type' => 'refund',
                    'quantity' => $item->quantity,
                    'reference_type' => 'refund',
                    'reference_id' => $payment->getId(),
                    'notes' => "Refund - Order #{$payment->getOrderId()}",
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }

            Log::info('Inventory updated for refund', [
                'payment_id' => $payment->getId(),
                'order_id' => $payment->getOrderId(),
                'items_count' => count($orderItems),
                'refund_amount' => $refundAmount
            ]);

        } catch (\Exception $e) {
            Log::error('Inventory refund update failed', [
                'payment_id' => $payment->getId(),
                'order_id' => $payment->getOrderId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Loyalty points'i düzelt
     */
    private function adjustLoyaltyPoints($payment, float $refundAmount): void
    {
        try {
            if (!$payment->getUserId()) {
                return;
            }

            // İade edilen tutara göre puan hesapla
            $pointsToDeduct = floor($refundAmount * 0.01);
            
            if ($pointsToDeduct > 0) {
                // User'ın loyalty points'ini azalt
                DB::table('users')
                    ->where('id', $payment->getUserId())
                    ->where('loyalty_points', '>=', $pointsToDeduct)
                    ->decrement('loyalty_points', $pointsToDeduct);

                // Loyalty points transaction kaydı
                DB::table('loyalty_transactions')->insert([
                    'user_id' => $payment->getUserId(),
                    'type' => 'deducted',
                    'points' => -$pointsToDeduct,
                    'reference_type' => 'refund',
                    'reference_id' => $payment->getId(),
                    'description' => "Points deducted for refund #{$payment->getId()}",
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                Log::info('Loyalty points adjusted for refund', [
                    'user_id' => $payment->getUserId(),
                    'payment_id' => $payment->getId(),
                    'points_deducted' => $pointsToDeduct,
                    'refund_amount' => $refundAmount
                ]);
            }

        } catch (\Exception $e) {
            Log::warning('Loyalty points adjustment failed', [
                'payment_id' => $payment->getId(),
                'user_id' => $payment->getUserId(),
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Cache güncelle
     */
    private function updateCache($payment, float $refundAmount): void
    {
        try {
            $cachePrefix = config('payment.cache.prefix', 'payment:');
            $cacheTtl = config('payment.cache.ttl', 3600);

            // User refund stats cache'ini güncelle
            if ($payment->getUserId()) {
                $userRefundKey = $cachePrefix . 'user_refunds:' . $payment->getUserId();
                Cache::forget($userRefundKey); // Yeniden hesaplanması için sil
            }

            // Daily refund stats cache'ini güncelle
            $dailyRefundKey = $cachePrefix . 'daily_refunds:' . date('Y-m-d');
            $dailyRefundStats = Cache::get($dailyRefundKey, [
                'count' => 0,
                'total_amount' => 0,
                'gateways' => []
            ]);

            $dailyRefundStats['count']++;
            $dailyRefundStats['total_amount'] += $refundAmount;
            $dailyRefundStats['gateways'][$payment->getGateway()->getProvider()] = 
                ($dailyRefundStats['gateways'][$payment->getGateway()->getProvider()] ?? 0) + 1;

            Cache::put($dailyRefundKey, $dailyRefundStats, $cacheTtl);

            // Gateway refund stats cache'ini güncelle
            $gatewayRefundKey = $cachePrefix . 'gateway_refunds:' . $payment->getGateway()->getProvider();
            Cache::forget($gatewayRefundKey); // Yeniden hesaplanması için sil

        } catch (\Exception $e) {
            Log::warning('Refund cache update failed', [
                'payment_id' => $payment->getId(),
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Metrics güncelle
     */
    private function updateMetrics($payment, float $refundAmount): void
    {
        try {
            // Refund metrics
            Log::info('Refund metrics', [
                'metric' => 'refund_processed',
                'payment_id' => $payment->getId(),
                'original_amount' => $payment->getAmount()->getAmount(),
                'refund_amount' => $refundAmount,
                'currency' => $payment->getAmount()->getCurrency(),
                'gateway' => $payment->getGateway()->getProvider(),
                'refund_percentage' => ($refundAmount / $payment->getAmount()->getAmount()) * 100,
                'is_full_refund' => $refundAmount >= $payment->getAmount()->getAmount(),
                'timestamp' => now()->toISOString()
            ]);

            // Revenue impact metrics
            Log::info('Revenue impact metrics', [
                'metric' => 'revenue_refunded',
                'amount' => $refundAmount,
                'currency' => $payment->getAmount()->getCurrency(),
                'gateway' => $payment->getGateway()->getProvider(),
                'original_payment_id' => $payment->getId(),
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::warning('Refund metrics update failed', [
                'payment_id' => $payment->getId(),
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Finance sistemine bildir
     */
    private function notifyFinanceSystem($payment, float $refundAmount): void
    {
        try {
            // Finance/Accounting sistemine iade bilgilerini gönder
            $financeData = [
                'type' => 'refund',
                'payment_id' => $payment->getId(),
                'order_id' => $payment->getOrderId(),
                'original_amount' => $payment->getAmount()->getAmount(),
                'refund_amount' => $refundAmount,
                'currency' => $payment->getAmount()->getCurrency(),
                'gateway' => $payment->getGateway()->getProvider(),
                'transaction_id' => $payment->getTransactionId()->getValue(),
                'gateway_transaction_id' => $payment->getGatewayTransactionId(),
                'refunded_at' => now()->toISOString(),
                'user_id' => $payment->getUserId(),
                'is_full_refund' => $refundAmount >= $payment->getAmount()->getAmount()
            ];

            // Queue'ya ekle (external API call için)
            \Queue::push('finance-refund-notification', $financeData);

            Log::info('Finance system refund notification queued', [
                'payment_id' => $payment->getId(),
                'refund_amount' => $refundAmount
            ]);

        } catch (\Exception $e) {
            Log::warning('Finance system refund notification failed', [
                'payment_id' => $payment->getId(),
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Shipping durumunu güncelle
     */
    private function updateShippingStatus($payment, float $refundAmount): void
    {
        try {
            // Tam iade ise shipping'i iptal et
            $isFullRefund = $refundAmount >= $payment->getAmount()->getAmount();
            
            if (!$isFullRefund) {
                return; // Kısmi iade için shipping işlemi yapmıyoruz
            }

            // Shipping'i iptal et
            \Queue::push('shipping-cancel', [
                'order_id' => $payment->getOrderId(),
                'payment_id' => $payment->getId(),
                'reason' => 'full_refund',
                'cancelled_at' => now()->toISOString()
            ]);

            Log::info('Shipping cancellation queued for refund', [
                'order_id' => $payment->getOrderId(),
                'payment_id' => $payment->getId(),
                'refund_amount' => $refundAmount
            ]);

        } catch (\Exception $e) {
            Log::warning('Shipping cancellation failed', [
                'payment_id' => $payment->getId(),
                'order_id' => $payment->getOrderId(),
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Kritik hata mı kontrol et
     */
    private function isCriticalError(\Exception $e): bool
    {
        // Database bağlantı hataları kritik
        if (str_contains($e->getMessage(), 'database') || 
            str_contains($e->getMessage(), 'connection')) {
            return true;
        }

        // Memory hataları kritik
        if (str_contains($e->getMessage(), 'memory') || 
            str_contains($e->getMessage(), 'Fatal error')) {
            return true;
        }

        return false;
    }

    /**
     * Job failed
     */
    public function failed(RefundProcessed $event, \Throwable $exception): void
    {
        Log::error('Refund processed listener job failed', [
            'payment_id' => $event->getPayment()->getId(),
            'refund_amount' => $event->getRefundAmount(),
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

        // Admin'e kritik hata bildirimi gönder
        try {
            $this->notificationService->sendAdminAlert(
                'Refund Processed Listener Failed',
                "Refund processed listener failed for payment ID: {$event->getPayment()->getId()}",
                [
                    'payment_id' => $event->getPayment()->getId(),
                    'refund_amount' => $event->getRefundAmount(),
                    'error' => $exception->getMessage(),
                    'timestamp' => now()->toISOString()
                ]
            );
        } catch (\Exception $e) {
            Log::error('Failed to send admin alert for refund listener failure', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
