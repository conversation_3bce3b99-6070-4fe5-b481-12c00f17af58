<?php

namespace App\Infrastructure\Payment;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Infrastructure\Payment\Repositories\EloquentPaymentRepository;
use App\Infrastructure\Payment\Repositories\CachePaymentRepository;
use App\Infrastructure\Payment\Gateways\Contracts\PaymentGatewayInterface;
use App\Infrastructure\Payment\Gateways\IyzicoGatewayAdapter;
use App\Infrastructure\Payment\Gateways\StripeGatewayAdapter;
use App\Infrastructure\Payment\Gateways\PayPalGatewayAdapter;
use App\Infrastructure\Payment\Gateways\BankTransferGatewayAdapter;
use App\Infrastructure\Payment\Services\FraudDetectionService;
use App\Infrastructure\Payment\Services\NotificationService;
use App\Infrastructure\Payment\Services\WebhookService;
use App\Infrastructure\Payment\Services\PaymentSecurityService;
use App\Infrastructure\Payment\Services\PaymentWebhookHandler;
use App\Infrastructure\Payment\Repositories\PaymentTransactionRepository;
use App\Infrastructure\Payment\Listeners\PaymentCreatedListener;
use App\Infrastructure\Payment\Listeners\PaymentProcessedListener;
use App\Infrastructure\Payment\Listeners\PaymentFailedListener;
use App\Infrastructure\Payment\Listeners\RefundProcessedListener;
use App\Infrastructure\Payment\Listeners\PaymentEventListener;
use App\Domain\Payment\Events\PaymentCreated;
use App\Domain\Payment\Events\PaymentProcessed;
use App\Domain\Payment\Events\PaymentFailed;
use App\Domain\Payment\Events\RefundProcessed;
use App\Domain\Payment\Events\PaymentInitiated;
use App\Domain\Payment\Events\PaymentCompleted;
use App\Domain\Payment\Events\PaymentCancelled;
use App\Domain\Payment\Events\RefundInitiated;
use App\Domain\Payment\Events\RefundCompleted;

/**
 * PaymentServiceProvider
 * Payment modülü için service provider
 */
class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register services
     */
    public function register(): void
    {
        $this->registerRepositories();
        $this->registerGateways();
        $this->registerServices();
        $this->registerListeners();
        $this->registerCommands();
    }

    /**
     * Bootstrap services
     */
    public function boot(): void
    {
        $this->registerEventListeners();
        $this->publishConfiguration();
        $this->loadMigrations();
    }

    /**
     * Repository'leri kaydet
     */
    private function registerRepositories(): void
    {
        // Base repository
        $this->app->bind(
            'payment.repository.eloquent',
            EloquentPaymentRepository::class
        );

        // Cache decorator
        $this->app->bind(PaymentRepositoryInterface::class, function ($app) {
            $eloquentRepository = $app->make('payment.repository.eloquent');

            // Cache kullanılacaksa decorator ile sar
            if (config('cache_infrastructure.entities.payment.enabled', true)) {
                return new CachePaymentRepository(
                    $eloquentRepository,
                    $app->make('cache.key_generator'),
                    $app->make('cache.tag_manager'),
                    config('cache_infrastructure.entities.payment', [])
                );
            }

            return $eloquentRepository;
        });

        // Payment Transaction Repository
        $this->app->singleton(PaymentTransactionRepository::class);
    }

    /**
     * Gateway'leri kaydet
     */
    private function registerGateways(): void
    {
        // İyzico Gateway
        $this->app->bind('payment.gateway.iyzico', IyzicoGatewayAdapter::class);

        // Stripe Gateway
        $this->app->bind('payment.gateway.stripe', StripeGatewayAdapter::class);

        // PayPal Gateway
        $this->app->bind('payment.gateway.paypal', PayPalGatewayAdapter::class);

        // Bank Transfer Gateway
        $this->app->bind('payment.gateway.bank_transfer', BankTransferGatewayAdapter::class);

        // Gateway Factory
        $this->app->bind('payment.gateway.factory', function ($app) {
            return new PaymentGatewayFactory([
                'iyzico' => fn() => $app->make('payment.gateway.iyzico'),
                'stripe' => fn() => $app->make('payment.gateway.stripe'),
                'paypal' => fn() => $app->make('payment.gateway.paypal'),
                'bank_transfer' => fn() => $app->make('payment.gateway.bank_transfer'),
            ]);
        });
    }

    /**
     * Service'leri kaydet
     */
    private function registerServices(): void
    {
        // Fraud Detection Service (Legacy)
        $this->app->singleton(FraudDetectionService::class, function ($app) {
            return new FraudDetectionService(
                config('payment.fraud_detection', [])
            );
        });

        // Payment Security Service (New)
        $this->app->singleton(PaymentSecurityService::class);

        // Notification Service
        $this->app->singleton(NotificationService::class, function ($app) {
            return new NotificationService(
                config('payment.notifications', [])
            );
        });

        // Webhook Service (Legacy)
        $this->app->singleton(WebhookService::class, function ($app) {
            return new WebhookService(
                $app->make('payment.gateway.factory'),
                config('payment.webhooks', [])
            );
        });

        // Payment Webhook Handler (New)
        $this->app->singleton(PaymentWebhookHandler::class, function ($app) {
            return new PaymentWebhookHandler(
                $app->make(PaymentRepositoryInterface::class),
                $app->make(PaymentTransactionRepository::class)
            );
        });
    }

    /**
     * Event Listener'ları kaydet
     */
    private function registerListeners(): void
    {
        // Legacy Listeners
        $this->app->singleton(PaymentCreatedListener::class);
        $this->app->singleton(PaymentProcessedListener::class);
        $this->app->singleton(PaymentFailedListener::class);
        $this->app->singleton(RefundProcessedListener::class);

        // New Payment Event Listener
        $this->app->singleton(PaymentEventListener::class, function ($app) {
            return new PaymentEventListener(
                $app->make(PaymentTransactionRepository::class),
                $app->make(PaymentSecurityService::class)
            );
        });
    }

    /**
     * Command'ları kaydet
     */
    private function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                // Payment komutları buraya eklenecek
            ]);
        }
    }

    /**
     * Event listener'ları kaydet
     */
    private function registerEventListeners(): void
    {
        // Legacy Event listeners
        Event::listen(PaymentCreated::class, PaymentCreatedListener::class);
        Event::listen(PaymentProcessed::class, PaymentProcessedListener::class);
        Event::listen(PaymentFailed::class, PaymentFailedListener::class);
        Event::listen(RefundProcessed::class, RefundProcessedListener::class);

        // New Payment Event listeners
        Event::listen(PaymentInitiated::class, [PaymentEventListener::class, 'handlePaymentInitiated']);
        Event::listen(PaymentCompleted::class, [PaymentEventListener::class, 'handlePaymentCompleted']);
        Event::listen(PaymentFailed::class, [PaymentEventListener::class, 'handlePaymentFailed']);
        Event::listen(PaymentCancelled::class, [PaymentEventListener::class, 'handlePaymentCancelled']);
        Event::listen(RefundInitiated::class, [PaymentEventListener::class, 'handleRefundInitiated']);
        Event::listen(RefundCompleted::class, [PaymentEventListener::class, 'handleRefundCompleted']);
    }

    /**
     * Konfigürasyon dosyalarını publish et
     */
    private function publishConfiguration(): void
    {
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/../../config/payment.php' => config_path('payment.php'),
            ], 'payment-config');
        }
    }

    /**
     * Migration'ları yükle
     */
    private function loadMigrations(): void
    {
        if ($this->app->runningInConsole()) {
            $this->loadMigrationsFrom(__DIR__ . '/../../database/migrations');
        }
    }

    /**
     * Provides
     */
    public function provides(): array
    {
        return [
            PaymentRepositoryInterface::class,
            'payment.repository.eloquent',
            'payment.gateway.iyzico',
            'payment.gateway.stripe',
            'payment.gateway.paypal',
            'payment.gateway.bank_transfer',
            'payment.gateway.factory',
            FraudDetectionService::class,
            NotificationService::class,
            WebhookService::class,
            PaymentSecurityService::class,
            PaymentWebhookHandler::class,
            PaymentTransactionRepository::class,
            PaymentEventListener::class,
        ];
    }
}

/**
 * PaymentGatewayFactory
 * Payment gateway factory
 */
class PaymentGatewayFactory
{
    public function __construct(
        private array $gateways
    ) {}

    /**
     * Gateway oluştur
     */
    public function create(string $gateway): PaymentGatewayInterface
    {
        if (!isset($this->gateways[$gateway])) {
            throw new \InvalidArgumentException("Unsupported payment gateway: {$gateway}");
        }

        return $this->gateways[$gateway]();
    }

    /**
     * Desteklenen gateway'leri getir
     */
    public function getSupportedGateways(): array
    {
        return array_keys($this->gateways);
    }

    /**
     * Gateway destekleniyor mu kontrol et
     */
    public function supports(string $gateway): bool
    {
        return isset($this->gateways[$gateway]);
    }
}
