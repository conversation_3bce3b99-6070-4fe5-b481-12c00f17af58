<?php

namespace App\Infrastructure\Shipping\Services;

use App\Infrastructure\Shipping\Models\EloquentCarrierIntegration;
use App\Infrastructure\Shipping\Adapters\CarrierAdapterInterface;
use App\Infrastructure\Shipping\Adapters\CarrierAdapterFactory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * CarrierIntegrationService
 * Kargo şirketi entegrasyon yönetimi servisi
 */
class CarrierIntegrationService
{
    public function __construct(
        private CarrierAdapterFactory $adapterFactory
    ) {}

    /**
     * Kargo ücret hesaplama
     */
    public function calculateShippingRate(
        string $carrierCode,
        array $originAddress,
        array $destinationAddress,
        array $packageInfo,
        string $serviceType = 'standard',
        ?float $declaredValue = null
    ): array {
        try {
            $carrier = EloquentCarrierIntegration::where('carrier_code', $carrierCode)
                ->where('is_active', true)
                ->first();

            if (!$carrier) {
                throw new \InvalidArgumentException("Carrier not found or inactive: {$carrierCode}");
            }

            if (!$carrier->supports_rate_calculation) {
                throw new \InvalidArgumentException("Carrier does not support rate calculation: {$carrierCode}");
            }

            // Rate calculation ID oluştur
            $calculationId = 'CALC-' . date('Y') . '-' . uniqid();

            // Adapter ile rate hesapla
            $adapter = $this->adapterFactory->create($carrierCode);
            $rateData = $adapter->calculateRate(
                $originAddress,
                $destinationAddress,
                $packageInfo,
                $serviceType,
                $declaredValue
            );

            // Rate calculation kaydını oluştur
            $calculationDbId = DB::table('shipping_rate_calculations')->insertGetId([
                'calculation_id' => $calculationId,
                'carrier_integration_id' => $carrier->id,
                'origin_address' => json_encode($originAddress),
                'destination_address' => json_encode($destinationAddress),
                'package_info' => json_encode($packageInfo),
                'service_type' => $serviceType,
                'declared_value' => $declaredValue,
                'base_cost' => $rateData['base_cost'] ?? 0,
                'fuel_surcharge' => $rateData['fuel_surcharge'] ?? 0,
                'insurance_cost' => $rateData['insurance_cost'] ?? 0,
                'handling_cost' => $rateData['handling_cost'] ?? 0,
                'taxes' => $rateData['taxes'] ?? 0,
                'total_cost' => $rateData['total_cost'] ?? 0,
                'currency' => $rateData['currency'] ?? 'TRY',
                'estimated_days' => $rateData['estimated_days'] ?? null,
                'estimated_delivery_date' => $rateData['estimated_delivery_date'] ?? null,
                'delivery_commitment' => $rateData['delivery_commitment'] ?? 'estimated',
                'calculation_method' => 'api',
                'rate_breakdown' => json_encode($rateData['breakdown'] ?? []),
                'carrier_response' => json_encode($rateData['raw_response'] ?? []),
                'calculated_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addHours(24), // 24 saat geçerli
                'is_valid' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            Log::info('Shipping rate calculated', [
                'calculation_id' => $calculationId,
                'carrier_code' => $carrierCode,
                'service_type' => $serviceType,
                'total_cost' => $rateData['total_cost'],
                'estimated_days' => $rateData['estimated_days']
            ]);

            return [
                'calculation_id' => $calculationId,
                'calculation_db_id' => $calculationDbId,
                'carrier_code' => $carrierCode,
                'carrier_name' => $carrier->carrier_name,
                'service_type' => $serviceType,
                'total_cost' => $rateData['total_cost'],
                'currency' => $rateData['currency'] ?? 'TRY',
                'cost_breakdown' => [
                    'base_cost' => $rateData['base_cost'] ?? 0,
                    'fuel_surcharge' => $rateData['fuel_surcharge'] ?? 0,
                    'insurance_cost' => $rateData['insurance_cost'] ?? 0,
                    'handling_cost' => $rateData['handling_cost'] ?? 0,
                    'taxes' => $rateData['taxes'] ?? 0
                ],
                'delivery_estimate' => [
                    'estimated_days' => $rateData['estimated_days'],
                    'estimated_delivery_date' => $rateData['estimated_delivery_date'],
                    'delivery_commitment' => $rateData['delivery_commitment'] ?? 'estimated'
                ],
                'expires_at' => Carbon::now()->addHours(24)->toISOString(),
                'calculated_at' => Carbon::now()->toISOString()
            ];
        } catch (\Exception $e) {
            Log::error('Failed to calculate shipping rate', [
                'carrier_code' => $carrierCode,
                'service_type' => $serviceType,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Birden fazla carrier için rate karşılaştırması
     */
    public function compareRates(
        array $carrierCodes,
        array $originAddress,
        array $destinationAddress,
        array $packageInfo,
        string $serviceType = 'standard',
        ?float $declaredValue = null
    ): array {
        $results = [
            'comparison_id' => 'COMP-' . date('Y') . '-' . uniqid(),
            'request_details' => [
                'origin_address' => $originAddress,
                'destination_address' => $destinationAddress,
                'package_info' => $packageInfo,
                'service_type' => $serviceType,
                'declared_value' => $declaredValue
            ],
            'rates' => [],
            'errors' => [],
            'best_rate' => null,
            'fastest_delivery' => null,
            'compared_at' => Carbon::now()->toISOString()
        ];

        foreach ($carrierCodes as $carrierCode) {
            try {
                $rate = $this->calculateShippingRate(
                    $carrierCode,
                    $originAddress,
                    $destinationAddress,
                    $packageInfo,
                    $serviceType,
                    $declaredValue
                );
                
                $results['rates'][] = $rate;
            } catch (\Exception $e) {
                $results['errors'][] = [
                    'carrier_code' => $carrierCode,
                    'error' => $e->getMessage()
                ];
            }
        }

        // En iyi fiyat ve en hızlı teslimatı bul
        if (!empty($results['rates'])) {
            // En düşük fiyat
            $bestRate = collect($results['rates'])->sortBy('total_cost')->first();
            $results['best_rate'] = $bestRate;

            // En hızlı teslimat
            $fastestDelivery = collect($results['rates'])
                ->filter(fn($rate) => !empty($rate['delivery_estimate']['estimated_days']))
                ->sortBy('delivery_estimate.estimated_days')
                ->first();
            $results['fastest_delivery'] = $fastestDelivery;
        }

        Log::info('Rate comparison completed', [
            'comparison_id' => $results['comparison_id'],
            'carriers_requested' => count($carrierCodes),
            'rates_received' => count($results['rates']),
            'errors' => count($results['errors'])
        ]);

        return $results;
    }

    /**
     * Shipment oluştur
     */
    public function createShipment(
        string $carrierCode,
        int $orderId,
        array $originAddress,
        array $destinationAddress,
        array $packageInfo,
        string $serviceType,
        array $options = []
    ): array {
        try {
            $carrier = EloquentCarrierIntegration::where('carrier_code', $carrierCode)
                ->where('is_active', true)
                ->first();

            if (!$carrier) {
                throw new \InvalidArgumentException("Carrier not found or inactive: {$carrierCode}");
            }

            // Adapter ile shipment oluştur
            $adapter = $this->adapterFactory->create($carrierCode);
            $shipmentData = $adapter->createShipment(
                $originAddress,
                $destinationAddress,
                $packageInfo,
                $serviceType,
                $options
            );

            // Shipment number oluştur
            $shipmentNumber = 'SHP-' . date('Y') . '-' . str_pad(
                DB::table('shipments')->whereYear('created_at', date('Y'))->count() + 1,
                6,
                '0',
                STR_PAD_LEFT
            );

            // Shipment kaydını oluştur
            $shipmentId = DB::table('shipments')->insertGetId([
                'shipment_number' => $shipmentNumber,
                'order_id' => $orderId,
                'carrier_integration_id' => $carrier->id,
                'tracking_number' => $shipmentData['tracking_number'] ?? null,
                'carrier_tracking_number' => $shipmentData['carrier_tracking_number'] ?? null,
                'reference_number' => $options['reference_number'] ?? null,
                'origin_address' => json_encode($originAddress),
                'destination_address' => json_encode($destinationAddress),
                'return_address' => isset($options['return_address']) ? json_encode($options['return_address']) : null,
                'package_info' => json_encode($packageInfo),
                'items' => isset($options['items']) ? json_encode($options['items']) : null,
                'declared_value' => $options['declared_value'] ?? null,
                'declared_currency' => $options['declared_currency'] ?? 'TRY',
                'service_type' => $serviceType,
                'delivery_type' => $options['delivery_type'] ?? 'door_to_door',
                'signature_required' => $options['signature_required'] ?? false,
                'insurance_required' => $options['insurance_required'] ?? false,
                'insurance_amount' => $options['insurance_amount'] ?? null,
                'status' => 'pending',
                'estimated_delivery_at' => $shipmentData['estimated_delivery_at'] ?? null,
                'shipping_cost' => $shipmentData['shipping_cost'] ?? 0,
                'insurance_cost' => $shipmentData['insurance_cost'] ?? 0,
                'handling_cost' => $shipmentData['handling_cost'] ?? 0,
                'fuel_surcharge' => $shipmentData['fuel_surcharge'] ?? 0,
                'taxes' => $shipmentData['taxes'] ?? 0,
                'total_cost' => $shipmentData['total_cost'] ?? 0,
                'cost_currency' => $shipmentData['currency'] ?? 'TRY',
                'label_format' => $shipmentData['label_format'] ?? null,
                'label_url' => $shipmentData['label_url'] ?? null,
                'label_data' => isset($shipmentData['label_data']) ? json_encode($shipmentData['label_data']) : null,
                'carrier_response' => json_encode($shipmentData['raw_response'] ?? []),
                'delivery_attempts' => 0,
                'return_to_sender' => false,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            // Label kaydını oluştur (varsa)
            if (!empty($shipmentData['label_data'])) {
                DB::table('shipping_labels')->insert([
                    'shipment_id' => $shipmentId,
                    'label_type' => 'shipping',
                    'label_format' => $shipmentData['label_format'] ?? 'pdf',
                    'label_size' => $options['label_size'] ?? '4x6',
                    'label_url' => $shipmentData['label_url'] ?? null,
                    'label_data' => is_array($shipmentData['label_data']) 
                        ? json_encode($shipmentData['label_data']) 
                        : $shipmentData['label_data'],
                    'generated_by' => 'carrier',
                    'generated_at' => Carbon::now(),
                    'is_valid' => true,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);
            }

            Log::info('Shipment created', [
                'shipment_id' => $shipmentId,
                'shipment_number' => $shipmentNumber,
                'order_id' => $orderId,
                'carrier_code' => $carrierCode,
                'tracking_number' => $shipmentData['tracking_number'] ?? null,
                'total_cost' => $shipmentData['total_cost'] ?? 0
            ]);

            return [
                'shipment_id' => $shipmentId,
                'shipment_number' => $shipmentNumber,
                'order_id' => $orderId,
                'carrier_code' => $carrierCode,
                'carrier_name' => $carrier->carrier_name,
                'tracking_number' => $shipmentData['tracking_number'] ?? null,
                'carrier_tracking_number' => $shipmentData['carrier_tracking_number'] ?? null,
                'service_type' => $serviceType,
                'status' => 'pending',
                'estimated_delivery_at' => $shipmentData['estimated_delivery_at'],
                'total_cost' => $shipmentData['total_cost'] ?? 0,
                'currency' => $shipmentData['currency'] ?? 'TRY',
                'label_url' => $shipmentData['label_url'] ?? null,
                'has_label' => !empty($shipmentData['label_data']),
                'created_at' => Carbon::now()->toISOString()
            ];
        } catch (\Exception $e) {
            Log::error('Failed to create shipment', [
                'carrier_code' => $carrierCode,
                'order_id' => $orderId,
                'service_type' => $serviceType,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Shipment iptal et
     */
    public function cancelShipment(int $shipmentId, string $reason = 'customer_request'): array
    {
        try {
            return DB::transaction(function () use ($shipmentId, $reason) {
                $shipment = DB::table('shipments')
                    ->join('carrier_integrations', 'shipments.carrier_integration_id', '=', 'carrier_integrations.id')
                    ->where('shipments.id', $shipmentId)
                    ->select('shipments.*', 'carrier_integrations.carrier_code')
                    ->first();

                if (!$shipment) {
                    throw new \InvalidArgumentException("Shipment not found: {$shipmentId}");
                }

                if (in_array($shipment->status, ['delivered', 'cancelled'])) {
                    throw new \InvalidArgumentException("Cannot cancel shipment with status: {$shipment->status}");
                }

                // Carrier adapter ile iptal et
                if ($shipment->tracking_number) {
                    try {
                        $adapter = $this->adapterFactory->create($shipment->carrier_code);
                        $cancelResult = $adapter->cancelShipment($shipment->tracking_number, $reason);
                    } catch (\Exception $e) {
                        Log::warning('Carrier cancellation failed, proceeding with local cancellation', [
                            'shipment_id' => $shipmentId,
                            'tracking_number' => $shipment->tracking_number,
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                // Local shipment'ı iptal et
                DB::table('shipments')
                    ->where('id', $shipmentId)
                    ->update([
                        'status' => 'cancelled',
                        'failure_reason' => $reason,
                        'updated_at' => Carbon::now()
                    ]);

                Log::info('Shipment cancelled', [
                    'shipment_id' => $shipmentId,
                    'shipment_number' => $shipment->shipment_number,
                    'reason' => $reason
                ]);

                return [
                    'shipment_id' => $shipmentId,
                    'shipment_number' => $shipment->shipment_number,
                    'status' => 'cancelled',
                    'reason' => $reason,
                    'cancelled_at' => Carbon::now()->toISOString()
                ];
            });
        } catch (\Exception $e) {
            Log::error('Failed to cancel shipment', [
                'shipment_id' => $shipmentId,
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Aktif carrier'ları getir
     */
    public function getActiveCarriers(): array
    {
        try {
            $carriers = EloquentCarrierIntegration::where('is_active', true)
                ->orderBy('priority', 'desc')
                ->orderBy('carrier_name')
                ->get();

            return $carriers->map(function ($carrier) {
                return [
                    'id' => $carrier->id,
                    'carrier_code' => $carrier->carrier_code,
                    'carrier_name' => $carrier->carrier_name,
                    'integration_type' => $carrier->integration_type,
                    'is_test_mode' => $carrier->is_test_mode,
                    'priority' => $carrier->priority,
                    'capabilities' => [
                        'tracking' => $carrier->supports_tracking,
                        'label_generation' => $carrier->supports_label_generation,
                        'rate_calculation' => $carrier->supports_rate_calculation,
                        'pickup_scheduling' => $carrier->supports_pickup_scheduling,
                        'delivery_confirmation' => $carrier->supports_delivery_confirmation
                    ],
                    'supported_countries' => $carrier->supported_countries,
                    'supported_services' => $carrier->supported_services
                ];
            })->toArray();
        } catch (\Exception $e) {
            Log::error('Failed to get active carriers', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Carrier performansını test et
     */
    public function testCarrierConnection(string $carrierCode): array
    {
        try {
            $carrier = EloquentCarrierIntegration::where('carrier_code', $carrierCode)->first();
            if (!$carrier) {
                throw new \InvalidArgumentException("Carrier not found: {$carrierCode}");
            }

            $adapter = $this->adapterFactory->create($carrierCode);
            $testResult = $adapter->testConnection();

            Log::info('Carrier connection tested', [
                'carrier_code' => $carrierCode,
                'test_result' => $testResult['success'],
                'response_time' => $testResult['response_time_ms'] ?? null
            ]);

            return [
                'carrier_code' => $carrierCode,
                'carrier_name' => $carrier->carrier_name,
                'connection_successful' => $testResult['success'],
                'response_time_ms' => $testResult['response_time_ms'] ?? null,
                'api_version' => $testResult['api_version'] ?? null,
                'test_message' => $testResult['message'] ?? null,
                'tested_at' => Carbon::now()->toISOString()
            ];
        } catch (\Exception $e) {
            Log::error('Carrier connection test failed', [
                'carrier_code' => $carrierCode,
                'error' => $e->getMessage()
            ]);

            return [
                'carrier_code' => $carrierCode,
                'connection_successful' => false,
                'error_message' => $e->getMessage(),
                'tested_at' => Carbon::now()->toISOString()
            ];
        }
    }
}
