<?php

namespace App\Infrastructure\Categories\Listeners;

use App\Domain\Categories\Events\CategoryUpdated;
use App\Infrastructure\Categories\Services\CategoryTreeService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;

/**
 * Category Updated Event Listener
 * Kategori güncellendiğinde çalışan infrastructure listener
 */
class CategoryUpdatedListener
{
    private CategoryTreeService $treeService;

    public function __construct(CategoryTreeService $treeService)
    {
        $this->treeService = $treeService;
    }

    /**
     * Event'i handle et
     */
    public function handle(CategoryUpdated $event): void
    {
        $category = $event->getCategory();
        $changedFields = $event->getChangedFields();

        try {
            // 1. Cache'i temizle
            $this->clearCategoryCache($category);

            // 2. Değişikliklere göre özel işlemler
            $this->handleSpecificChanges($category, $changedFields);

            // 3. Analytics'e kaydet
            $this->recordUpdateAnalytics($category, $changedFields);

            // 4. SEO güncellemeleri
            $this->updateSeoData($category, $changedFields);

            // 5. Menu cache'ini güncelle
            $this->updateMenuCache($category, $changedFields);

            Log::info('Category updated successfully processed', [
                'category_id' => $category->getId(),
                'changed_fields' => array_keys($changedFields),
            ]);

        } catch (\Exception $e) {
            Log::error('Error processing category updated event', [
                'category_id' => $category->getId(),
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Kategori cache'ini temizle
     */
    private function clearCategoryCache($category): void
    {
        $cacheKeys = [
            'category.' . $category->getId(),
            'category.slug.' . $category->getSlug()->getValue(),
            'categories.tree.*',
            'categories.active.*',
            'categories.featured.*',
            'categories.menu.*',
        ];

        if ($category->getParentId()) {
            $cacheKeys[] = 'categories.parent.' . $category->getParentId() . '.*';
        }

        foreach ($cacheKeys as $pattern) {
            Cache::flush(); // Production'da daha spesifik olmalı
        }
    }

    /**
     * Belirli değişikliklere göre özel işlemler
     */
    private function handleSpecificChanges($category, array $changedFields): void
    {
        // İsim değişikliği
        if (isset($changedFields['name'])) {
            $this->handleNameChange($category, $changedFields['name']);
        }

        // Slug değişikliği
        if (isset($changedFields['slug'])) {
            $this->handleSlugChange($category, $changedFields['slug']);
        }

        // Durum değişikliği
        if (isset($changedFields['status'])) {
            $this->handleStatusChange($category, $changedFields['status']);
        }

        // Featured durumu değişikliği
        if (isset($changedFields['featured'])) {
            $this->handleFeaturedChange($category, $changedFields['featured']);
        }

        // Menu görünürlüğü değişikliği
        if (isset($changedFields['show_in_menu'])) {
            $this->handleMenuVisibilityChange($category, $changedFields['show_in_menu']);
        }

        // Açıklama değişikliği
        if (isset($changedFields['description'])) {
            $this->handleDescriptionChange($category, $changedFields['description']);
        }

        // Pozisyon değişikliği
        if (isset($changedFields['position'])) {
            $this->handlePositionChange($category, $changedFields['position']);
        }
    }

    /**
     * İsim değişikliği işle
     */
    private function handleNameChange($category, array $nameChange): void
    {
        $oldName = $nameChange['old'];
        $newName = $nameChange['new'];

        // SEO meta title'ı güncelle
        Queue::push('update-category-meta-title', [
            'category_id' => $category->getId(),
            'new_name' => $newName,
        ]);

        // Breadcrumb cache'ini temizle
        Cache::forget("category.breadcrumb.{$category->getId()}");

        Log::info('Category name changed', [
            'category_id' => $category->getId(),
            'old_name' => $oldName,
            'new_name' => $newName,
        ]);
    }

    /**
     * Slug değişikliği işle
     */
    private function handleSlugChange($category, array $slugChange): void
    {
        $oldSlug = $slugChange['old'];
        $newSlug = $slugChange['new'];

        // URL redirect'leri oluştur
        Queue::push('create-category-redirect', [
            'old_slug' => $oldSlug,
            'new_slug' => $newSlug,
            'category_id' => $category->getId(),
        ]);

        // Alt kategorilerin URL'lerini güncelle
        Queue::push('update-descendant-urls', [
            'category_id' => $category->getId(),
        ]);

        // Sitemap'i güncelle
        Queue::push('update-sitemap', [
            'type' => 'category',
            'action' => 'update',
            'category_id' => $category->getId(),
            'old_url' => "/categories/{$oldSlug}",
            'new_url' => "/categories/{$newSlug}",
        ]);

        Log::info('Category slug changed', [
            'category_id' => $category->getId(),
            'old_slug' => $oldSlug,
            'new_slug' => $newSlug,
        ]);
    }

    /**
     * Durum değişikliği işle
     */
    private function handleStatusChange($category, array $statusChange): void
    {
        $oldStatus = $statusChange['old'];
        $newStatus = $statusChange['new'];

        if (!$oldStatus && $newStatus) {
            // Kategori aktif hale geldi
            Queue::push('category-activated', [
                'category_id' => $category->getId(),
            ]);

            // Alt kategorileri de aktif et (opsiyonel)
            if (config('categories.auto_activate_children', false)) {
                Queue::push('activate-child-categories', [
                    'parent_id' => $category->getId(),
                ]);
            }

        } elseif ($oldStatus && !$newStatus) {
            // Kategori pasif hale geldi
            Queue::push('category-deactivated', [
                'category_id' => $category->getId(),
            ]);

            // Alt kategorileri de pasif et (opsiyonel)
            if (config('categories.auto_deactivate_children', false)) {
                Queue::push('deactivate-child-categories', [
                    'parent_id' => $category->getId(),
                ]);
            }
        }

        // Menu cache'ini temizle
        Cache::forget('categories.menu.*');
    }

    /**
     * Featured durumu değişikliği işle
     */
    private function handleFeaturedChange($category, array $featuredChange): void
    {
        $oldFeatured = $featuredChange['old'];
        $newFeatured = $featuredChange['new'];

        if (!$oldFeatured && $newFeatured) {
            // Kategori öne çıkarıldı
            Queue::push('category-featured', [
                'category_id' => $category->getId(),
            ]);

            // Featured kategoriler bildirimini gönder
            Queue::push('send-featured-category-notification', [
                'category_id' => $category->getId(),
                'category_name' => $category->getName(),
            ]);
        }

        // Featured kategoriler cache'ini temizle
        Cache::forget('categories.featured.*');
    }

    /**
     * Menu görünürlüğü değişikliği işle
     */
    private function handleMenuVisibilityChange($category, array $visibilityChange): void
    {
        $oldVisibility = $visibilityChange['old'];
        $newVisibility = $visibilityChange['new'];

        // Menu cache'ini temizle
        Cache::forget('menu.categories');
        Cache::forget('categories.menu.*');

        // Menu'yu yeniden oluştur
        Queue::push('regenerate-menu-cache', [
            'trigger' => 'visibility_changed',
            'category_id' => $category->getId(),
            'old_visibility' => $oldVisibility,
            'new_visibility' => $newVisibility,
        ]);

        Log::info('Category menu visibility changed', [
            'category_id' => $category->getId(),
            'old_visibility' => $oldVisibility,
            'new_visibility' => $newVisibility,
        ]);
    }

    /**
     * Açıklama değişikliği işle
     */
    private function handleDescriptionChange($category, array $descriptionChange): void
    {
        // SEO meta description'ı güncelle
        Queue::push('update-category-meta-description', [
            'category_id' => $category->getId(),
            'new_description' => $descriptionChange['new'],
        ]);
    }

    /**
     * Pozisyon değişikliği işle
     */
    private function handlePositionChange($category, array $positionChange): void
    {
        $oldPosition = $positionChange['old'];
        $newPosition = $positionChange['new'];

        // Kardeş kategorilerin sıralamasını güncelle
        Queue::push('reorder-sibling-categories', [
            'category_id' => $category->getId(),
            'parent_id' => $category->getParentId(),
            'old_position' => $oldPosition,
            'new_position' => $newPosition,
        ]);

        // Menu sıralamasını güncelle
        Cache::forget('categories.menu.*');
        Cache::forget('menu.categories');

        Log::info('Category position changed', [
            'category_id' => $category->getId(),
            'old_position' => $oldPosition,
            'new_position' => $newPosition,
        ]);
    }

    /**
     * Update analytics'i kaydet
     */
    private function recordUpdateAnalytics($category, array $changedFields): void
    {
        $analyticsData = [
            'event' => 'category_updated',
            'category_id' => $category->getId(),
            'changed_fields' => array_keys($changedFields),
            'timestamp' => now()->toISOString(),
        ];

        Queue::push('record-analytics', $analyticsData);
    }

    /**
     * SEO verilerini güncelle
     */
    private function updateSeoData($category, array $changedFields): void
    {
        $seoFields = ['name', 'description', 'slug'];
        $needsSeoUpdate = !empty(array_intersect(array_keys($changedFields), $seoFields));

        if ($needsSeoUpdate) {
            Queue::push('update-category-seo', [
                'category_id' => $category->getId(),
                'changed_fields' => array_keys($changedFields),
            ]);

            // Sitemap'i güncelle
            Queue::push('update-sitemap', [
                'type' => 'category',
                'action' => 'update',
                'category_id' => $category->getId(),
                'url' => "/categories/{$category->getSlug()->getValue()}",
                'last_modified' => now()->toISOString(),
            ]);
        }
    }

    /**
     * Menu cache'ini güncelle
     */
    private function updateMenuCache($category, array $changedFields): void
    {
        $menuFields = ['name', 'status', 'show_in_menu', 'position'];
        $needsMenuUpdate = !empty(array_intersect(array_keys($changedFields), $menuFields));

        if ($needsMenuUpdate) {
            Cache::forget('menu.categories');
            Cache::forget('categories.menu.*');

            Queue::push('regenerate-menu-cache', [
                'trigger' => 'category_updated',
                'category_id' => $category->getId(),
                'changed_fields' => array_keys($changedFields),
            ]);
        }
    }
}
