<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessOrderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Sipariş ID'si
     *
     * @var int
     */
    protected $orderId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $orderId)
    {
        $this->orderId = $orderId;
        $this->onQueue('orders');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Sipariş işleme simülasyonu
        Log::info("Sipariş #{$this->orderId} işleniyor...");

        // İşlem simülasyonu için 2 saniye bekle
        sleep(2);

        // İşlem tamamlandı
        Log::info("Sipariş #{$this->orderId} başarı<PERSON> işlendi.");
    }
}
