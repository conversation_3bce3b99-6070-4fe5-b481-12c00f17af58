<?php

namespace App\Core\Infrastructure\Events\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Core\Infrastructure\Events\Contracts\EventListenerInterface;
use App\Core\Infrastructure\Events\Contracts\CacheInvalidationListenerInterface;
use App\Core\Infrastructure\Events\Listeners\CacheInvalidationEventListener;
use App\Core\Infrastructure\Events\Listeners\ProductCacheInvalidationListener;
use App\Core\Infrastructure\Events\Services\EventListenerRegistry;
use App\Core\Infrastructure\Events\Services\EventMiddlewareManager;

/**
 * EventInfrastructureServiceProvider
 * Event infrastructure için service provider
 */
class EventInfrastructureServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerEventServices();
        $this->registerEventListeners();
        $this->registerEventConfiguration();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->publishConfiguration();
        $this->registerEventMappings();
        $this->registerCacheInvalidationListeners();
    }

    /**
     * Event servislerini kaydet
     */
    private function registerEventServices(): void
    {
        // Event Listener Registry
        $this->app->singleton(EventListenerRegistry::class, function ($app) {
            return new EventListenerRegistry();
        });

        // Event Middleware Manager
        $this->app->singleton(EventMiddlewareManager::class, function ($app) {
            return new EventMiddlewareManager();
        });

        // Alias'lar
        $this->app->alias(EventListenerRegistry::class, 'event.listener_registry');
        $this->app->alias(EventMiddlewareManager::class, 'event.middleware_manager');
    }

    /**
     * Event listener'ları kaydet
     */
    private function registerEventListeners(): void
    {
        // Ana Cache Invalidation Listener
        $this->app->singleton(CacheInvalidationEventListener::class, function ($app) {
            return new CacheInvalidationEventListener(
                $app->make('cache.key_generator'),
                $app->make('cache.tag_manager')
            );
        });

        // Product Cache Invalidation Listener
        $this->app->singleton(ProductCacheInvalidationListener::class, function ($app) {
            return new ProductCacheInvalidationListener(
                $app->make('cache.key_generator'),
                $app->make('cache.tag_manager')
            );
        });

        // Event Listener Registry'ye kaydet
        $this->app->afterResolving(EventListenerRegistry::class, function ($registry, $app) {
            $registry->register($app->make(CacheInvalidationEventListener::class));
            $registry->register($app->make(ProductCacheInvalidationListener::class));
        });
    }

    /**
     * Event konfigürasyonunu kaydet
     */
    private function registerEventConfiguration(): void
    {
        $this->mergeConfigFrom(
            __DIR__ . '/../Config/event_infrastructure.php',
            'event_infrastructure'
        );
    }

    /**
     * Konfigürasyon dosyalarını publish et
     */
    private function publishConfiguration(): void
    {
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/../Config/event_infrastructure.php' => config_path('event_infrastructure.php'),
            ], 'event-infrastructure-config');
        }
    }

    /**
     * Event mapping'lerini kaydet
     */
    private function registerEventMappings(): void
    {
        $eventMappings = config('event_infrastructure.event_mappings', []);

        foreach ($eventMappings as $eventClass => $listeners) {
            foreach ($listeners as $listenerClass) {
                if (class_exists($listenerClass)) {
                    Event::listen($eventClass, $listenerClass);
                }
            }
        }
    }

    /**
     * Cache invalidation listener'larını kaydet
     */
    private function registerCacheInvalidationListeners(): void
    {
        if (!config('event_infrastructure.cache_invalidation.enabled', true)) {
            return;
        }

        $registry = $this->app->make(EventListenerRegistry::class);
        $listeners = $registry->getListenersByInterface(CacheInvalidationListenerInterface::class);

        foreach ($listeners as $listener) {
            $supportedEvents = $listener->getSupportedEvents();
            
            foreach ($supportedEvents as $eventClass) {
                Event::listen($eventClass, function ($event) use ($listener) {
                    if ($listener->canHandle($event)) {
                        $listener->handle($event);
                    }
                });
            }
        }

        // Domain event'ler için wildcard listener
        Event::listen('*', function ($eventName, $payload) use ($registry) {
            if (!$this->isDomainEvent($payload)) {
                return;
            }

            $event = $payload[0] ?? null;
            if (!$event) {
                return;
            }

            $listeners = $registry->getListenersByInterface(CacheInvalidationListenerInterface::class);
            
            foreach ($listeners as $listener) {
                if ($listener->canHandle($event)) {
                    $listener->handle($event);
                }
            }
        });
    }

    /**
     * Payload'ın domain event olup olmadığını kontrol et
     */
    private function isDomainEvent($payload): bool
    {
        if (!is_array($payload) || empty($payload)) {
            return false;
        }

        $event = $payload[0] ?? null;
        
        return $event && (
            $event instanceof \App\Domain\Shared\Events\DomainEvent ||
            (is_object($event) && method_exists($event, 'getEventName'))
        );
    }

    /**
     * Provides
     */
    public function provides(): array
    {
        return [
            EventListenerRegistry::class,
            EventMiddlewareManager::class,
            CacheInvalidationEventListener::class,
            ProductCacheInvalidationListener::class,
            'event.listener_registry',
            'event.middleware_manager',
        ];
    }
}
