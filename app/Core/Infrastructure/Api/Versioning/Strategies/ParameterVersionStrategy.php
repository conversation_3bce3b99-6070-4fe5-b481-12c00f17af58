<?php

namespace App\Core\Infrastructure\Api\Versioning\Strategies;

use App\Core\Infrastructure\Api\Versioning\Contracts\VersionStrategyInterface;
use Illuminate\Http\Request;

/**
 * ParameterVersionStrategy
 * Query parameter-based version detection strategy
 */
class ParameterVersionStrategy implements VersionStrategyInterface
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'parameter_name' => 'version',
            'fallback_parameters' => ['api_version', 'v'],
            'case_sensitive' => false,
        ], $config);
    }

    /**
     * Request'ten version'ı extract et
     */
    public function extractVersion(Request $request): ?string
    {
        // Ana parameter'ı kontrol et
        $version = $request->query($this->config['parameter_name']);
        
        if ($version) {
            return $this->normalizeVersion($version);
        }

        // Fallback parameter'ları kontrol et
        foreach ($this->config['fallback_parameters'] as $parameter) {
            $version = $request->query($parameter);
            if ($version) {
                return $this->normalizeVersion($version);
            }
        }

        return null;
    }

    /**
     * Strategy'nin request'i support edip etmediğini kontrol et
     */
    public function supports(Request $request): bool
    {
        return $request->has($this->config['parameter_name']) ||
               collect($this->config['fallback_parameters'])->some(fn($param) => $request->has($param));
    }

    /**
     * Strategy'nin priority'sini al
     */
    public function getPriority(): int
    {
        return 60; // Düşük priority
    }

    /**
     * Strategy'nin adını al
     */
    public function getName(): string
    {
        return 'parameter';
    }

    /**
     * Strategy'nin açıklamasını al
     */
    public function getDescription(): string
    {
        return 'Detects API version from query parameters';
    }

    /**
     * Version'ı request'e set et
     */
    public function setVersion(Request $request, string $version): void
    {
        $request->query->set($this->config['parameter_name'], $version);
    }

    /**
     * Strategy configuration'ını validate et
     */
    public function validateConfiguration(array $config): bool
    {
        return isset($config['parameter_name']) && 
               is_string($config['parameter_name']) && 
               !empty($config['parameter_name']);
    }

    /**
     * Version'ı normalize et
     */
    private function normalizeVersion(string $version): string
    {
        // Whitespace'leri temizle
        $version = trim($version);
        
        // Case sensitivity kontrolü
        if (!$this->config['case_sensitive']) {
            $version = strtolower($version);
        }

        // v prefix'ini kaldır (v1.0 -> 1.0)
        if (str_starts_with($version, 'v')) {
            $version = substr($version, 1);
        }

        return $version;
    }

    /**
     * Version parameter'ını URL'e ekle
     */
    public function addVersionToUrl(string $url, string $version): string
    {
        $separator = str_contains($url, '?') ? '&' : '?';
        return $url . $separator . $this->config['parameter_name'] . '=' . urlencode($version);
    }

    /**
     * URL'den version parameter'ını kaldır
     */
    public function removeVersionFromUrl(string $url): string
    {
        $parameterName = $this->config['parameter_name'];
        $fallbackParameters = $this->config['fallback_parameters'];
        
        $allParameters = array_merge([$parameterName], $fallbackParameters);
        
        foreach ($allParameters as $param) {
            $url = preg_replace('/[?&]' . preg_quote($param) . '=[^&]*/', '', $url);
        }
        
        // Clean up URL
        $url = preg_replace('/\?&/', '?', $url);
        $url = rtrim($url, '?&');
        
        return $url;
    }
}
