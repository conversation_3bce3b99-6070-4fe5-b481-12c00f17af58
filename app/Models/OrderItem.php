<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'product_id',
        'product_name',
        'price',
        'quantity',
        'subtotal',
        'options',
    ];

    protected $casts = [
        'options' => 'array',
    ];

    /**
     * Bu öğenin ait olduğu sipariş
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Bu öğenin ait olduğu ürün
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
