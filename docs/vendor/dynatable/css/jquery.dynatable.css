/*
 * jQ<PERSON>y Dynatable plugin 0.3.1
 *
 * Copyright (c) 2014 <PERSON> (JangoSteve)
 *
 * Dual licensed under the AGPL and Proprietary licenses:
 *   http://www.dynatable.com/license/
 *
 * Date: Tue Jan 02 2014
 */
th a {
  color: #fff;
}
th a:hover {
  color: #fff;
  text-decoration: underline;
}

.dynatable-search {
  float: right;
  margin-bottom: 10px;
}

.dynatable-pagination-links {
  float: right;
}

.dynatable-record-count {
  display: block;
  padding: 5px 0;
}

.dynatable-pagination-links span,
.dynatable-pagination-links li {
  display: inline-block;
}

.dynatable-page-link,
.dynatable-page-break {
  display: block;
  padding: 5px 7px;
}

.dynatable-page-link {
  cursor: pointer;
}

.dynatable-active-page,
.dynatable-disabled-page {
  cursor: text;
}
.dynatable-active-page:hover,
.dynatable-disabled-page:hover {
  text-decoration: none;
}

.dynatable-active-page {
  background: #71AF5A;
  border-radius: 5px;
  color: #fff;
}
.dynatable-active-page:hover {
  color: #fff;
}
.dynatable-disabled-page,
.dynatable-disabled-page:hover {
  background: none;
  color: #999;
}
