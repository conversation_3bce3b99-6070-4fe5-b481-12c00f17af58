<?php

return [
    /*
     * The API key of a MailChimp account. You can find yours at
     * https://us10.admin.mailchimp.com/account/api-key-popup/.
     */
    'apiKey' => env('MAILCHIMP_APIKEY'),

    /*
     * The listName to use when no listName has been specified in a method.
     */
    'defaultListName' => 'subscribers',

    /*
     * Here you can define properties of the lists.
     */
    'lists' => [
        'subscribers' => [
            /*
             * A MailChimp list id. Check the MailChimp docs if you don't know
             * how to get this value:
             * https://mailchimp.com/help/find-audience-id/.
             */
            'id' => env('MAILCHIMP_LIST_ID'),
        ],
    ],

    /*
     * If you're using double opt-in, this setting specifies if your subscribers
     * need to confirm their subscription again.
     */
    'doubleOptIn' => true,

    /*
     * If you're using double opt-in, this setting specifies if your subscribers
     * should receive a welcome email after confirming their subscription.
     */
    'sendWelcomeEmail' => true,

    /*
     * If you need to set a custom subdomain for your campaigns (default null)
     */
    'subdomain' => null,

    /*
     * If your service is behind a proxy, you need to specify your proxy IP
     * addresses here. Example: ['*******']
     */
    'proxy_ips' => [],
];
