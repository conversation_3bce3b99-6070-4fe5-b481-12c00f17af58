<?php

use App\Http\Controllers\Admin\BankAccountController;
use App\Http\Controllers\Admin\CacheController;
use App\Http\Controllers\Admin\EmailLogController;
use App\Http\Controllers\Admin\EmailSettingController;
use App\Http\Controllers\Admin\EmailTemplateController;
use App\Http\Controllers\Admin\QueueMonitorController;
use App\Http\Controllers\Admin\SearchAnalyticsController;
use App\Http\Controllers\Admin\ShippingCompanyController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AttributeController;
use App\Http\Controllers\BulkProductController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\CategoryAttributeController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CategoryOrderController;
use App\Http\Controllers\CategoryTreeController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\CouponController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\CustomerAuthController;
use App\Http\Controllers\CustomerDashboardController;
use App\Http\Controllers\CustomerOrderController;
use App\Http\Controllers\OrderTrackingController;
use App\Http\Controllers\FrontendCategoryController;
use App\Http\Controllers\FrontendProductController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ModuleController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductVariantController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Ana Sayfa
Route::get('/', [HomeController::class, 'index'])->name('home');

// Ürünler
Route::get('/products', [FrontendProductController::class, 'indexWithQueryFilters'])->name('frontend.products.index');
// Eski filtre yöntemi için geriye dönük uyumluluk
Route::get('/products/filtreler/{filters}', [FrontendProductController::class, 'indexWithFilters'])->name('frontend.products.filters');
// Sonra daha genel rotaları tanımla
Route::get('/products/{id}', [FrontendProductController::class, 'show'])->name('frontend.products.show')->where('id', '[0-9]+');
Route::get('/products/{id}/details', [\App\Http\Controllers\Api\ProductController::class, 'getProductDetails'])->where('id', '[0-9]+');

// Kategori içindeki ürünler
Route::get('/categories/{category}/products', [FrontendProductController::class, 'categoryProducts'])->name('categories.products');

// Kategoriler
Route::get('/categories', [FrontendCategoryController::class, 'index'])->name('categories.index');
Route::get('/categories/{category}', [FrontendCategoryController::class, 'show'])->name('categories.show');

// Arama
Route::get('/search', [SearchController::class, 'index'])->name('search.index');
Route::get('/api/search/autocomplete', [SearchController::class, 'autocomplete'])->name('search.autocomplete');

// Admin kimlik doğrulama rotaları
Route::middleware('guest')->group(function () {
    Route::get('/admin/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/admin/login', [AuthController::class, 'login']);
});

Route::middleware('auth')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/profile', [AuthController::class, 'profile'])->name('profile');
    Route::put('/profile', [AuthController::class, 'updateProfile'])->name('profile.update');
});

// Müşteri kimlik doğrulama rotaları
Route::middleware('guest')->group(function () {
    Route::get('/login', [CustomerAuthController::class, 'showLoginForm'])->name('customer.login');
    Route::post('/login', [CustomerAuthController::class, 'login']);
    Route::get('/register', [CustomerAuthController::class, 'showRegisterForm'])->name('customer.register');
    Route::post('/register', [CustomerAuthController::class, 'register']);
});

// Müşteri sipariş rotaları (auth middleware ile)
require __DIR__ . '/customer/orders.php';

// Müşteri paneli rotaları
Route::middleware(['auth', 'role:customer'])->prefix('customer')->name('customer.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [CustomerDashboardController::class, 'index'])->name('dashboard');

    // Profil
    Route::get('/profile', [CustomerDashboardController::class, 'profile'])->name('profile');
    Route::put('/profile', [CustomerDashboardController::class, 'updateProfile'])->name('profile.update');

    // Siparişler
    Route::get('/orders', [CustomerOrderController::class, 'index'])->name('orders');
    Route::get('/orders/{orderNumber}', [CustomerOrderController::class, 'show'])->name('orders.show');

    // Adresler
    Route::get('/addresses', [CustomerDashboardController::class, 'addresses'])->name('addresses');
    Route::get('/addresses/create', [CustomerDashboardController::class, 'createAddress'])->name('addresses.create');
    Route::post('/addresses', [CustomerDashboardController::class, 'storeAddress'])->name('addresses.store');
    Route::get('/addresses/{address}/edit', [CustomerDashboardController::class, 'editAddress'])->name('addresses.edit');
    Route::put('/addresses/{address}', [CustomerDashboardController::class, 'updateAddress'])->name('addresses.update');
    Route::delete('/addresses/{address}', [CustomerDashboardController::class, 'destroyAddress'])->name('addresses.destroy');

    // Favoriler
    Route::get('/favorites', [CustomerDashboardController::class, 'favorites'])->name('favorites');
    Route::delete('/favorites/{favorite}', [CustomerDashboardController::class, 'destroyFavorite'])->name('favorites.destroy');

    // Çıkış
    Route::post('/logout', [CustomerAuthController::class, 'logout'])->name('logout');
});

// Test rotaları
Route::get('/test', function () {
    return Inertia::render('Test');
});

Route::get('/simple-test', function () {
    return Inertia::render('SimpleTest');
});

// Admin rotaları
Route::prefix('admin')->middleware(['auth'])->group(function () {
    // Kargo bölgeleri ve metodları için rotaları dahil et
    require __DIR__ . '/admin/shipping.php';
    // Sipariş yönetimi rotaları
    require __DIR__ . '/admin/orders.php';
    // Dashboard
    Route::get('/dashboard', function () {
        try {
            $productCount = \App\Models\Product::count();
            $categoryCount = \App\Models\Category::count();
            $userCount = \App\Models\User::count();
            $orderCount = \App\Models\Order::count(); // withoutTrashed() kaldırıldı

            $latestProducts = \App\Models\Product::with('category')
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();

            $latestOrders = \App\Models\Order::with('user') // withoutTrashed() kaldırıldı
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        } catch (\Exception $e) {
            // Veritabanı bağlantı sorunu varsa varsayılan değerler kullan
            $productCount = 0;
            $categoryCount = 0;
            $userCount = 0;
            $orderCount = 0;
            $latestProducts = collect();
            $latestOrders = collect();
        }

        return Inertia::render('Admin/Dashboard', [
            'stats' => [
                'productCount' => $productCount,
                'categoryCount' => $categoryCount,
                'userCount' => $userCount,
                'orderCount' => $orderCount,
            ],
            'latestProducts' => $latestProducts,
            'latestOrders' => $latestOrders
        ]);
    })->name('admin.dashboard');

    // Ürünler
    Route::get('/products', [ProductController::class, 'index'])->name('admin.products.index');
    Route::get('/products/create', [ProductController::class, 'create'])->name('admin.products.create');
    Route::post('/products', [ProductController::class, 'store'])->name('admin.products.store');
    Route::get('/products/{product}/edit', [ProductController::class, 'edit'])->name('admin.products.edit');
    Route::put('/products/{product}', [ProductController::class, 'update'])->name('admin.products.update');
    Route::delete('/products/{product}', [ProductController::class, 'destroy'])->name('admin.products.destroy');

    // Kategoriler
    Route::get('/categories', [CategoryController::class, 'index'])
        ->name('admin.categories.index');

    // Siparişler
    Route::get('/orders', [OrderController::class, 'index'])->name('admin.orders.index');
    Route::get('/orders/{order}', [OrderController::class, 'show'])->name('admin.orders.show');
    Route::get('/orders/{order}/edit', [OrderController::class, 'edit'])->name('admin.orders.edit');
    Route::put('/orders/{order}', [OrderController::class, 'update'])->name('admin.orders.update');
    Route::delete('/orders/{order}', [OrderController::class, 'destroy'])->name('admin.orders.destroy');

    // Kullanıcılar
    Route::get('/users', [UserController::class, 'index'])->name('admin.users.index');
    Route::get('/users/create', [UserController::class, 'create'])->name('admin.users.create');
    Route::post('/users', [UserController::class, 'store'])->name('admin.users.store');
    Route::get('/users/{user}', [UserController::class, 'show'])->name('admin.users.show');
    Route::get('/users/{user}/edit', [UserController::class, 'edit'])->name('admin.users.edit');
    Route::put('/users/{user}', [UserController::class, 'update'])->name('admin.users.update');
    Route::delete('/users/{user}', [UserController::class, 'destroy'])->name('admin.users.destroy');

    // Roller
    Route::get('/roles', [RoleController::class, 'index'])->name('admin.roles.index');
    Route::get('/roles/create', [RoleController::class, 'create'])->name('admin.roles.create');
    Route::post('/roles', [RoleController::class, 'store'])->name('admin.roles.store');
    Route::get('/roles/{role}', [RoleController::class, 'show'])->name('admin.roles.show');
    Route::get('/roles/{role}/edit', [RoleController::class, 'edit'])->name('admin.roles.edit');
    Route::put('/roles/{role}', [RoleController::class, 'update'])->name('admin.roles.update');
    Route::delete('/roles/{role}', [RoleController::class, 'destroy'])->name('admin.roles.destroy');

    // Kuponlar
    Route::get('/coupons', [CouponController::class, 'index'])->name('admin.coupons.index');
    Route::get('/coupons/create', [CouponController::class, 'create'])->name('admin.coupons.create');
    Route::post('/coupons', [CouponController::class, 'store'])->name('admin.coupons.store');
    Route::get('/coupons/{coupon}', [CouponController::class, 'show'])->name('admin.coupons.show');
    Route::get('/coupons/{coupon}/edit', [CouponController::class, 'edit'])->name('admin.coupons.edit');
    Route::put('/coupons/{coupon}', [CouponController::class, 'update'])->name('admin.coupons.update');
    Route::delete('/coupons/{coupon}', [CouponController::class, 'destroy'])->name('admin.coupons.destroy');

    // Modüller
    Route::get('/modules', [ModuleController::class, 'index'])->name('admin.modules.index');
    Route::post('/modules', [ModuleController::class, 'update'])->name('admin.modules.update');

    // Özellikler
    Route::get('/attributes', [AttributeController::class, 'index'])->name('admin.attributes.index');
    Route::get('/attributes/create', [AttributeController::class, 'create'])->name('admin.attributes.create');
    Route::post('/attributes', [AttributeController::class, 'store'])->name('admin.attributes.store');
    Route::get('/attributes/{attribute}', [AttributeController::class, 'show'])->name('admin.attributes.show');
    Route::get('/attributes/{attribute}/edit', [AttributeController::class, 'edit'])->name('admin.attributes.edit');
    Route::put('/attributes/{attribute}', [AttributeController::class, 'update'])->name('admin.attributes.update');
    Route::delete('/attributes/{attribute}', [AttributeController::class, 'destroy'])->name('admin.attributes.destroy');

    // Ürün varyantları
    Route::get('/products/{product}/variants', [ProductVariantController::class, 'index'])->name('admin.products.variants.index');
    Route::get('/products/{product}/variants/create', [ProductVariantController::class, 'create'])->name('admin.products.variants.create');
    Route::post('/products/{product}/variants', [ProductVariantController::class, 'store'])->name('admin.products.variants.store');
    Route::get('/products/{product}/variants/{variant}/edit', [ProductVariantController::class, 'edit'])->name('admin.products.variants.edit');
    Route::put('/products/{product}/variants/{variant}', [ProductVariantController::class, 'update'])->name('admin.products.variants.update');
    Route::put('/products/{product}/variants/{variant}/stock', [ProductVariantController::class, 'updateStock'])->name('admin.products.variants.stock');
    Route::put('/products/{product}/variants/{variant}/set-default', [ProductVariantController::class, 'setDefault'])->name('admin.products.variants.set-default');
    Route::put('/products/{product}/variants-bulk-stock', [ProductVariantController::class, 'bulkUpdateStock'])->name('admin.products.variants.bulk-stock');
    Route::put('/products/{product}/variants-bulk-status', [ProductVariantController::class, 'bulkUpdateStatus'])->name('admin.products.variants.bulk-status');
    Route::put('/products/{product}/variants-bulk-price', [ProductVariantController::class, 'bulkUpdatePrice'])->name('admin.products.variants.bulk-price');
    Route::delete('/products/{product}/variants-bulk-delete', [ProductVariantController::class, 'bulkDestroy'])->name('admin.products.variants.bulk-delete');
    Route::delete('/products/{product}/variants/{variant}', [ProductVariantController::class, 'destroy'])->name('admin.products.variants.destroy');

    // Toplu ürün işlemleri
    Route::get('/products/bulk-edit', [BulkProductController::class, 'edit'])->name('admin.products.bulk-edit');
    Route::put('/products/bulk-status', [BulkProductController::class, 'updateStatus'])->name('admin.products.bulk-status');
    Route::put('/products/bulk-category', [BulkProductController::class, 'updateCategory'])->name('admin.products.bulk-category');
    Route::put('/products/bulk-prices', [BulkProductController::class, 'updatePrices'])->name('admin.products.bulk-prices');
    Route::put('/products/bulk-stock', [BulkProductController::class, 'updateStock'])->name('admin.products.bulk-stock');
    Route::put('/products/bulk-default-variant', [BulkProductController::class, 'updateDefaultVariant'])->name('admin.products.bulk-default-variant');
    Route::delete('/products/bulk-delete', [BulkProductController::class, 'destroy'])->name('admin.products.bulk-delete');
    Route::get('/products/import', [BulkProductController::class, 'importForm'])->name('admin.products.import');
    Route::post('/products/import', [BulkProductController::class, 'import'])->name('admin.products.import.process');
    Route::get('/products/export', [BulkProductController::class, 'export'])->name('admin.products.export');

    // Kategori ağacı ve sıralama
    Route::get('/categories/tree', [CategoryTreeController::class, 'index'])->name('admin.categories.tree');
    Route::put('/categories/positions', [CategoryTreeController::class, 'updatePositions'])->name('admin.categories.positions');
    Route::put('/categories/{category}/featured', [CategoryTreeController::class, 'toggleFeatured'])->name('admin.categories.featured');
    Route::put('/categories/{category}/menu-visibility', [CategoryTreeController::class, 'toggleMenuVisibility'])->name('admin.categories.menu-visibility');
    Route::get('/categories/dropdown', [CategoryController::class, 'getTree'])->name('admin.categories.dropdown');

    // Kategori sıralama (drag-and-drop)
    Route::get('/categories/order', [CategoryOrderController::class, 'index'])->name('admin.categories.order');
    Route::post('/categories/order', [CategoryOrderController::class, 'update'])->name('admin.categories.order.update');

    // Kategoriler
    Route::get('/categories', [CategoryController::class, 'index'])->name('admin.categories.index');
    Route::get('/categories/create', [CategoryController::class, 'create'])->name('admin.categories.create');
    Route::post('/categories', [CategoryController::class, 'store'])->name('admin.categories.store');
    Route::get('/categories/{category}', [CategoryController::class, 'show'])->name('admin.categories.show');
    Route::get('/categories/{category}/edit', [CategoryController::class, 'edit'])->name('admin.categories.edit');
    Route::put('/categories/{category}', [CategoryController::class, 'update'])->name('admin.categories.update');
    Route::delete('/categories/{category}', [CategoryController::class, 'destroy'])->name('admin.categories.destroy');

    // Kategori özellikleri
    Route::get('/categories/{category}/attributes', [CategoryAttributeController::class, 'edit'])->name('admin.categories.attributes.edit');
    Route::put('/categories/{category}/attributes', [CategoryAttributeController::class, 'update'])->name('admin.categories.attributes.update');

    // Siparişler
    Route::get('/orders', [OrderController::class, 'index'])->name('admin.orders.index');
    Route::get('/orders/{order}', [OrderController::class, 'show'])->name('admin.orders.show');
    Route::put('/orders/{order}', [OrderController::class, 'update'])->name('admin.orders.update');
    Route::delete('/orders/{order}', [OrderController::class, 'destroy'])->name('admin.orders.destroy');

    // Sipariş notları
    Route::post('/orders/{order}/notes', [OrderController::class, 'addNote'])->name('admin.orders.notes.add');
    Route::post('/orders/{order}/notes-with-type', [OrderController::class, 'addNoteWithType'])->name('admin.orders.notes.add-with-type');
    Route::delete('/orders/{order}/notes/{note}', [OrderController::class, 'deleteNote'])->name('admin.orders.notes.delete');

    // Sipariş durumu
    Route::put('/orders/{order}/status', [OrderController::class, 'updateStatus'])->name('admin.orders.update-status');
    Route::put('/orders/{order}/payment-status', [OrderController::class, 'updatePaymentStatus'])->name('admin.orders.payment-status');

    // Kargo ve teslimat
    Route::put('/orders/{order}/shipping', [OrderController::class, 'updateShipping'])->name('admin.orders.shipping');
    Route::put('/orders/{order}/shipping-info', [OrderController::class, 'updateShippingInfo'])->name('admin.orders.shipping-info');
    Route::put('/orders/{order}/mark-delivered', [OrderController::class, 'markAsDelivered'])->name('admin.orders.mark-delivered');

    // Fatura
    Route::put('/orders/{order}/invoice-info', [OrderController::class, 'updateInvoiceInfo'])->name('admin.orders.invoice-info');
    Route::get('/orders/{order}/invoice', [OrderController::class, 'generateInvoice'])->name('admin.orders.invoice');

    // Bildirimler
    Route::post('/orders/{order}/send-confirmation', [OrderController::class, 'sendConfirmationEmail'])->name('admin.orders.send-confirmation');

    // Kargo Şirketleri - Artık routes/admin/shipping.php içinde tanımlanmıştır
    // Route::get('/shipping-companies', [ShippingCompanyController::class, 'index'])->name('admin.shipping-companies.index');
    // Route::get('/shipping-companies/create', [ShippingCompanyController::class, 'create'])->name('admin.shipping-companies.create');
    // Route::post('/shipping-companies', [ShippingCompanyController::class, 'store'])->name('admin.shipping-companies.store');
    // Route::get('/shipping-companies/{shippingCompany}/edit', [ShippingCompanyController::class, 'edit'])->name('admin.shipping-companies.edit');
    // Route::put('/shipping-companies/{shippingCompany}', [ShippingCompanyController::class, 'update'])->name('admin.shipping-companies.update');
    // Route::delete('/shipping-companies/{shippingCompany}', [ShippingCompanyController::class, 'destroy'])->name('admin.shipping-companies.destroy');
    Route::post('/shipping-companies/{shippingCompany}/toggle-status', [ShippingCompanyController::class, 'toggleStatus'])->name('admin.shipping-companies.toggle-status');

    // Banka Hesapları
    Route::get('/bank-accounts', [BankAccountController::class, 'index'])->name('admin.bank-accounts.index');
    Route::get('/bank-accounts/create', [BankAccountController::class, 'create'])->name('admin.bank-accounts.create');
    Route::post('/bank-accounts', [BankAccountController::class, 'store'])->name('admin.bank-accounts.store');
    Route::get('/bank-accounts/{bankAccount}/edit', [BankAccountController::class, 'edit'])->name('admin.bank-accounts.edit');
    Route::put('/bank-accounts/{bankAccount}', [BankAccountController::class, 'update'])->name('admin.bank-accounts.update');
    Route::delete('/bank-accounts/{bankAccount}', [BankAccountController::class, 'destroy'])->name('admin.bank-accounts.destroy');
    Route::post('/bank-accounts/{bankAccount}/toggle-status', [BankAccountController::class, 'toggleStatus'])->name('admin.bank-accounts.toggle-status');

    // Cache Yönetimi
    Route::get('/cache', [CacheController::class, 'index'])->name('admin.cache.index');
    Route::post('/cache/clear', [CacheController::class, 'clear'])->name('admin.cache.clear');
    Route::post('/cache/warm', [CacheController::class, 'warm'])->name('admin.cache.warm');

    // Konum Yönetimi
    Route::get('/locations', [\App\Http\Controllers\Admin\LocationController::class, 'index'])->name('admin.locations.index');
    Route::post('/locations/refresh-cache', [\App\Http\Controllers\Admin\LocationController::class, 'refreshCache'])->name('admin.locations.refresh-cache');
    Route::post('/locations/import-data', [\App\Http\Controllers\Admin\LocationController::class, 'importData'])->name('admin.locations.import-data');

    // E-posta Şablonları
    Route::get('/email-templates', [EmailTemplateController::class, 'index'])->name('admin.email-templates.index');
    Route::get('/email-templates/{emailTemplate}', [EmailTemplateController::class, 'show'])->name('admin.email-templates.show');
    Route::get('/email-templates/{emailTemplate}/edit', [EmailTemplateController::class, 'edit'])->name('admin.email-templates.edit');
    Route::put('/email-templates/{emailTemplate}', [EmailTemplateController::class, 'update'])->name('admin.email-templates.update');
    Route::post('/email-templates/{emailTemplate}/send-test', [EmailTemplateController::class, 'sendTest'])->name('admin.email-templates.send-test');

    // E-posta Ayarları
    Route::get('/email-settings', [EmailSettingController::class, 'index'])->name('admin.email-settings.index');
    Route::put('/email-settings', [EmailSettingController::class, 'update'])->name('admin.email-settings.update');
    Route::post('/email-settings/send-test', [EmailSettingController::class, 'sendTest'])->name('admin.email-settings.send-test');
    Route::post('/email-settings/test-mailchimp', [EmailSettingController::class, 'testMailchimp'])->name('admin.email-settings.test-mailchimp');
    Route::post('/email-settings/test-mailtrap', [EmailSettingController::class, 'testMailtrap'])->name('admin.email-settings.test-mailtrap');

    // E-posta Logları
    Route::get('/email-logs', [EmailLogController::class, 'index'])->name('admin.email-logs.index');
    Route::get('/email-logs/{emailLog}', [EmailLogController::class, 'show'])->name('admin.email-logs.show');
    Route::post('/email-logs/clear', [EmailLogController::class, 'clear'])->name('admin.email-logs.clear');
    Route::post('/email-logs/{emailLog}/resend', [EmailLogController::class, 'resend'])->name('admin.email-logs.resend');

    // Arama Analitiği
    Route::get('/search-analytics', [SearchAnalyticsController::class, 'index'])->name('admin.search-analytics.index');
    Route::get('/search-analytics/term/{query}', [SearchAnalyticsController::class, 'showSearchTerm'])->name('admin.search-analytics.term');
    Route::get('/search-analytics/product/{id}', [SearchAnalyticsController::class, 'showProduct'])->name('admin.search-analytics.product');

    // Queue Monitör
    Route::get('/queue-monitor', [QueueMonitorController::class, 'index'])->name('admin.queue-monitor.index');
    Route::post('/queue-monitor/send-test-jobs', [QueueMonitorController::class, 'sendTestJobs'])->name('admin.queue-monitor.send-test-jobs');
    Route::post('/queue-monitor/clear-failed-jobs', [QueueMonitorController::class, 'clearFailedJobs'])->name('admin.queue-monitor.clear-failed-jobs');
});

// Sepet Rotaları
Route::get('/cart', [CartController::class, 'index'])->name('cart.index');
Route::post('/cart/add', [CartController::class, 'addItem'])->name('cart.add');
Route::put('/cart/update/{cartItem}', [CartController::class, 'updateItem'])->name('cart.update');
Route::delete('/cart/remove/{cartItem}', [CartController::class, 'removeItem'])->name('cart.remove');
Route::post('/cart/clear', [CartController::class, 'clear'])->name('cart.clear');
Route::post('/cart/coupon', [CartController::class, 'applyCoupon'])->name('cart.coupon.apply');
Route::delete('/cart/coupon', [CartController::class, 'removeCoupon'])->name('cart.coupon.remove');

// Sipariş Takip Rotaları
Route::get('/orders/track', [OrderTrackingController::class, 'index'])->name('orders.track');
Route::post('/orders/track', [OrderTrackingController::class, 'track'])->name('orders.track.submit');

// Ödeme Rotaları
Route::get('/checkout', [CheckoutController::class, 'index'])->name('checkout');
Route::get('/checkout/success', [CheckoutController::class, 'success'])->name('checkout.success');
Route::get('/checkout/payment-form', [CheckoutController::class, 'paymentForm'])->name('checkout.payment-form');

// Ödeme İşleme Rotaları (Inertia.js'i bypass eden rotalar)
Route::middleware([\App\Http\Middleware\BypassInertia::class])->group(function () {
    Route::post('/payment/bank-transfer', [PaymentController::class, 'processBankTransfer'])->name('checkout.bank-transfer');
    Route::post('/payment/iyzico', [PaymentController::class, 'processIyzico'])->name('checkout.iyzico');
    Route::post('/payment/paywithiyzico', [PaymentController::class, 'processPayWithIyzico'])->name('checkout.paywithiyzico');
    Route::post('/payment/callback', [PaymentController::class, 'iyzicoCallback'])->name('checkout.callback');
    Route::match(['get', 'post'], '/payment/paywithiyzico/callback', [PaymentController::class, 'payWithIyzicoCallback'])->name('checkout.paywithiyzico.callback');
});

// API rotaları
Route::prefix('api')->group(function () {
    // Sepet sayısı
    Route::get('/cart/count', [CartController::class, 'getCartCount'])->name('api.cart.count');

    // Ürün arama
    Route::get('/products/search', [\App\Http\Controllers\Api\ProductController::class, 'search']);

    // Resim yükleme
    Route::post('/upload-image', [\App\Http\Controllers\Api\ImageUploadController::class, 'upload']);

    // Favoriler
    Route::post('/favorites/add', [\App\Http\Controllers\Api\FavoriteController::class, 'add'])->middleware('auth');
    Route::post('/favorites/remove', [\App\Http\Controllers\Api\FavoriteController::class, 'remove'])->middleware('auth');
    Route::get('/favorites', [\App\Http\Controllers\Api\FavoriteController::class, 'getUserFavorites'])->middleware('auth');
    Route::get('/favorites/check', [\App\Http\Controllers\Api\FavoriteController::class, 'checkFavoriteStatus'])->middleware('auth');

    // Kargo şirketleri
    Route::get('/shipping-companies', function() {
        return \App\Models\ShippingCompany::where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'code', 'tracking_url_pattern']);
    });

    // Kargo bölgeleri ve metodları
    Route::get('/shipping/methods', [\App\Http\Controllers\Api\ShippingController::class, 'getMethods']);
    Route::get('/shipping/zones', [\App\Http\Controllers\Api\ShippingController::class, 'getZones']);
    Route::post('/shipping/available-methods', [\App\Http\Controllers\Api\ShippingController::class, 'getAvailableMethods']);
    Route::post('/shipping/calculate-cost', [\App\Http\Controllers\Api\ShippingController::class, 'calculateShippingCost']);

    // Ödeme durumu kontrolü
    Route::get('/check-payment-status', [\App\Http\Controllers\Api\PaymentStatusController::class, 'checkStatus']);

    // Ödeme kesintisi yönetimi
    Route::get('/check-order-status', [PaymentController::class, 'checkOrderStatus'])->name('api.check-order-status');
    Route::get('/resume-payment', [PaymentController::class, 'resumePayment'])->name('api.resume-payment');
    Route::post('/cancel-order', [PaymentController::class, 'cancelOrder'])->name('api.cancel-order');

    // Konum yönetimi için API rotaları
    Route::prefix('locations')->group(function () {
        // Ülkeler
        Route::get('/countries', [\App\Http\Controllers\Api\LocationController::class, 'countries']);

        // Eyaletler/İller
        Route::get('/countries/{countryId}/states', [\App\Http\Controllers\Api\LocationController::class, 'states']);
        Route::get('/turkiye/states', [\App\Http\Controllers\Api\LocationController::class, 'turkeyStates']);

        // Şehirler/İlçeler
        Route::get('/states/{stateId}/cities', [\App\Http\Controllers\Api\LocationController::class, 'cities']);
        Route::get('/turkiye/states/{stateId}/cities', [\App\Http\Controllers\Api\LocationController::class, 'turkeyCities']);

        // Önbelleği yenile (sadece yöneticiler için)
        Route::post('/refresh-cache', [\App\Http\Controllers\Api\LocationController::class, 'refreshCache'])->middleware('auth:sanctum');
    });

    // Kimlik doğrulama gerektiren rotalar
    Route::middleware('auth')->group(function () {
        // Ürünler
        Route::apiResource('products', ProductController::class)
            ->except('index');

        // Kategoriler
        Route::apiResource('categories', CategoryController::class)
            ->except('index');
    });
});

// SEO dostu kategori ve ürün URL'leri - admin ve diğer özel URL'leri hariç tut
// Bu route'u en sona ekledik, böylece diğer tüm route'lar öncelikli olacak
Route::get('/{seo_url}', function($seo_url) {
    // Kategori URL'si mi kontrol et
    if (preg_match('/-c-(\d+)$/', $seo_url)) {
        return app()->make(FrontendCategoryController::class)->showBySeoUrl(request(), $seo_url);
    }

    // Ürün URL'si mi kontrol et
    if (preg_match('/-p-/', $seo_url)) {
        return app()->make(FrontendProductController::class)->showBySeoUrl(request(), $seo_url);
    }

    // Eşleşme yoksa 404 döndür
    abort(404);
})
->where('seo_url', '^(?!admin|api|cart|checkout|customer|login|register|profile|test|simple-test).*$')
->name('frontend.products.show.seo');
