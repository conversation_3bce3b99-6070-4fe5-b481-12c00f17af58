# Yeniden Düzenlenmiş Migration Yapısı

Bu dizin, uygulamanın veritabanı migration dosyalarını içerir. Migration'lar, tablolar arası bağımlılıklar ve mantıksal gruplamalar göz önünde bulundurularak yeniden düzenlenmiştir.

## Migration Dosyaları

Migration'lar aşağıdaki sırayla düzenlenmiştir:

### 1. **0001_01_01_000001_create_base_system_tables.php**
**Bağımlılık:** Yok
**İçerik:**
- `users` - Kullanıcı kimlik doğrulama tablosu
- `password_reset_tokens` - <PERSON><PERSON><PERSON> sıfırlama token'ları
- `sessions` - Kullanı<PERSON>ı oturumları
- `cache`, `cache_locks` - Önbellek sistemi
- `jobs`, `job_batches`, `failed_jobs` - Kuyruk sistemi

### 2. **0001_01_01_000002_create_location_tables.php**
**Bağımlılık:** Yok (users'a referans yok)
**İçerik:**
- `countries` - Ülkeler tablosu
- `states` - Eyaletler/İller tablosu (countries'e bağımlı)
- `cities` - Şehirler tablosu (states'e bağımlı)

### 3. **0001_01_01_000003_create_catalog_base_tables.php**
**Bağımlılık:** Yok (kendi içinde referanslar)
**İçerik:**
- `categories` - Ürün kategorileri (self-referencing)
- `attributes` - Ürün özellikleri tanımları
- `attribute_values` - Özellik değerleri (attributes'e bağımlı)
- `category_attributes` - Kategori-özellik ilişkileri

### 4. **0001_01_01_000004_create_product_tables.php**
**Bağımlılık:** categories, attributes
**İçerik:**
- `products` - Ana ürün bilgileri (categories'e bağımlı)
- `product_attributes` - Ürün-özellik ilişkileri
- `product_variants` - Ürün varyantları (products'e bağımlı)
- `product_views` - Ürün görüntüleme analitikleri (products, users'e bağımlı)

### 5. **0001_01_01_000005_create_shipping_tables.php**
**Bağımlılık:** locations (shipping_zone_locations için)
**İçerik:**
- `shipping_companies` - Kargo şirketleri
- `shipping_zones` - Kargo bölgeleri
- `shipping_zone_locations` - Bölge-lokasyon ilişkileri
- `shipping_methods` - Kargo yöntemleri
- `shipping_zone_methods` - Bölge-yöntem ilişkileri ve fiyatlandırma
- `shipping_zone_method_rates` - Detaylı fiyat yapıları

### 6. **0001_01_01_000006_create_order_base_tables.php**
**Bağımlılık:** users
**İçerik:**
- `addresses` - Müşteri adresleri (users'e bağımlı)
- `bank_accounts` - Banka hesap bilgileri
- `coupons` - İndirim kuponları

### 7. **0001_01_01_000007_create_order_tables.php**
**Bağımlılık:** users, products, coupons
**İçerik:**
- `orders` - Ana sipariş bilgileri
- `order_items` - Sipariş kalemleri (orders, products'e bağımlı)
- `order_addresses` - Sipariş adresleri
- `order_notes` - Sipariş notları

### 8. **0001_01_01_000008_create_cart_and_favorites_tables.php**
**Bağımlılık:** users, products, product_variants
**İçerik:**
- `carts` - Alışveriş sepetleri
- `cart_items` - Sepet kalemleri
- `favorites` - Favori ürünler

### 9. **0001_01_01_000009_create_email_and_analytics_tables.php**
**Bağımlılık:** users
**İçerik:**
- `email_templates` - E-posta şablonları
- `email_logs` - E-posta gönderim geçmişi
- `email_settings` - E-posta yapılandırması
- `search_queries` - Arama analitikleri (users'e bağımlı)

### 10. **0001_01_01_000010_create_permission_tables.php**
**Bağımlılık:** users
**İçerik:**
- `permissions` - Sistem izinleri
- `roles` - Kullanıcı rolleri
- `role_permissions` - Rol-izin ilişkileri
- `user_roles` - Kullanıcı-rol ilişkileri
- `user_permissions` - Doğrudan kullanıcı izinleri

## Migration Çalıştırma

Tüm migration'ları doğru sırayla çalıştırmak için:

```bash
php artisan migrate --path=database/migrations_new
```

Belirli bir migration'ı çalıştırmak için:

```bash
php artisan migrate --path=database/migrations_new/0001_01_01_000001_create_base_system_tables.php
```

Tüm migration'ları geri almak için:

```bash
php artisan migrate:rollback --path=database/migrations_new
```

Migration'ları yenilemek için (rollback + migrate):

```bash
php artisan migrate:refresh --path=database/migrations_new
```

## Bağımlılık Haritası

```
users (base)
├── sessions
├── addresses
├── product_views
├── search_queries
├── user_roles
├── user_permissions
├── carts
└── favorites

countries
└── states
    └── cities

categories (self-referencing)
├── products
│   ├── product_attributes
│   ├── product_variants
│   └── product_views
└── category_attributes

attributes
├── attribute_values
├── category_attributes
└── product_attributes

shipping_companies
├── shipping_methods
└── shipping_zones
    ├── shipping_zone_locations
    └── shipping_zone_methods
        └── shipping_zone_method_rates

orders
├── order_items
├── order_addresses
└── order_notes

permissions
├── role_permissions
└── user_permissions

roles
├── role_permissions
└── user_roles
```

## Önemli Notlar

1. **Foreign Key Constraints**: Tüm foreign key'ler uygun cascade davranışları ile tanımlanmıştır
2. **Soft Deletes**: Gerekli tablolarda soft delete desteği eklenmiştir
3. **Indexing**: Performans için gerekli index'ler tanımlanmıştır
4. **Unique Constraints**: Veri bütünlüğü için unique constraint'ler eklenmiştir
5. **Nullable Fields**: İsteğe bağlı alanlar nullable olarak işaretlenmiştir

## Eski Migration'lardan Farklar

- Tüm sonradan eklenen sütunlar ana tablolara entegre edilmiştir
- Tablolar mantıksal gruplar halinde organize edilmiştir
- Bağımlılık sırası optimize edilmiştir
- Gereksiz migration dosyaları birleştirilmiştir
- Tutarlı naming convention uygulanmıştır

## Backup Bilgisi

Eski migration dosyaları `database/migrations_backup_YYYYMMDD_HHMMSS/` dizininde yedeklenmiştir.
