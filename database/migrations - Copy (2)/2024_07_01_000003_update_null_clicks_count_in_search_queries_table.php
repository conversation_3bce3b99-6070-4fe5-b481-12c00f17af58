<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // clicks_count ve conversion_count sütunlarındaki null değerleri 0 olarak güncelle
        DB::table('search_queries')
            ->whereNull('clicks_count')
            ->update(['clicks_count' => 0]);
            
        DB::table('search_queries')
            ->whereNull('conversion_count')
            ->update(['conversion_count' => 0]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Bu migrasyon geri alınamaz
    }
};
