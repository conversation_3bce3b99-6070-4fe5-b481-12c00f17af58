<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // shipping_cost sütunu ekle (eğer yoksa)
            if (!Schema::hasColumn('orders', 'shipping_cost')) {
                $table->decimal('shipping_cost', 10, 2)->default(0)->after('total_amount');
            }
            
            // notes sütunu ekle (eğer yoksa)
            if (!Schema::hasColumn('orders', 'notes')) {
                $table->text('notes')->nullable()->after('tracking_url');
            }
            
            // admin_notes sütunu ekle (eğer yoksa)
            if (!Schema::hasColumn('orders', 'admin_notes')) {
                $table->text('admin_notes')->nullable()->after('notes');
            }
            
            // Diğer eksik sütunları ekle
            if (!Schema::hasColumn('orders', 'billing_name')) {
                $table->string('billing_name')->nullable()->after('bank_account_id');
            }
            
            if (!Schema::hasColumn('orders', 'billing_email')) {
                $table->string('billing_email')->nullable()->after('billing_name');
            }
            
            if (!Schema::hasColumn('orders', 'billing_phone')) {
                $table->string('billing_phone')->nullable()->after('billing_email');
            }
            
            if (!Schema::hasColumn('orders', 'billing_address')) {
                $table->string('billing_address')->nullable()->after('billing_phone');
            }
            
            if (!Schema::hasColumn('orders', 'billing_city')) {
                $table->string('billing_city')->nullable()->after('billing_address');
            }
            
            if (!Schema::hasColumn('orders', 'billing_state')) {
                $table->string('billing_state')->nullable()->after('billing_city');
            }
            
            if (!Schema::hasColumn('orders', 'billing_zipcode')) {
                $table->string('billing_zipcode')->nullable()->after('billing_state');
            }
            
            if (!Schema::hasColumn('orders', 'billing_country')) {
                $table->string('billing_country')->nullable()->after('billing_zipcode');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_name')) {
                $table->string('shipping_name')->nullable()->after('billing_country');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_email')) {
                $table->string('shipping_email')->nullable()->after('shipping_name');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_phone')) {
                $table->string('shipping_phone')->nullable()->after('shipping_email');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_address')) {
                $table->string('shipping_address')->nullable()->after('shipping_phone');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_city')) {
                $table->string('shipping_city')->nullable()->after('shipping_address');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_state')) {
                $table->string('shipping_state')->nullable()->after('shipping_city');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_zipcode')) {
                $table->string('shipping_zipcode')->nullable()->after('shipping_state');
            }
            
            if (!Schema::hasColumn('orders', 'shipping_country')) {
                $table->string('shipping_country')->nullable()->after('shipping_zipcode');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Bu sütunları silmek istemiyoruz, çünkü veri kaybına neden olabilir
        });
    }
};
