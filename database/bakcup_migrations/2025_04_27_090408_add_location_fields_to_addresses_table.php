<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->foreignId('country_id')->nullable()->after('user_id')->constrained('countries');
            $table->foreignId('state_id')->nullable()->after('country_id')->constrained('states');
            $table->foreignId('city_id')->nullable()->after('state_id')->constrained('cities');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->dropForeign(['city_id']);
            $table->dropForeign(['state_id']);
            $table->dropForeign(['country_id']);
            $table->dropColumn(['city_id', 'state_id', 'country_id']);
        });
    }
};
