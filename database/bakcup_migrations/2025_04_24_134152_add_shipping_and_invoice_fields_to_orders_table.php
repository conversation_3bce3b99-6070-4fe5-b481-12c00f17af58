<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('shipping_company')->nullable()->after('tracking_number');
            $table->date('shipping_date')->nullable()->after('shipping_company');
            $table->date('actual_delivery_date')->nullable()->after('estimated_delivery_date');
            $table->string('invoice_number')->nullable()->after('actual_delivery_date');
            $table->date('invoice_date')->nullable()->after('invoice_number');
            $table->string('invoice_pdf')->nullable()->after('invoice_date');
            $table->string('shipping_label_pdf')->nullable()->after('invoice_pdf');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'shipping_company',
                'shipping_date',
                'actual_delivery_date',
                'invoice_number',
                'invoice_date',
                'invoice_pdf',
                'shipping_label_pdf',
            ]);
        });
    }
};
