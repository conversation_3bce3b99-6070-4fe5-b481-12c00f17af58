<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Carrier Performance Metrics - Kargo şirketi performans metrikleri
        Schema::create('carrier_performance_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('carrier_integration_id')->constrained()->onDelete('cascade');
            $table->date('metric_date');
            $table->string('metric_period'); // daily, weekly, monthly
            
            // Volume metrics
            $table->integer('total_shipments')->default(0);
            $table->integer('successful_shipments')->default(0);
            $table->integer('failed_shipments')->default(0);
            $table->integer('cancelled_shipments')->default(0);
            $table->integer('returned_shipments')->default(0);
            
            // Delivery performance
            $table->decimal('on_time_delivery_rate', 5, 4)->default(0); // %
            $table->decimal('delivery_success_rate', 5, 4)->default(0); // %
            $table->decimal('average_delivery_days', 8, 2)->default(0);
            $table->integer('total_delivery_attempts')->default(0);
            $table->decimal('first_attempt_success_rate', 5, 4)->default(0); // %
            
            // Cost metrics
            $table->decimal('total_shipping_cost', 12, 4)->default(0);
            $table->decimal('average_cost_per_shipment', 10, 4)->default(0);
            $table->decimal('cost_variance_percentage', 5, 4)->default(0); // %
            
            // Service quality
            $table->decimal('tracking_accuracy_rate', 5, 4)->default(0); // %
            $table->integer('tracking_updates_count')->default(0);
            $table->decimal('customer_satisfaction_score', 3, 2)->nullable(); // 1-5 scale
            $table->integer('complaints_count')->default(0);
            $table->integer('damage_reports_count')->default(0);
            
            // API performance
            $table->integer('api_calls_count')->default(0);
            $table->integer('api_errors_count')->default(0);
            $table->decimal('api_success_rate', 5, 4)->default(0); // %
            $table->decimal('average_response_time_ms', 8, 2)->default(0);
            
            $table->timestamps();

            // Indexes
            $table->unique(['carrier_integration_id', 'metric_date', 'metric_period']);
            $table->index(['metric_date', 'metric_period']);
            $table->index('on_time_delivery_rate');
            $table->index('delivery_success_rate');
        });

        // Shipping Analytics Summary - Kargo analytics özeti
        Schema::create('shipping_analytics_summary', function (Blueprint $table) {
            $table->id();
            $table->date('summary_date');
            $table->string('summary_period'); // daily, weekly, monthly, quarterly, yearly
            
            // Overall volume
            $table->integer('total_shipments')->default(0);
            $table->integer('domestic_shipments')->default(0);
            $table->integer('international_shipments')->default(0);
            $table->decimal('total_shipping_revenue', 12, 4)->default(0);
            $table->decimal('total_shipping_cost', 12, 4)->default(0);
            $table->decimal('shipping_profit_margin', 5, 4)->default(0); // %
            
            // Delivery performance
            $table->decimal('overall_delivery_rate', 5, 4)->default(0); // %
            $table->decimal('on_time_delivery_rate', 5, 4)->default(0); // %
            $table->decimal('average_delivery_time_hours', 8, 2)->default(0);
            $table->integer('total_delivery_attempts')->default(0);
            $table->decimal('first_attempt_success_rate', 5, 4)->default(0); // %
            
            // Service type breakdown
            $table->integer('standard_shipments')->default(0);
            $table->integer('express_shipments')->default(0);
            $table->integer('overnight_shipments')->default(0);
            $table->integer('pickup_shipments')->default(0);
            
            // Geographic distribution
            $table->json('top_destinations')->nullable(); // Top 10 cities/countries
            $table->json('shipping_zones_breakdown')->nullable();
            $table->json('regional_performance')->nullable();
            
            // Customer satisfaction
            $table->decimal('average_customer_rating', 3, 2)->nullable(); // 1-5 scale
            $table->integer('total_reviews')->default(0);
            $table->integer('complaints_count')->default(0);
            $table->decimal('complaint_resolution_rate', 5, 4)->default(0); // %
            
            // Issues and returns
            $table->integer('damaged_packages')->default(0);
            $table->integer('lost_packages')->default(0);
            $table->integer('returned_packages')->default(0);
            $table->decimal('package_damage_rate', 5, 4)->default(0); // %
            $table->decimal('package_loss_rate', 5, 4)->default(0); // %
            $table->decimal('return_rate', 5, 4)->default(0); // %
            
            // Cost analysis
            $table->decimal('average_cost_per_kg', 8, 4)->default(0);
            $table->decimal('average_cost_per_shipment', 10, 4)->default(0);
            $table->json('cost_breakdown_by_service')->nullable();
            $table->json('cost_trends')->nullable();
            
            $table->timestamps();

            // Indexes
            $table->unique(['summary_date', 'summary_period']);
            $table->index(['summary_period', 'summary_date']);
            $table->index('total_shipments');
        });

        // Delivery Time Analysis - Teslimat süresi analizi
        Schema::create('delivery_time_analysis', function (Blueprint $table) {
            $table->id();
            $table->foreignId('carrier_integration_id')->constrained()->onDelete('cascade');
            $table->string('service_type');
            $table->string('origin_zone');
            $table->string('destination_zone');
            $table->date('analysis_date');
            
            // Time metrics
            $table->decimal('average_delivery_hours', 8, 2)->default(0);
            $table->decimal('median_delivery_hours', 8, 2)->default(0);
            $table->decimal('min_delivery_hours', 8, 2)->default(0);
            $table->decimal('max_delivery_hours', 8, 2)->default(0);
            $table->decimal('std_deviation_hours', 8, 2)->default(0);
            
            // Performance buckets
            $table->integer('delivered_within_24h')->default(0);
            $table->integer('delivered_within_48h')->default(0);
            $table->integer('delivered_within_72h')->default(0);
            $table->integer('delivered_within_week')->default(0);
            $table->integer('delivered_over_week')->default(0);
            
            // Sample size and reliability
            $table->integer('total_samples')->default(0);
            $table->decimal('confidence_level', 5, 4)->default(0); // %
            $table->boolean('is_reliable')->default(false);
            
            // Trends
            $table->decimal('trend_direction', 5, 4)->default(0); // -1 to 1 (getting worse to better)
            $table->decimal('seasonal_factor', 5, 4)->default(1); // Seasonal adjustment
            
            $table->timestamps();

            // Indexes
            $table->unique(['carrier_integration_id', 'service_type', 'origin_zone', 'destination_zone', 'analysis_date'], 'unique_delivery_analysis');
            $table->index(['analysis_date', 'service_type']);
            $table->index('average_delivery_hours');
        });

        // Route Optimization Analysis - Rota optimizasyon analizi
        Schema::create('route_optimization_analysis', function (Blueprint $table) {
            $table->id();
            $table->date('analysis_date');
            $table->string('route_type'); // pickup, delivery, return
            $table->string('geographic_area'); // city, region, country
            
            // Route efficiency
            $table->integer('total_stops')->default(0);
            $table->decimal('total_distance_km', 10, 2)->default(0);
            $table->decimal('total_time_hours', 8, 2)->default(0);
            $table->decimal('average_distance_per_stop', 8, 2)->default(0);
            $table->decimal('average_time_per_stop', 6, 2)->default(0);
            
            // Optimization metrics
            $table->decimal('route_efficiency_score', 5, 4)->default(0); // 0-1 scale
            $table->decimal('fuel_efficiency_score', 5, 4)->default(0); // 0-1 scale
            $table->decimal('time_efficiency_score', 5, 4)->default(0); // 0-1 scale
            $table->decimal('cost_per_km', 8, 4)->default(0);
            $table->decimal('cost_per_stop', 8, 4)->default(0);
            
            // Success rates
            $table->integer('successful_deliveries')->default(0);
            $table->integer('failed_deliveries')->default(0);
            $table->integer('rescheduled_deliveries')->default(0);
            $table->decimal('first_attempt_success_rate', 5, 4)->default(0); // %
            
            // Recommendations
            $table->json('optimization_suggestions')->nullable();
            $table->decimal('potential_savings_percentage', 5, 4)->default(0); // %
            $table->json('bottleneck_analysis')->nullable();
            
            $table->timestamps();

            // Indexes
            $table->unique(['analysis_date', 'route_type', 'geographic_area']);
            $table->index(['analysis_date', 'route_type']);
            $table->index('route_efficiency_score');
        });

        // Customer Shipping Preferences - Müşteri kargo tercihleri
        Schema::create('customer_shipping_preferences', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->string('customer_segment')->nullable(); // vip, regular, new
            $table->date('analysis_date');
            
            // Preference metrics
            $table->string('preferred_carrier')->nullable();
            $table->string('preferred_service_type')->nullable();
            $table->string('preferred_delivery_time')->nullable(); // morning, afternoon, evening
            $table->boolean('prefers_signature_required')->default(false);
            $table->boolean('prefers_insurance')->default(false);
            $table->string('preferred_delivery_location')->nullable(); // door, pickup_point, locker
            
            // Behavior patterns
            $table->integer('total_shipments')->default(0);
            $table->decimal('average_order_value', 10, 4)->default(0);
            $table->decimal('average_shipping_cost', 8, 4)->default(0);
            $table->decimal('shipping_cost_sensitivity', 5, 4)->default(0); // 0-1 scale
            $table->decimal('delivery_speed_preference', 5, 4)->default(0); // 0-1 scale
            
            // Satisfaction metrics
            $table->decimal('average_satisfaction_score', 3, 2)->nullable(); // 1-5 scale
            $table->integer('complaints_count')->default(0);
            $table->integer('compliments_count')->default(0);
            $table->decimal('retention_rate', 5, 4)->default(0); // %
            
            // Geographic patterns
            $table->json('frequent_destinations')->nullable();
            $table->string('primary_shipping_zone')->nullable();
            $table->boolean('ships_internationally')->default(false);
            
            $table->timestamps();

            // Indexes
            $table->index(['customer_id', 'analysis_date']);
            $table->index(['customer_segment', 'analysis_date']);
            $table->index('preferred_carrier');
            $table->index('average_satisfaction_score');

            // Foreign key
            $table->foreign('customer_id')->references('id')->on('users')->onDelete('set null');
        });

        // Shipping Cost Trends - Kargo maliyet trendleri
        Schema::create('shipping_cost_trends', function (Blueprint $table) {
            $table->id();
            $table->foreignId('carrier_integration_id')->nullable()->constrained()->onDelete('set null');
            $table->string('service_type');
            $table->string('route_type'); // domestic, international, regional
            $table->date('trend_date');
            $table->string('trend_period'); // daily, weekly, monthly
            
            // Cost metrics
            $table->decimal('average_base_cost', 10, 4)->default(0);
            $table->decimal('average_fuel_surcharge', 8, 4)->default(0);
            $table->decimal('average_total_cost', 10, 4)->default(0);
            $table->decimal('cost_per_kg', 8, 4)->default(0);
            $table->decimal('cost_per_km', 8, 4)->default(0);
            
            // Trend analysis
            $table->decimal('cost_change_percentage', 5, 4)->default(0); // % change from previous period
            $table->decimal('fuel_impact_percentage', 5, 4)->default(0); // % of cost due to fuel
            $table->decimal('seasonal_adjustment', 5, 4)->default(1); // Seasonal factor
            $table->string('trend_direction')->default('stable'); // increasing, decreasing, stable
            
            // Market factors
            $table->decimal('fuel_price_index', 8, 4)->nullable();
            $table->decimal('currency_exchange_rate', 8, 6)->nullable();
            $table->decimal('market_competition_index', 5, 4)->nullable(); // 0-1 scale
            $table->json('external_factors')->nullable(); // Weather, events, etc.
            
            // Volume impact
            $table->integer('shipment_volume')->default(0);
            $table->decimal('volume_discount_rate', 5, 4)->default(0); // %
            $table->decimal('capacity_utilization', 5, 4)->default(0); // %
            
            // Forecasting
            $table->decimal('predicted_next_period_cost', 10, 4)->nullable();
            $table->decimal('prediction_confidence', 5, 4)->nullable(); // %
            $table->json('cost_drivers')->nullable();
            
            $table->timestamps();

            // Indexes
            $table->index(['carrier_integration_id', 'trend_date']);
            $table->index(['service_type', 'route_type', 'trend_date']);
            $table->index(['trend_date', 'trend_period']);
            $table->index('cost_change_percentage');
        });

        // Shipping Incidents - Kargo olayları ve sorunları
        Schema::create('shipping_incidents', function (Blueprint $table) {
            $table->id();
            $table->string('incident_number')->unique(); // INC-2024-001
            $table->foreignId('shipment_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('carrier_integration_id')->nullable()->constrained()->onDelete('set null');
            
            // Incident classification
            $table->string('incident_type'); // damage, loss, delay, wrong_delivery, customer_complaint
            $table->string('severity'); // low, medium, high, critical
            $table->string('category'); // operational, system, external, customer
            $table->string('subcategory')->nullable();
            
            // Incident details
            $table->string('title');
            $table->text('description');
            $table->timestamp('occurred_at');
            $table->timestamp('reported_at');
            $table->timestamp('detected_at')->nullable();
            
            // Impact assessment
            $table->decimal('financial_impact', 10, 4)->default(0);
            $table->integer('affected_shipments')->default(1);
            $table->integer('affected_customers')->default(1);
            $table->string('service_impact')->nullable(); // none, minor, major, critical
            $table->integer('delay_hours')->nullable();
            
            // Resolution tracking
            $table->string('status')->default('open'); // open, investigating, resolved, closed
            $table->timestamp('resolved_at')->nullable();
            $table->timestamp('closed_at')->nullable();
            $table->text('resolution_description')->nullable();
            $table->decimal('resolution_cost', 10, 4)->default(0);
            
            // Responsibility and escalation
            $table->unsignedBigInteger('assigned_to')->nullable();
            $table->unsignedBigInteger('reported_by')->nullable();
            $table->string('escalation_level')->default('level1'); // level1, level2, level3, management
            $table->boolean('customer_notified')->default(false);
            $table->timestamp('customer_notification_sent')->nullable();
            
            // Root cause analysis
            $table->string('root_cause')->nullable();
            $table->text('root_cause_analysis')->nullable();
            $table->json('contributing_factors')->nullable();
            $table->text('prevention_measures')->nullable();
            
            // Documentation
            $table->json('attachments')->nullable(); // Photos, documents, etc.
            $table->json('communication_log')->nullable();
            $table->json('metadata')->nullable();
            
            $table->timestamps();

            // Indexes
            $table->index(['shipment_id', 'incident_type']);
            $table->index(['carrier_integration_id', 'occurred_at']);
            $table->index(['incident_type', 'severity']);
            $table->index(['status', 'occurred_at']);
            $table->index(['occurred_at', 'resolved_at']);

            // Foreign keys
            $table->foreign('assigned_to')->references('id')->on('users')->onDelete('set null');
            $table->foreign('reported_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_incidents');
        Schema::dropIfExists('shipping_cost_trends');
        Schema::dropIfExists('customer_shipping_preferences');
        Schema::dropIfExists('route_optimization_analysis');
        Schema::dropIfExists('delivery_time_analysis');
        Schema::dropIfExists('shipping_analytics_summary');
        Schema::dropIfExists('carrier_performance_metrics');
    }
};
