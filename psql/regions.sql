--
-- PostgreSQL database dump
--

-- Dumped from database version 16.8 (Ubuntu 16.8-1.pgdg24.04+1)
-- Dumped by pg_dump version 16.8 (Ubuntu 16.8-1.pgdg24.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

ALTER TABLE IF EXISTS ONLY public.regions DROP CONSTRAINT IF EXISTS regions_pkey;
DROP TABLE IF EXISTS public.regions;
SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: regions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.regions (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    translations text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    flag smallint DEFAULT 1 NOT NULL,
    "wikiDataId" character varying(255)
);


ALTER TABLE public.regions OWNER TO postgres;

--
-- Name: COLUMN regions."wikiDataId"; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.regions."wikiDataId" IS 'Rapid API GeoDB Cities';


--
-- Name: regions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

ALTER TABLE public.regions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.regions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Data for Name: regions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.regions VALUES (1, 'Africa', '{"ko":"아프리카","pt-BR":"África","pt":"África","nl":"Afrika","hr":"Afrika","fa":"آفریقا","de":"Afrika","es":"África","fr":"Afrique","ja":"アフリカ","it":"Africa","zh-CN":"非洲","tr":"Afrika","ru":"Африка","uk":"Африка","pl":"Afryka"}', '2023-08-14 10:41:03', '2023-08-14 10:41:03', 1, 'Q15');
INSERT INTO public.regions VALUES (2, 'Americas', '{"ko":"아메리카","pt-BR":"América","pt":"América","nl":"Amerika","hr":"Amerika","fa":"قاره آمریکا","de":"Amerika","es":"América","fr":"Amérique","ja":"アメリカ州","it":"America","zh-CN":"美洲","tr":"Amerika","ru":"Америка","uk":"Америка","pl":"Ameryka"}', '2023-08-14 10:41:03', '2024-06-16 04:09:55', 1, 'Q828');
INSERT INTO public.regions VALUES (3, 'Asia', '{"ko":"아시아","pt-BR":"Ásia","pt":"Ásia","nl":"Azië","hr":"Ázsia","fa":"آسیا","de":"Asien","es":"Asia","fr":"Asie","ja":"アジア","it":"Asia","zh-CN":"亚洲","tr":"Asya","ru":"Азия","uk":"Азія","pl":"Azja"}', '2023-08-14 10:41:03', '2023-08-14 10:41:03', 1, 'Q48');
INSERT INTO public.regions VALUES (4, 'Europe', '{"ko":"유럽","pt-BR":"Europa","pt":"Europa","nl":"Europa","hr":"Európa","fa":"اروپا","de":"Europa","es":"Europa","fr":"Europe","ja":"ヨーロッパ","it":"Europa","zh-CN":"欧洲","tr":"Avrupa","ru":"Европа","uk":"Європа","pl":"Europa"}', '2023-08-14 10:41:03', '2023-08-14 10:41:03', 1, 'Q46');
INSERT INTO public.regions VALUES (5, 'Oceania', '{"ko":"오세아니아","pt-BR":"Oceania","pt":"Oceania","nl":"Oceanië en Australië","hr":"Óceánia és Ausztrália","fa":"اقیانوسیه","de":"Ozeanien und Australien","es":"Oceanía","fr":"Océanie","ja":"オセアニア","it":"Oceania","zh-CN":"大洋洲","tr":"Okyanusya","ru":"Океания","uk":"Океанія","pl":"Oceania"}', '2023-08-14 10:41:03', '2023-08-14 10:41:03', 1, 'Q55643');
INSERT INTO public.regions VALUES (6, 'Polar', '{"ko":"남극","pt-BR":"Antártida","pt":"Antártida","nl":"Antarctica","hr":"Antarktika","fa":"جنوبگان","de":"Antarktika","es":"Antártida","fr":"Antarctique","ja":"南極大陸","it":"Antartide","zh-CN":"南極洲","tr":"Antarktika","ru":"Антарктика","uk":"Антарктика","pl":"Antarktyka"}', '2023-08-14 10:41:03', '2024-06-16 04:20:26', 1, 'Q51');


--
-- Name: regions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.regions_id_seq', 6, true);


--
-- Name: regions regions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.regions
    ADD CONSTRAINT regions_pkey PRIMARY KEY (id);


--
-- PostgreSQL database dump complete
--

