--
-- PostgreSQL database dump
--

-- Dumped from database version 16.8 (Ubuntu 16.8-1.pgdg24.04+1)
-- Dumped by pg_dump version 16.8 (Ubuntu 16.8-1.pgdg24.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

ALTER TABLE IF EXISTS ONLY public.subregions DROP CONSTRAINT IF EXISTS subregions_region_id_fkey;
DROP INDEX IF EXISTS public.subregions_region_id_idx;
ALTER TABLE IF EXISTS ONLY public.subregions DROP CONSTRAINT IF EXISTS subregions_pkey;
DROP TABLE IF EXISTS public.subregions;
SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: subregions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.subregions (
    id bigint NOT NULL,
    name character varying(100) NOT NULL,
    translations text,
    region_id bigint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    flag smallint DEFAULT 1 NOT NULL,
    "wikiDataId" character varying(255)
);


ALTER TABLE public.subregions OWNER TO postgres;

--
-- Name: COLUMN subregions."wikiDataId"; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.subregions."wikiDataId" IS 'Rapid API GeoDB Cities';


--
-- Name: subregions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

ALTER TABLE public.subregions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.subregions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Data for Name: subregions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.subregions VALUES (1, 'Northern Africa', '{"ko":"북아프리카","pt":"Norte de África","nl":"Noord-Afrika","hr":"Sjeverna Afrika","fa":"شمال آفریقا","de":"Nordafrika","es":"Norte de África","fr":"Afrique du Nord","ja":"北アフリカ","it":"Nordafrica","zh-CN":"北部非洲","ru":"Северная Африка","uk":"Північна Африка","pl":"Afryka Północna"}', 1, '2023-08-14 10:41:03', '2023-08-24 23:40:23', 1, 'Q27381');
INSERT INTO public.subregions VALUES (2, 'Middle Africa', '{"ko":"중앙아프리카","pt":"África Central","nl":"Centraal-Afrika","hr":"Srednja Afrika","fa":"مرکز آفریقا","de":"Zentralafrika","es":"África Central","fr":"Afrique centrale","ja":"中部アフリカ","it":"Africa centrale","zh-CN":"中部非洲","ru":"Средняя Африка","uk":"Середня Африка","pl":"Afryka Środkowa"}', 1, '2023-08-14 10:41:03', '2023-08-24 23:52:09', 1, 'Q27433');
INSERT INTO public.subregions VALUES (3, 'Western Africa', '{"ko":"서아프리카","pt":"África Ocidental","nl":"West-Afrika","hr":"Zapadna Afrika","fa":"غرب آفریقا","de":"Westafrika","es":"África Occidental","fr":"Afrique de l''Ouest","ja":"西アフリカ","it":"Africa occidentale","zh-CN":"西非","ru":"Западная Африка","uk":"Західна Африка","pl":"Afryka Zachodnia"}', 1, '2023-08-14 10:41:03', '2023-08-24 23:52:09', 1, 'Q4412');
INSERT INTO public.subregions VALUES (4, 'Eastern Africa', '{"ko":"동아프리카","pt":"África Oriental","nl":"Oost-Afrika","hr":"Istočna Afrika","fa":"شرق آفریقا","de":"Ostafrika","es":"África Oriental","fr":"Afrique de l''Est","ja":"東アフリカ","it":"Africa orientale","zh-CN":"东部非洲","ru":"Восточная Африка","uk":"Східна Африка","pl":"Afryka Wschodnia"}', 1, '2023-08-14 10:41:03', '2023-08-24 23:52:10', 1, 'Q27407');
INSERT INTO public.subregions VALUES (5, 'Southern Africa', '{"ko":"남아프리카","pt":"África Austral","nl":"Zuidelijk Afrika","hr":"Južna Afrika","fa":"جنوب آفریقا","de":"Südafrika","es":"África austral","fr":"Afrique australe","ja":"南部アフリカ","it":"Africa australe","zh-CN":"南部非洲","ru":"Южная Африка","uk":"Південна Африка","pl":"Afryka Południowa"}', 1, '2023-08-14 10:41:03', '2023-08-24 23:52:10', 1, 'Q27394');
INSERT INTO public.subregions VALUES (6, 'Northern America', '{"ko":"북미","pt":"América Setentrional","nl":"Noord-Amerika","fa":"شمال آمریکا","de":"Nordamerika","es":"América Norteña","fr":"Amérique septentrionale","ja":"北部アメリカ","it":"America settentrionale","zh-CN":"北美地區","ru":"Северная Америка","uk":"Північна Америка","pl":"Ameryka Północna"}', 2, '2023-08-14 10:41:03', '2023-08-24 23:52:10', 1, 'Q2017699');
INSERT INTO public.subregions VALUES (7, 'Caribbean', '{"ko":"카리브","pt":"Caraíbas","nl":"Caraïben","hr":"Karibi","fa":"کارائیب","de":"Karibik","es":"Caribe","fr":"Caraïbes","ja":"カリブ海地域","it":"Caraibi","zh-CN":"加勒比地区","ru":"Карибы","uk":"Кариби","pl":"Karaiby"}', 2, '2023-08-14 10:41:03', '2024-06-16 04:42:18', 1, 'Q664609');
INSERT INTO public.subregions VALUES (8, 'South America', '{"ko":"남아메리카","pt":"América do Sul","nl":"Zuid-Amerika","hr":"Južna Amerika","fa":"آمریکای جنوبی","de":"Südamerika","es":"América del Sur","fr":"Amérique du Sud","ja":"南アメリカ","it":"America meridionale","zh-CN":"南美洲","ru":"Южная Америка","uk":"Південна Америка","pl":"Ameryka Południowa"}', 2, '2023-08-14 10:41:03', '2023-08-24 23:52:10', 1, 'Q18');
INSERT INTO public.subregions VALUES (9, 'Central America', '{"ko":"중앙아메리카","pt":"América Central","nl":"Centraal-Amerika","hr":"Srednja Amerika","fa":"آمریکای مرکزی","de":"Zentralamerika","es":"América Central","fr":"Amérique centrale","ja":"中央アメリカ","it":"America centrale","zh-CN":"中美洲","ru":"Центральная Америка","uk":"Центральна Америка","pl":"Ameryka Środkowa"}', 2, '2023-08-14 10:41:03', '2023-08-24 23:52:11', 1, 'Q27611');
INSERT INTO public.subregions VALUES (10, 'Central Asia', '{"ko":"중앙아시아","pt":"Ásia Central","nl":"Centraal-Azië","hr":"Srednja Azija","fa":"آسیای میانه","de":"Zentralasien","es":"Asia Central","fr":"Asie centrale","ja":"中央アジア","it":"Asia centrale","zh-CN":"中亚","ru":"Центральная Азия","uk":"Центральна Азія","pl":"Azja Środkowa"}', 3, '2023-08-14 10:41:03', '2023-08-24 23:52:11', 1, 'Q27275');
INSERT INTO public.subregions VALUES (11, 'Western Asia', '{"ko":"서아시아","pt":"Sudoeste Asiático","nl":"Zuidwest-Azië","hr":"Jugozapadna Azija","fa":"غرب آسیا","de":"Vorderasien","es":"Asia Occidental","fr":"Asie de l''Ouest","ja":"西アジア","it":"Asia occidentale","zh-CN":"西亚","ru":"Западная Азия","uk":"Західна Азія","pl":"Azja Zachodnia"}', 3, '2023-08-14 10:41:03', '2023-08-24 23:52:11', 1, 'Q27293');
INSERT INTO public.subregions VALUES (12, 'Eastern Asia', '{"ko":"동아시아","pt":"Ásia Oriental","nl":"Oost-Azië","hr":"Istočna Azija","fa":"شرق آسیا","de":"Ostasien","es":"Asia Oriental","fr":"Asie de l''Est","ja":"東アジア","it":"Asia orientale","zh-CN":"東亞","ru":"Восточная Азия","uk":"Східна Азія","pl":"Azja Wschodnia"}', 3, '2023-08-14 10:41:03', '2023-08-24 23:52:11', 1, 'Q27231');
INSERT INTO public.subregions VALUES (13, 'South-Eastern Asia', '{"ko":"동남아시아","pt":"Sudeste Asiático","nl":"Zuidoost-Azië","hr":"Jugoistočna Azija","fa":"جنوب شرق آسیا","de":"Südostasien","es":"Sudeste Asiático","fr":"Asie du Sud-Est","ja":"東南アジア","it":"Sud-est asiatico","zh-CN":"东南亚","ru":"Юго-Восточная Азия","uk":"Південно-Східна Азія","pl":"Azja Południowo-Wschodnia"}', 3, '2023-08-14 10:41:03', '2023-08-24 23:52:12', 1, 'Q11708');
INSERT INTO public.subregions VALUES (14, 'Southern Asia', '{"ko":"남아시아","pt":"Ásia Meridional","nl":"Zuid-Azië","hr":"Južna Azija","fa":"جنوب آسیا","de":"Südasien","es":"Asia del Sur","fr":"Asie du Sud","ja":"南アジア","it":"Asia meridionale","zh-CN":"南亚","ru":"Южная Азия","uk":"Південна Азія","pl":"Azja Południowa"}', 3, '2023-08-14 10:41:03', '2023-08-24 23:52:12', 1, 'Q771405');
INSERT INTO public.subregions VALUES (15, 'Eastern Europe', '{"ko":"동유럽","pt":"Europa de Leste","nl":"Oost-Europa","hr":"Istočna Europa","fa":"شرق اروپا","de":"Osteuropa","es":"Europa Oriental","fr":"Europe de l''Est","ja":"東ヨーロッパ","it":"Europa orientale","zh-CN":"东欧","ru":"Восточная Европа","uk":"Східна Європа","pl":"Europa Wschodnia"}', 4, '2023-08-14 10:41:03', '2023-08-24 23:52:12', 1, 'Q27468');
INSERT INTO public.subregions VALUES (16, 'Southern Europe', '{"ko":"남유럽","pt":"Europa meridional","nl":"Zuid-Europa","hr":"Južna Europa","fa":"جنوب اروپا","de":"Südeuropa","es":"Europa del Sur","fr":"Europe du Sud","ja":"南ヨーロッパ","it":"Europa meridionale","zh-CN":"南欧","ru":"Южная Европа","uk":"Південна Європа","pl":"Europa Południowa"}', 4, '2023-08-14 10:41:03', '2023-08-24 23:52:12', 1, 'Q27449');
INSERT INTO public.subregions VALUES (17, 'Western Europe', '{"ko":"서유럽","pt":"Europa Ocidental","nl":"West-Europa","hr":"Zapadna Europa","fa":"غرب اروپا","de":"Westeuropa","es":"Europa Occidental","fr":"Europe de l''Ouest","ja":"西ヨーロッパ","it":"Europa occidentale","zh-CN":"西欧","ru":"Западная Европа","uk":"Західна Європа","pl":"Europa Zachodnia"}', 4, '2023-08-14 10:41:03', '2023-08-24 23:52:12', 1, 'Q27496');
INSERT INTO public.subregions VALUES (18, 'Northern Europe', '{"ko":"북유럽","pt":"Europa Setentrional","nl":"Noord-Europa","hr":"Sjeverna Europa","fa":"شمال اروپا","de":"Nordeuropa","es":"Europa del Norte","fr":"Europe du Nord","ja":"北ヨーロッパ","it":"Europa settentrionale","zh-CN":"北歐","ru":"Северная Европа","uk":"Північна Європа","pl":"Europa Północna"}', 4, '2023-08-14 10:41:03', '2023-08-24 23:52:13', 1, 'Q27479');
INSERT INTO public.subregions VALUES (19, 'Australia and New Zealand', '{"ko":"오스트랄라시아","pt":"Australásia","nl":"Australazië","hr":"Australazija","fa":"استرالزی","de":"Australasien","es":"Australasia","fr":"Australasie","ja":"オーストララシア","it":"Australasia","zh-CN":"澳大拉西亞","ru":"Австралия и Новая Зеландия","uk":"Австралія та Нова Зеландія","pl":"Australia i Nowa Zelandia"}', 5, '2023-08-14 10:41:03', '2023-08-24 23:52:13', 1, 'Q45256');
INSERT INTO public.subregions VALUES (20, 'Melanesia', '{"ko":"멜라네시아","pt":"Melanésia","nl":"Melanesië","hr":"Melanezija","fa":"ملانزی","de":"Melanesien","es":"Melanesia","fr":"Mélanésie","ja":"メラネシア","it":"Melanesia","zh-CN":"美拉尼西亚","ru":"Меланезия","uk":"Меланезія","pl":"Melanezja"}', 5, '2023-08-14 10:41:03', '2023-08-24 23:52:13', 1, 'Q37394');
INSERT INTO public.subregions VALUES (21, 'Micronesia', '{"ko":"미크로네시아","pt":"Micronésia","nl":"Micronesië","hr":"Mikronezija","fa":"میکرونزی","de":"Mikronesien","es":"Micronesia","fr":"Micronésie","ja":"ミクロネシア","it":"Micronesia","zh-CN":"密克罗尼西亚群岛","ru":"Микронезия","uk":"Мікронезія","pl":"Mikronezja"}', 5, '2023-08-14 10:41:03', '2023-08-24 23:52:13', 1, 'Q3359409');
INSERT INTO public.subregions VALUES (22, 'Polynesia', '{"ko":"폴리네시아","pt":"Polinésia","nl":"Polynesië","hr":"Polinezija","fa":"پلی‌نزی","de":"Polynesien","es":"Polinesia","fr":"Polynésie","ja":"ポリネシア","it":"Polinesia","zh-CN":"玻里尼西亞","ru":"Полинезия","uk":"Полінезія","pl":"Polinezja"}', 5, '2023-08-14 10:41:03', '2023-08-24 23:52:13', 1, 'Q35942');


--
-- Name: subregions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.subregions_id_seq', 22, true);


--
-- Name: subregions subregions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.subregions
    ADD CONSTRAINT subregions_pkey PRIMARY KEY (id);


--
-- Name: subregions_region_id_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX subregions_region_id_idx ON public.subregions USING btree (region_id);


--
-- Name: subregions subregions_region_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.subregions
    ADD CONSTRAINT subregions_region_id_fkey FOREIGN KEY (region_id) REFERENCES public.regions(id);


--
-- PostgreSQL database dump complete
--

